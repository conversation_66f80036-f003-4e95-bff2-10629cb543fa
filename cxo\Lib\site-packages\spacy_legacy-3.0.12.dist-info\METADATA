Metadata-Version: 2.1
Name: spacy-legacy
Version: 3.0.12
Summary: Legacy registered functions for spaCy backwards compatibility
Home-page: https://spacy.io
Author: Explosion
Author-email: <EMAIL>
License: MIT
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: Topic :: Scientific/Engineering
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: POSIX :: Linux
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: Microsoft :: Windows
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Requires-Python: >=3.6
Description-Content-Type: text/markdown
License-File: LICENSE

<a href="https://explosion.ai"><img src="https://explosion.ai/assets/img/logo.svg" width="125" height="125" align="right" /></a>

# spacy-legacy: Legacy functions and architectures for backwards compatibility

This package includes outdated registered functions for [spaCy](https://spacy.io) v3.x, for example model architectures, pipeline components and utilities. It's **installed automatically** as a dependency of spaCy, and allows us to provide backwards compatibility, while keeping the core library tidy and up to date. All of this happens under the hood, so you typically shouldn't have to care about this package.

[![Azure Pipelines](https://img.shields.io/azure-devops/build/explosion-ai/public/21/master.svg?logo=azure-pipelines&style=flat-square&label=build)](https://dev.azure.com/explosion-ai/public/_build?definitionId=21)
[![pypi Version](https://img.shields.io/pypi/v/spacy-legacy.svg?style=flat-square&logo=pypi&logoColor=white)](https://pypi.org/project/spacy-legacy/)


## How it works

Whenever a new backwards-incompatible version of a registered function is available, e.g. `spacy.Tok2Vec.v1` &rarr; `spacy.Tok2Vec.v2`, the legacy version is moved to `spacy-legacy`, and exposed via [entry points](setup.cfg). This means that it will still be available if your config files use it, even though the core library only includes the latest version.

Legacy functions are exposed with the prefix `spacy-legacy`, e.g. `spacy-legacy.Tok2Vec.v1`. When spaCy resolves your config and a function is not available in the core library, e.g. `spacy.Tok2Vec.v1`, it will check if there's a legacy function available and fall back to that. You can also explicitly refer to legacy functions in your config, to indicate that a newer version is available.
