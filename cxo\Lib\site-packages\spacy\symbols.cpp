/* Generated by Cython 0.29.35 */

/* BEGIN: Cython Metadata
{
    "distutils": {
        "extra_compile_args": [
            "-std=c++11"
        ],
        "include_dirs": [
            "C:\\hostedtoolcache\\windows\\Python\\3.11.4\\x64\\Lib\\site-packages\\numpy\\core\\include",
            "C:\\hostedtoolcache\\windows\\Python\\3.11.4\\x64\\include"
        ],
        "language": "c++",
        "name": "spacy.symbols",
        "sources": [
            "spacy/symbols.pyx"
        ]
    },
    "module_name": "spacy.symbols"
}
END: Cython Metadata */

#ifndef PY_SSIZE_T_CLEAN
#define PY_SSIZE_T_CLEAN
#endif /* PY_SSIZE_T_CLEAN */
#include "Python.h"
#ifndef Py_PYTHON_H
    #error Python headers needed to compile C extensions, please install development version of Python.
#elif PY_VERSION_HEX < 0x02060000 || (0x03000000 <= PY_VERSION_HEX && PY_VERSION_HEX < 0x03030000)
    #error Cython requires Python 2.6+ or Python 3.3+.
#else
#define CYTHON_ABI "0_29_35"
#define CYTHON_HEX_VERSION 0x001D23F0
#define CYTHON_FUTURE_DIVISION 0
#include <stddef.h>
#ifndef offsetof
  #define offsetof(type, member) ( (size_t) & ((type*)0) -> member )
#endif
#if !defined(WIN32) && !defined(MS_WINDOWS)
  #ifndef __stdcall
    #define __stdcall
  #endif
  #ifndef __cdecl
    #define __cdecl
  #endif
  #ifndef __fastcall
    #define __fastcall
  #endif
#endif
#ifndef DL_IMPORT
  #define DL_IMPORT(t) t
#endif
#ifndef DL_EXPORT
  #define DL_EXPORT(t) t
#endif
#define __PYX_COMMA ,
#ifndef HAVE_LONG_LONG
  #if PY_VERSION_HEX >= 0x02070000
    #define HAVE_LONG_LONG
  #endif
#endif
#ifndef PY_LONG_LONG
  #define PY_LONG_LONG LONG_LONG
#endif
#ifndef Py_HUGE_VAL
  #define Py_HUGE_VAL HUGE_VAL
#endif
#ifdef PYPY_VERSION
  #define CYTHON_COMPILING_IN_PYPY 1
  #define CYTHON_COMPILING_IN_PYSTON 0
  #define CYTHON_COMPILING_IN_CPYTHON 0
  #define CYTHON_COMPILING_IN_NOGIL 0
  #undef CYTHON_USE_TYPE_SLOTS
  #define CYTHON_USE_TYPE_SLOTS 0
  #undef CYTHON_USE_PYTYPE_LOOKUP
  #define CYTHON_USE_PYTYPE_LOOKUP 0
  #if PY_VERSION_HEX < 0x03050000
    #undef CYTHON_USE_ASYNC_SLOTS
    #define CYTHON_USE_ASYNC_SLOTS 0
  #elif !defined(CYTHON_USE_ASYNC_SLOTS)
    #define CYTHON_USE_ASYNC_SLOTS 1
  #endif
  #undef CYTHON_USE_PYLIST_INTERNALS
  #define CYTHON_USE_PYLIST_INTERNALS 0
  #undef CYTHON_USE_UNICODE_INTERNALS
  #define CYTHON_USE_UNICODE_INTERNALS 0
  #undef CYTHON_USE_UNICODE_WRITER
  #define CYTHON_USE_UNICODE_WRITER 0
  #undef CYTHON_USE_PYLONG_INTERNALS
  #define CYTHON_USE_PYLONG_INTERNALS 0
  #undef CYTHON_AVOID_BORROWED_REFS
  #define CYTHON_AVOID_BORROWED_REFS 1
  #undef CYTHON_ASSUME_SAFE_MACROS
  #define CYTHON_ASSUME_SAFE_MACROS 0
  #undef CYTHON_UNPACK_METHODS
  #define CYTHON_UNPACK_METHODS 0
  #undef CYTHON_FAST_THREAD_STATE
  #define CYTHON_FAST_THREAD_STATE 0
  #undef CYTHON_FAST_PYCALL
  #define CYTHON_FAST_PYCALL 0
  #if PY_VERSION_HEX < 0x03090000
    #undef CYTHON_PEP489_MULTI_PHASE_INIT
    #define CYTHON_PEP489_MULTI_PHASE_INIT 0
  #elif !defined(CYTHON_PEP489_MULTI_PHASE_INIT)
    #define CYTHON_PEP489_MULTI_PHASE_INIT 1
  #endif
  #undef CYTHON_USE_TP_FINALIZE
  #define CYTHON_USE_TP_FINALIZE 0
  #undef CYTHON_USE_DICT_VERSIONS
  #define CYTHON_USE_DICT_VERSIONS 0
  #undef CYTHON_USE_EXC_INFO_STACK
  #define CYTHON_USE_EXC_INFO_STACK 0
  #ifndef CYTHON_UPDATE_DESCRIPTOR_DOC
    #define CYTHON_UPDATE_DESCRIPTOR_DOC 0
  #endif
#elif defined(PYSTON_VERSION)
  #define CYTHON_COMPILING_IN_PYPY 0
  #define CYTHON_COMPILING_IN_PYSTON 1
  #define CYTHON_COMPILING_IN_CPYTHON 0
  #define CYTHON_COMPILING_IN_NOGIL 0
  #ifndef CYTHON_USE_TYPE_SLOTS
    #define CYTHON_USE_TYPE_SLOTS 1
  #endif
  #undef CYTHON_USE_PYTYPE_LOOKUP
  #define CYTHON_USE_PYTYPE_LOOKUP 0
  #undef CYTHON_USE_ASYNC_SLOTS
  #define CYTHON_USE_ASYNC_SLOTS 0
  #undef CYTHON_USE_PYLIST_INTERNALS
  #define CYTHON_USE_PYLIST_INTERNALS 0
  #ifndef CYTHON_USE_UNICODE_INTERNALS
    #define CYTHON_USE_UNICODE_INTERNALS 1
  #endif
  #undef CYTHON_USE_UNICODE_WRITER
  #define CYTHON_USE_UNICODE_WRITER 0
  #undef CYTHON_USE_PYLONG_INTERNALS
  #define CYTHON_USE_PYLONG_INTERNALS 0
  #ifndef CYTHON_AVOID_BORROWED_REFS
    #define CYTHON_AVOID_BORROWED_REFS 0
  #endif
  #ifndef CYTHON_ASSUME_SAFE_MACROS
    #define CYTHON_ASSUME_SAFE_MACROS 1
  #endif
  #ifndef CYTHON_UNPACK_METHODS
    #define CYTHON_UNPACK_METHODS 1
  #endif
  #undef CYTHON_FAST_THREAD_STATE
  #define CYTHON_FAST_THREAD_STATE 0
  #undef CYTHON_FAST_PYCALL
  #define CYTHON_FAST_PYCALL 0
  #undef CYTHON_PEP489_MULTI_PHASE_INIT
  #define CYTHON_PEP489_MULTI_PHASE_INIT 0
  #undef CYTHON_USE_TP_FINALIZE
  #define CYTHON_USE_TP_FINALIZE 0
  #undef CYTHON_USE_DICT_VERSIONS
  #define CYTHON_USE_DICT_VERSIONS 0
  #undef CYTHON_USE_EXC_INFO_STACK
  #define CYTHON_USE_EXC_INFO_STACK 0
  #ifndef CYTHON_UPDATE_DESCRIPTOR_DOC
    #define CYTHON_UPDATE_DESCRIPTOR_DOC 0
  #endif
#elif defined(PY_NOGIL)
  #define CYTHON_COMPILING_IN_PYPY 0
  #define CYTHON_COMPILING_IN_PYSTON 0
  #define CYTHON_COMPILING_IN_CPYTHON 0
  #define CYTHON_COMPILING_IN_NOGIL 1
  #ifndef CYTHON_USE_TYPE_SLOTS
    #define CYTHON_USE_TYPE_SLOTS 1
  #endif
  #undef CYTHON_USE_PYTYPE_LOOKUP
  #define CYTHON_USE_PYTYPE_LOOKUP 0
  #ifndef CYTHON_USE_ASYNC_SLOTS
    #define CYTHON_USE_ASYNC_SLOTS 1
  #endif
  #undef CYTHON_USE_PYLIST_INTERNALS
  #define CYTHON_USE_PYLIST_INTERNALS 0
  #ifndef CYTHON_USE_UNICODE_INTERNALS
    #define CYTHON_USE_UNICODE_INTERNALS 1
  #endif
  #undef CYTHON_USE_UNICODE_WRITER
  #define CYTHON_USE_UNICODE_WRITER 0
  #undef CYTHON_USE_PYLONG_INTERNALS
  #define CYTHON_USE_PYLONG_INTERNALS 0
  #ifndef CYTHON_AVOID_BORROWED_REFS
    #define CYTHON_AVOID_BORROWED_REFS 0
  #endif
  #ifndef CYTHON_ASSUME_SAFE_MACROS
    #define CYTHON_ASSUME_SAFE_MACROS 1
  #endif
  #ifndef CYTHON_UNPACK_METHODS
    #define CYTHON_UNPACK_METHODS 1
  #endif
  #undef CYTHON_FAST_THREAD_STATE
  #define CYTHON_FAST_THREAD_STATE 0
  #undef CYTHON_FAST_PYCALL
  #define CYTHON_FAST_PYCALL 0
  #ifndef CYTHON_PEP489_MULTI_PHASE_INIT
    #define CYTHON_PEP489_MULTI_PHASE_INIT 1
  #endif
  #ifndef CYTHON_USE_TP_FINALIZE
    #define CYTHON_USE_TP_FINALIZE 1
  #endif
  #undef CYTHON_USE_DICT_VERSIONS
  #define CYTHON_USE_DICT_VERSIONS 0
  #undef CYTHON_USE_EXC_INFO_STACK
  #define CYTHON_USE_EXC_INFO_STACK 0
#else
  #define CYTHON_COMPILING_IN_PYPY 0
  #define CYTHON_COMPILING_IN_PYSTON 0
  #define CYTHON_COMPILING_IN_CPYTHON 1
  #define CYTHON_COMPILING_IN_NOGIL 0
  #ifndef CYTHON_USE_TYPE_SLOTS
    #define CYTHON_USE_TYPE_SLOTS 1
  #endif
  #if PY_VERSION_HEX < 0x02070000
    #undef CYTHON_USE_PYTYPE_LOOKUP
    #define CYTHON_USE_PYTYPE_LOOKUP 0
  #elif !defined(CYTHON_USE_PYTYPE_LOOKUP)
    #define CYTHON_USE_PYTYPE_LOOKUP 1
  #endif
  #if PY_MAJOR_VERSION < 3
    #undef CYTHON_USE_ASYNC_SLOTS
    #define CYTHON_USE_ASYNC_SLOTS 0
  #elif !defined(CYTHON_USE_ASYNC_SLOTS)
    #define CYTHON_USE_ASYNC_SLOTS 1
  #endif
  #if PY_VERSION_HEX < 0x02070000
    #undef CYTHON_USE_PYLONG_INTERNALS
    #define CYTHON_USE_PYLONG_INTERNALS 0
  #elif !defined(CYTHON_USE_PYLONG_INTERNALS)
    #define CYTHON_USE_PYLONG_INTERNALS (PY_VERSION_HEX < 0x030C00A5)
  #endif
  #ifndef CYTHON_USE_PYLIST_INTERNALS
    #define CYTHON_USE_PYLIST_INTERNALS 1
  #endif
  #ifndef CYTHON_USE_UNICODE_INTERNALS
    #define CYTHON_USE_UNICODE_INTERNALS 1
  #endif
  #if PY_VERSION_HEX < 0x030300F0 || PY_VERSION_HEX >= 0x030B00A2
    #undef CYTHON_USE_UNICODE_WRITER
    #define CYTHON_USE_UNICODE_WRITER 0
  #elif !defined(CYTHON_USE_UNICODE_WRITER)
    #define CYTHON_USE_UNICODE_WRITER 1
  #endif
  #ifndef CYTHON_AVOID_BORROWED_REFS
    #define CYTHON_AVOID_BORROWED_REFS 0
  #endif
  #ifndef CYTHON_ASSUME_SAFE_MACROS
    #define CYTHON_ASSUME_SAFE_MACROS 1
  #endif
  #ifndef CYTHON_UNPACK_METHODS
    #define CYTHON_UNPACK_METHODS 1
  #endif
  #if PY_VERSION_HEX >= 0x030B00A4
    #undef CYTHON_FAST_THREAD_STATE
    #define CYTHON_FAST_THREAD_STATE 0
  #elif !defined(CYTHON_FAST_THREAD_STATE)
    #define CYTHON_FAST_THREAD_STATE 1
  #endif
  #ifndef CYTHON_FAST_PYCALL
    #define CYTHON_FAST_PYCALL (PY_VERSION_HEX < 0x030A0000)
  #endif
  #ifndef CYTHON_PEP489_MULTI_PHASE_INIT
    #define CYTHON_PEP489_MULTI_PHASE_INIT (PY_VERSION_HEX >= 0x03050000)
  #endif
  #ifndef CYTHON_USE_TP_FINALIZE
    #define CYTHON_USE_TP_FINALIZE (PY_VERSION_HEX >= 0x030400a1)
  #endif
  #ifndef CYTHON_USE_DICT_VERSIONS
    #define CYTHON_USE_DICT_VERSIONS ((PY_VERSION_HEX >= 0x030600B1) && (PY_VERSION_HEX < 0x030C00A5))
  #endif
  #if PY_VERSION_HEX >= 0x030B00A4
    #undef CYTHON_USE_EXC_INFO_STACK
    #define CYTHON_USE_EXC_INFO_STACK 0
  #elif !defined(CYTHON_USE_EXC_INFO_STACK)
    #define CYTHON_USE_EXC_INFO_STACK (PY_VERSION_HEX >= 0x030700A3)
  #endif
  #ifndef CYTHON_UPDATE_DESCRIPTOR_DOC
    #define CYTHON_UPDATE_DESCRIPTOR_DOC 1
  #endif
#endif
#if !defined(CYTHON_FAST_PYCCALL)
#define CYTHON_FAST_PYCCALL  (CYTHON_FAST_PYCALL && PY_VERSION_HEX >= 0x030600B1)
#endif
#if CYTHON_USE_PYLONG_INTERNALS
  #if PY_MAJOR_VERSION < 3
    #include "longintrepr.h"
  #endif
  #undef SHIFT
  #undef BASE
  #undef MASK
  #ifdef SIZEOF_VOID_P
    enum { __pyx_check_sizeof_voidp = 1 / (int)(SIZEOF_VOID_P == sizeof(void*)) };
  #endif
#endif
#ifndef __has_attribute
  #define __has_attribute(x) 0
#endif
#ifndef __has_cpp_attribute
  #define __has_cpp_attribute(x) 0
#endif
#ifndef CYTHON_RESTRICT
  #if defined(__GNUC__)
    #define CYTHON_RESTRICT __restrict__
  #elif defined(_MSC_VER) && _MSC_VER >= 1400
    #define CYTHON_RESTRICT __restrict
  #elif defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L
    #define CYTHON_RESTRICT restrict
  #else
    #define CYTHON_RESTRICT
  #endif
#endif
#ifndef CYTHON_UNUSED
# if defined(__GNUC__)
#   if !(defined(__cplusplus)) || (__GNUC__ > 3 || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4))
#     define CYTHON_UNUSED __attribute__ ((__unused__))
#   else
#     define CYTHON_UNUSED
#   endif
# elif defined(__ICC) || (defined(__INTEL_COMPILER) && !defined(_MSC_VER))
#   define CYTHON_UNUSED __attribute__ ((__unused__))
# else
#   define CYTHON_UNUSED
# endif
#endif
#ifndef CYTHON_MAYBE_UNUSED_VAR
#  if defined(__cplusplus)
     template<class T> void CYTHON_MAYBE_UNUSED_VAR( const T& ) { }
#  else
#    define CYTHON_MAYBE_UNUSED_VAR(x) (void)(x)
#  endif
#endif
#ifndef CYTHON_NCP_UNUSED
# if CYTHON_COMPILING_IN_CPYTHON
#  define CYTHON_NCP_UNUSED
# else
#  define CYTHON_NCP_UNUSED CYTHON_UNUSED
# endif
#endif
#define __Pyx_void_to_None(void_result) ((void)(void_result), Py_INCREF(Py_None), Py_None)
#ifdef _MSC_VER
    #ifndef _MSC_STDINT_H_
        #if _MSC_VER < 1300
           typedef unsigned char     uint8_t;
           typedef unsigned int      uint32_t;
        #else
           typedef unsigned __int8   uint8_t;
           typedef unsigned __int32  uint32_t;
        #endif
    #endif
#else
   #include <stdint.h>
#endif
#ifndef CYTHON_FALLTHROUGH
  #if defined(__cplusplus) && __cplusplus >= 201103L
    #if __has_cpp_attribute(fallthrough)
      #define CYTHON_FALLTHROUGH [[fallthrough]]
    #elif __has_cpp_attribute(clang::fallthrough)
      #define CYTHON_FALLTHROUGH [[clang::fallthrough]]
    #elif __has_cpp_attribute(gnu::fallthrough)
      #define CYTHON_FALLTHROUGH [[gnu::fallthrough]]
    #endif
  #endif
  #ifndef CYTHON_FALLTHROUGH
    #if __has_attribute(fallthrough)
      #define CYTHON_FALLTHROUGH __attribute__((fallthrough))
    #else
      #define CYTHON_FALLTHROUGH
    #endif
  #endif
  #if defined(__clang__ ) && defined(__apple_build_version__)
    #if __apple_build_version__ < 7000000
      #undef  CYTHON_FALLTHROUGH
      #define CYTHON_FALLTHROUGH
    #endif
  #endif
#endif

#ifndef __cplusplus
  #error "Cython files generated with the C++ option must be compiled with a C++ compiler."
#endif
#ifndef CYTHON_INLINE
  #if defined(__clang__)
    #define CYTHON_INLINE __inline__ __attribute__ ((__unused__))
  #else
    #define CYTHON_INLINE inline
  #endif
#endif
template<typename T>
void __Pyx_call_destructor(T& x) {
    x.~T();
}
template<typename T>
class __Pyx_FakeReference {
  public:
    __Pyx_FakeReference() : ptr(NULL) { }
    __Pyx_FakeReference(const T& ref) : ptr(const_cast<T*>(&ref)) { }
    T *operator->() { return ptr; }
    T *operator&() { return ptr; }
    operator T&() { return *ptr; }
    template<typename U> bool operator ==(U other) { return *ptr == other; }
    template<typename U> bool operator !=(U other) { return *ptr != other; }
  private:
    T *ptr;
};

#if CYTHON_COMPILING_IN_PYPY && PY_VERSION_HEX < 0x02070600 && !defined(Py_OptimizeFlag)
  #define Py_OptimizeFlag 0
#endif
#define __PYX_BUILD_PY_SSIZE_T "n"
#define CYTHON_FORMAT_SSIZE_T "z"
#if PY_MAJOR_VERSION < 3
  #define __Pyx_BUILTIN_MODULE_NAME "__builtin__"
  #define __Pyx_PyCode_New(a, k, l, s, f, code, c, n, v, fv, cell, fn, name, fline, lnos)\
          PyCode_New(a+k, l, s, f, code, c, n, v, fv, cell, fn, name, fline, lnos)
  #define __Pyx_DefaultClassType PyClass_Type
#else
  #define __Pyx_BUILTIN_MODULE_NAME "builtins"
  #define __Pyx_DefaultClassType PyType_Type
#if PY_VERSION_HEX >= 0x030B00A1
    static CYTHON_INLINE PyCodeObject* __Pyx_PyCode_New(int a, int k, int l, int s, int f,
                                                    PyObject *code, PyObject *c, PyObject* n, PyObject *v,
                                                    PyObject *fv, PyObject *cell, PyObject* fn,
                                                    PyObject *name, int fline, PyObject *lnos) {
        PyObject *kwds=NULL, *argcount=NULL, *posonlyargcount=NULL, *kwonlyargcount=NULL;
        PyObject *nlocals=NULL, *stacksize=NULL, *flags=NULL, *replace=NULL, *call_result=NULL, *empty=NULL;
        const char *fn_cstr=NULL;
        const char *name_cstr=NULL;
        PyCodeObject* co=NULL;
        PyObject *type, *value, *traceback;
        PyErr_Fetch(&type, &value, &traceback);
        if (!(kwds=PyDict_New())) goto end;
        if (!(argcount=PyLong_FromLong(a))) goto end;
        if (PyDict_SetItemString(kwds, "co_argcount", argcount) != 0) goto end;
        if (!(posonlyargcount=PyLong_FromLong(0))) goto end;
        if (PyDict_SetItemString(kwds, "co_posonlyargcount", posonlyargcount) != 0) goto end;
        if (!(kwonlyargcount=PyLong_FromLong(k))) goto end;
        if (PyDict_SetItemString(kwds, "co_kwonlyargcount", kwonlyargcount) != 0) goto end;
        if (!(nlocals=PyLong_FromLong(l))) goto end;
        if (PyDict_SetItemString(kwds, "co_nlocals", nlocals) != 0) goto end;
        if (!(stacksize=PyLong_FromLong(s))) goto end;
        if (PyDict_SetItemString(kwds, "co_stacksize", stacksize) != 0) goto end;
        if (!(flags=PyLong_FromLong(f))) goto end;
        if (PyDict_SetItemString(kwds, "co_flags", flags) != 0) goto end;
        if (PyDict_SetItemString(kwds, "co_code", code) != 0) goto end;
        if (PyDict_SetItemString(kwds, "co_consts", c) != 0) goto end;
        if (PyDict_SetItemString(kwds, "co_names", n) != 0) goto end;
        if (PyDict_SetItemString(kwds, "co_varnames", v) != 0) goto end;
        if (PyDict_SetItemString(kwds, "co_freevars", fv) != 0) goto end;
        if (PyDict_SetItemString(kwds, "co_cellvars", cell) != 0) goto end;
        if (PyDict_SetItemString(kwds, "co_linetable", lnos) != 0) goto end;
        if (!(fn_cstr=PyUnicode_AsUTF8AndSize(fn, NULL))) goto end;
        if (!(name_cstr=PyUnicode_AsUTF8AndSize(name, NULL))) goto end;
        if (!(co = PyCode_NewEmpty(fn_cstr, name_cstr, fline))) goto end;
        if (!(replace = PyObject_GetAttrString((PyObject*)co, "replace"))) goto cleanup_code_too;
        if (!(empty = PyTuple_New(0))) goto cleanup_code_too; // unfortunately __pyx_empty_tuple isn't available here
        if (!(call_result = PyObject_Call(replace, empty, kwds))) goto cleanup_code_too;
        Py_XDECREF((PyObject*)co);
        co = (PyCodeObject*)call_result;
        call_result = NULL;
        if (0) {
            cleanup_code_too:
            Py_XDECREF((PyObject*)co);
            co = NULL;
        }
        end:
        Py_XDECREF(kwds);
        Py_XDECREF(argcount);
        Py_XDECREF(posonlyargcount);
        Py_XDECREF(kwonlyargcount);
        Py_XDECREF(nlocals);
        Py_XDECREF(stacksize);
        Py_XDECREF(replace);
        Py_XDECREF(call_result);
        Py_XDECREF(empty);
        if (type) {
            PyErr_Restore(type, value, traceback);
        }
        return co;
    }
#else
  #define __Pyx_PyCode_New(a, k, l, s, f, code, c, n, v, fv, cell, fn, name, fline, lnos)\
          PyCode_New(a, k, l, s, f, code, c, n, v, fv, cell, fn, name, fline, lnos)
#endif
  #define __Pyx_DefaultClassType PyType_Type
#endif
#ifndef Py_TPFLAGS_CHECKTYPES
  #define Py_TPFLAGS_CHECKTYPES 0
#endif
#ifndef Py_TPFLAGS_HAVE_INDEX
  #define Py_TPFLAGS_HAVE_INDEX 0
#endif
#ifndef Py_TPFLAGS_HAVE_NEWBUFFER
  #define Py_TPFLAGS_HAVE_NEWBUFFER 0
#endif
#ifndef Py_TPFLAGS_HAVE_FINALIZE
  #define Py_TPFLAGS_HAVE_FINALIZE 0
#endif
#ifndef METH_STACKLESS
  #define METH_STACKLESS 0
#endif
#if PY_VERSION_HEX <= 0x030700A3 || !defined(METH_FASTCALL)
  #ifndef METH_FASTCALL
     #define METH_FASTCALL 0x80
  #endif
  typedef PyObject *(*__Pyx_PyCFunctionFast) (PyObject *self, PyObject *const *args, Py_ssize_t nargs);
  typedef PyObject *(*__Pyx_PyCFunctionFastWithKeywords) (PyObject *self, PyObject *const *args,
                                                          Py_ssize_t nargs, PyObject *kwnames);
#else
  #define __Pyx_PyCFunctionFast _PyCFunctionFast
  #define __Pyx_PyCFunctionFastWithKeywords _PyCFunctionFastWithKeywords
#endif
#if CYTHON_FAST_PYCCALL
#define __Pyx_PyFastCFunction_Check(func)\
    ((PyCFunction_Check(func) && (METH_FASTCALL == (PyCFunction_GET_FLAGS(func) & ~(METH_CLASS | METH_STATIC | METH_COEXIST | METH_KEYWORDS | METH_STACKLESS)))))
#else
#define __Pyx_PyFastCFunction_Check(func) 0
#endif
#if CYTHON_COMPILING_IN_PYPY && !defined(PyObject_Malloc)
  #define PyObject_Malloc(s)   PyMem_Malloc(s)
  #define PyObject_Free(p)     PyMem_Free(p)
  #define PyObject_Realloc(p)  PyMem_Realloc(p)
#endif
#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX < 0x030400A1
  #define PyMem_RawMalloc(n)           PyMem_Malloc(n)
  #define PyMem_RawRealloc(p, n)       PyMem_Realloc(p, n)
  #define PyMem_RawFree(p)             PyMem_Free(p)
#endif
#if CYTHON_COMPILING_IN_PYSTON
  #define __Pyx_PyCode_HasFreeVars(co)  PyCode_HasFreeVars(co)
  #define __Pyx_PyFrame_SetLineNumber(frame, lineno) PyFrame_SetLineNumber(frame, lineno)
#else
  #define __Pyx_PyCode_HasFreeVars(co)  (PyCode_GetNumFree(co) > 0)
  #define __Pyx_PyFrame_SetLineNumber(frame, lineno)  (frame)->f_lineno = (lineno)
#endif
#if !CYTHON_FAST_THREAD_STATE || PY_VERSION_HEX < 0x02070000
  #define __Pyx_PyThreadState_Current PyThreadState_GET()
#elif PY_VERSION_HEX >= 0x03060000
  #define __Pyx_PyThreadState_Current _PyThreadState_UncheckedGet()
#elif PY_VERSION_HEX >= 0x03000000
  #define __Pyx_PyThreadState_Current PyThreadState_GET()
#else
  #define __Pyx_PyThreadState_Current _PyThreadState_Current
#endif
#if PY_VERSION_HEX < 0x030700A2 && !defined(PyThread_tss_create) && !defined(Py_tss_NEEDS_INIT)
#include "pythread.h"
#define Py_tss_NEEDS_INIT 0
typedef int Py_tss_t;
static CYTHON_INLINE int PyThread_tss_create(Py_tss_t *key) {
  *key = PyThread_create_key();
  return 0;
}
static CYTHON_INLINE Py_tss_t * PyThread_tss_alloc(void) {
  Py_tss_t *key = (Py_tss_t *)PyObject_Malloc(sizeof(Py_tss_t));
  *key = Py_tss_NEEDS_INIT;
  return key;
}
static CYTHON_INLINE void PyThread_tss_free(Py_tss_t *key) {
  PyObject_Free(key);
}
static CYTHON_INLINE int PyThread_tss_is_created(Py_tss_t *key) {
  return *key != Py_tss_NEEDS_INIT;
}
static CYTHON_INLINE void PyThread_tss_delete(Py_tss_t *key) {
  PyThread_delete_key(*key);
  *key = Py_tss_NEEDS_INIT;
}
static CYTHON_INLINE int PyThread_tss_set(Py_tss_t *key, void *value) {
  return PyThread_set_key_value(*key, value);
}
static CYTHON_INLINE void * PyThread_tss_get(Py_tss_t *key) {
  return PyThread_get_key_value(*key);
}
#endif
#if CYTHON_COMPILING_IN_CPYTHON || defined(_PyDict_NewPresized)
#define __Pyx_PyDict_NewPresized(n)  ((n <= 8) ? PyDict_New() : _PyDict_NewPresized(n))
#else
#define __Pyx_PyDict_NewPresized(n)  PyDict_New()
#endif
#if PY_MAJOR_VERSION >= 3 || CYTHON_FUTURE_DIVISION
  #define __Pyx_PyNumber_Divide(x,y)         PyNumber_TrueDivide(x,y)
  #define __Pyx_PyNumber_InPlaceDivide(x,y)  PyNumber_InPlaceTrueDivide(x,y)
#else
  #define __Pyx_PyNumber_Divide(x,y)         PyNumber_Divide(x,y)
  #define __Pyx_PyNumber_InPlaceDivide(x,y)  PyNumber_InPlaceDivide(x,y)
#endif
#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX >= 0x030500A1 && CYTHON_USE_UNICODE_INTERNALS
#define __Pyx_PyDict_GetItemStr(dict, name)  _PyDict_GetItem_KnownHash(dict, name, ((PyASCIIObject *) name)->hash)
#else
#define __Pyx_PyDict_GetItemStr(dict, name)  PyDict_GetItem(dict, name)
#endif
#if PY_VERSION_HEX > 0x03030000 && defined(PyUnicode_KIND)
  #define CYTHON_PEP393_ENABLED 1
  #if PY_VERSION_HEX >= 0x030C0000
    #define __Pyx_PyUnicode_READY(op)       (0)
  #else
    #define __Pyx_PyUnicode_READY(op)       (likely(PyUnicode_IS_READY(op)) ?\
                                                0 : _PyUnicode_Ready((PyObject *)(op)))
  #endif
  #define __Pyx_PyUnicode_GET_LENGTH(u)   PyUnicode_GET_LENGTH(u)
  #define __Pyx_PyUnicode_READ_CHAR(u, i) PyUnicode_READ_CHAR(u, i)
  #define __Pyx_PyUnicode_MAX_CHAR_VALUE(u)   PyUnicode_MAX_CHAR_VALUE(u)
  #define __Pyx_PyUnicode_KIND(u)         PyUnicode_KIND(u)
  #define __Pyx_PyUnicode_DATA(u)         PyUnicode_DATA(u)
  #define __Pyx_PyUnicode_READ(k, d, i)   PyUnicode_READ(k, d, i)
  #define __Pyx_PyUnicode_WRITE(k, d, i, ch)  PyUnicode_WRITE(k, d, i, ch)
  #if PY_VERSION_HEX >= 0x030C0000
    #define __Pyx_PyUnicode_IS_TRUE(u)      (0 != PyUnicode_GET_LENGTH(u))
  #else
    #if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX >= 0x03090000
    #define __Pyx_PyUnicode_IS_TRUE(u)      (0 != (likely(PyUnicode_IS_READY(u)) ? PyUnicode_GET_LENGTH(u) : ((PyCompactUnicodeObject *)(u))->wstr_length))
    #else
    #define __Pyx_PyUnicode_IS_TRUE(u)      (0 != (likely(PyUnicode_IS_READY(u)) ? PyUnicode_GET_LENGTH(u) : PyUnicode_GET_SIZE(u)))
    #endif
  #endif
#else
  #define CYTHON_PEP393_ENABLED 0
  #define PyUnicode_1BYTE_KIND  1
  #define PyUnicode_2BYTE_KIND  2
  #define PyUnicode_4BYTE_KIND  4
  #define __Pyx_PyUnicode_READY(op)       (0)
  #define __Pyx_PyUnicode_GET_LENGTH(u)   PyUnicode_GET_SIZE(u)
  #define __Pyx_PyUnicode_READ_CHAR(u, i) ((Py_UCS4)(PyUnicode_AS_UNICODE(u)[i]))
  #define __Pyx_PyUnicode_MAX_CHAR_VALUE(u)   ((sizeof(Py_UNICODE) == 2) ? 65535 : 1114111)
  #define __Pyx_PyUnicode_KIND(u)         (sizeof(Py_UNICODE))
  #define __Pyx_PyUnicode_DATA(u)         ((void*)PyUnicode_AS_UNICODE(u))
  #define __Pyx_PyUnicode_READ(k, d, i)   ((void)(k), (Py_UCS4)(((Py_UNICODE*)d)[i]))
  #define __Pyx_PyUnicode_WRITE(k, d, i, ch)  (((void)(k)), ((Py_UNICODE*)d)[i] = ch)
  #define __Pyx_PyUnicode_IS_TRUE(u)      (0 != PyUnicode_GET_SIZE(u))
#endif
#if CYTHON_COMPILING_IN_PYPY
  #define __Pyx_PyUnicode_Concat(a, b)      PyNumber_Add(a, b)
  #define __Pyx_PyUnicode_ConcatSafe(a, b)  PyNumber_Add(a, b)
#else
  #define __Pyx_PyUnicode_Concat(a, b)      PyUnicode_Concat(a, b)
  #define __Pyx_PyUnicode_ConcatSafe(a, b)  ((unlikely((a) == Py_None) || unlikely((b) == Py_None)) ?\
      PyNumber_Add(a, b) : __Pyx_PyUnicode_Concat(a, b))
#endif
#if CYTHON_COMPILING_IN_PYPY && !defined(PyUnicode_Contains)
  #define PyUnicode_Contains(u, s)  PySequence_Contains(u, s)
#endif
#if CYTHON_COMPILING_IN_PYPY && !defined(PyByteArray_Check)
  #define PyByteArray_Check(obj)  PyObject_TypeCheck(obj, &PyByteArray_Type)
#endif
#if CYTHON_COMPILING_IN_PYPY && !defined(PyObject_Format)
  #define PyObject_Format(obj, fmt)  PyObject_CallMethod(obj, "__format__", "O", fmt)
#endif
#define __Pyx_PyString_FormatSafe(a, b)   ((unlikely((a) == Py_None || (PyString_Check(b) && !PyString_CheckExact(b)))) ? PyNumber_Remainder(a, b) : __Pyx_PyString_Format(a, b))
#define __Pyx_PyUnicode_FormatSafe(a, b)  ((unlikely((a) == Py_None || (PyUnicode_Check(b) && !PyUnicode_CheckExact(b)))) ? PyNumber_Remainder(a, b) : PyUnicode_Format(a, b))
#if PY_MAJOR_VERSION >= 3
  #define __Pyx_PyString_Format(a, b)  PyUnicode_Format(a, b)
#else
  #define __Pyx_PyString_Format(a, b)  PyString_Format(a, b)
#endif
#if PY_MAJOR_VERSION < 3 && !defined(PyObject_ASCII)
  #define PyObject_ASCII(o)            PyObject_Repr(o)
#endif
#if PY_MAJOR_VERSION >= 3
  #define PyBaseString_Type            PyUnicode_Type
  #define PyStringObject               PyUnicodeObject
  #define PyString_Type                PyUnicode_Type
  #define PyString_Check               PyUnicode_Check
  #define PyString_CheckExact          PyUnicode_CheckExact
#ifndef PyObject_Unicode
  #define PyObject_Unicode             PyObject_Str
#endif
#endif
#if PY_MAJOR_VERSION >= 3
  #define __Pyx_PyBaseString_Check(obj) PyUnicode_Check(obj)
  #define __Pyx_PyBaseString_CheckExact(obj) PyUnicode_CheckExact(obj)
#else
  #define __Pyx_PyBaseString_Check(obj) (PyString_Check(obj) || PyUnicode_Check(obj))
  #define __Pyx_PyBaseString_CheckExact(obj) (PyString_CheckExact(obj) || PyUnicode_CheckExact(obj))
#endif
#ifndef PySet_CheckExact
  #define PySet_CheckExact(obj)        (Py_TYPE(obj) == &PySet_Type)
#endif
#if PY_VERSION_HEX >= 0x030900A4
  #define __Pyx_SET_REFCNT(obj, refcnt) Py_SET_REFCNT(obj, refcnt)
  #define __Pyx_SET_SIZE(obj, size) Py_SET_SIZE(obj, size)
#else
  #define __Pyx_SET_REFCNT(obj, refcnt) Py_REFCNT(obj) = (refcnt)
  #define __Pyx_SET_SIZE(obj, size) Py_SIZE(obj) = (size)
#endif
#if CYTHON_ASSUME_SAFE_MACROS
  #define __Pyx_PySequence_SIZE(seq)  Py_SIZE(seq)
#else
  #define __Pyx_PySequence_SIZE(seq)  PySequence_Size(seq)
#endif
#if PY_MAJOR_VERSION >= 3
  #define PyIntObject                  PyLongObject
  #define PyInt_Type                   PyLong_Type
  #define PyInt_Check(op)              PyLong_Check(op)
  #define PyInt_CheckExact(op)         PyLong_CheckExact(op)
  #define PyInt_FromString             PyLong_FromString
  #define PyInt_FromUnicode            PyLong_FromUnicode
  #define PyInt_FromLong               PyLong_FromLong
  #define PyInt_FromSize_t             PyLong_FromSize_t
  #define PyInt_FromSsize_t            PyLong_FromSsize_t
  #define PyInt_AsLong                 PyLong_AsLong
  #define PyInt_AS_LONG                PyLong_AS_LONG
  #define PyInt_AsSsize_t              PyLong_AsSsize_t
  #define PyInt_AsUnsignedLongMask     PyLong_AsUnsignedLongMask
  #define PyInt_AsUnsignedLongLongMask PyLong_AsUnsignedLongLongMask
  #define PyNumber_Int                 PyNumber_Long
#endif
#if PY_MAJOR_VERSION >= 3
  #define PyBoolObject                 PyLongObject
#endif
#if PY_MAJOR_VERSION >= 3 && CYTHON_COMPILING_IN_PYPY
  #ifndef PyUnicode_InternFromString
    #define PyUnicode_InternFromString(s) PyUnicode_FromString(s)
  #endif
#endif
#if PY_VERSION_HEX < 0x030200A4
  typedef long Py_hash_t;
  #define __Pyx_PyInt_FromHash_t PyInt_FromLong
  #define __Pyx_PyInt_AsHash_t   __Pyx_PyIndex_AsHash_t
#else
  #define __Pyx_PyInt_FromHash_t PyInt_FromSsize_t
  #define __Pyx_PyInt_AsHash_t   __Pyx_PyIndex_AsSsize_t
#endif
#if PY_MAJOR_VERSION >= 3
  #define __Pyx_PyMethod_New(func, self, klass) ((self) ? ((void)(klass), PyMethod_New(func, self)) : __Pyx_NewRef(func))
#else
  #define __Pyx_PyMethod_New(func, self, klass) PyMethod_New(func, self, klass)
#endif
#if CYTHON_USE_ASYNC_SLOTS
  #if PY_VERSION_HEX >= 0x030500B1
    #define __Pyx_PyAsyncMethodsStruct PyAsyncMethods
    #define __Pyx_PyType_AsAsync(obj) (Py_TYPE(obj)->tp_as_async)
  #else
    #define __Pyx_PyType_AsAsync(obj) ((__Pyx_PyAsyncMethodsStruct*) (Py_TYPE(obj)->tp_reserved))
  #endif
#else
  #define __Pyx_PyType_AsAsync(obj) NULL
#endif
#ifndef __Pyx_PyAsyncMethodsStruct
    typedef struct {
        unaryfunc am_await;
        unaryfunc am_aiter;
        unaryfunc am_anext;
    } __Pyx_PyAsyncMethodsStruct;
#endif

#if defined(_WIN32) || defined(WIN32) || defined(MS_WINDOWS)
  #if !defined(_USE_MATH_DEFINES)
    #define _USE_MATH_DEFINES
  #endif
#endif
#include <math.h>
#ifdef NAN
#define __PYX_NAN() ((float) NAN)
#else
static CYTHON_INLINE float __PYX_NAN() {
  float value;
  memset(&value, 0xFF, sizeof(value));
  return value;
}
#endif
#if defined(__CYGWIN__) && defined(_LDBL_EQ_DBL)
#define __Pyx_truncl trunc
#else
#define __Pyx_truncl truncl
#endif

#define __PYX_MARK_ERR_POS(f_index, lineno) \
    { __pyx_filename = __pyx_f[f_index]; (void)__pyx_filename; __pyx_lineno = lineno; (void)__pyx_lineno; __pyx_clineno = __LINE__; (void)__pyx_clineno; }
#define __PYX_ERR(f_index, lineno, Ln_error) \
    { __PYX_MARK_ERR_POS(f_index, lineno) goto Ln_error; }

#ifndef __PYX_EXTERN_C
  #ifdef __cplusplus
    #define __PYX_EXTERN_C extern "C"
  #else
    #define __PYX_EXTERN_C extern
  #endif
#endif

#define __PYX_HAVE__spacy__symbols
#define __PYX_HAVE_API__spacy__symbols
/* Early includes */
#ifdef _OPENMP
#include <omp.h>
#endif /* _OPENMP */

#if defined(PYREX_WITHOUT_ASSERTIONS) && !defined(CYTHON_WITHOUT_ASSERTIONS)
#define CYTHON_WITHOUT_ASSERTIONS
#endif

typedef struct {PyObject **p; const char *s; const Py_ssize_t n; const char* encoding;
                const char is_unicode; const char is_str; const char intern; } __Pyx_StringTabEntry;

#define __PYX_DEFAULT_STRING_ENCODING_IS_ASCII 0
#define __PYX_DEFAULT_STRING_ENCODING_IS_UTF8 0
#define __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT (PY_MAJOR_VERSION >= 3 && __PYX_DEFAULT_STRING_ENCODING_IS_UTF8)
#define __PYX_DEFAULT_STRING_ENCODING ""
#define __Pyx_PyObject_FromString __Pyx_PyBytes_FromString
#define __Pyx_PyObject_FromStringAndSize __Pyx_PyBytes_FromStringAndSize
#define __Pyx_uchar_cast(c) ((unsigned char)c)
#define __Pyx_long_cast(x) ((long)x)
#define __Pyx_fits_Py_ssize_t(v, type, is_signed)  (\
    (sizeof(type) < sizeof(Py_ssize_t))  ||\
    (sizeof(type) > sizeof(Py_ssize_t) &&\
          likely(v < (type)PY_SSIZE_T_MAX ||\
                 v == (type)PY_SSIZE_T_MAX)  &&\
          (!is_signed || likely(v > (type)PY_SSIZE_T_MIN ||\
                                v == (type)PY_SSIZE_T_MIN)))  ||\
    (sizeof(type) == sizeof(Py_ssize_t) &&\
          (is_signed || likely(v < (type)PY_SSIZE_T_MAX ||\
                               v == (type)PY_SSIZE_T_MAX)))  )
static CYTHON_INLINE int __Pyx_is_valid_index(Py_ssize_t i, Py_ssize_t limit) {
    return (size_t) i < (size_t) limit;
}
#if defined (__cplusplus) && __cplusplus >= 201103L
    #include <cstdlib>
    #define __Pyx_sst_abs(value) std::abs(value)
#elif SIZEOF_INT >= SIZEOF_SIZE_T
    #define __Pyx_sst_abs(value) abs(value)
#elif SIZEOF_LONG >= SIZEOF_SIZE_T
    #define __Pyx_sst_abs(value) labs(value)
#elif defined (_MSC_VER)
    #define __Pyx_sst_abs(value) ((Py_ssize_t)_abs64(value))
#elif defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L
    #define __Pyx_sst_abs(value) llabs(value)
#elif defined (__GNUC__)
    #define __Pyx_sst_abs(value) __builtin_llabs(value)
#else
    #define __Pyx_sst_abs(value) ((value<0) ? -value : value)
#endif
static CYTHON_INLINE const char* __Pyx_PyObject_AsString(PyObject*);
static CYTHON_INLINE const char* __Pyx_PyObject_AsStringAndSize(PyObject*, Py_ssize_t* length);
#define __Pyx_PyByteArray_FromString(s) PyByteArray_FromStringAndSize((const char*)s, strlen((const char*)s))
#define __Pyx_PyByteArray_FromStringAndSize(s, l) PyByteArray_FromStringAndSize((const char*)s, l)
#define __Pyx_PyBytes_FromString        PyBytes_FromString
#define __Pyx_PyBytes_FromStringAndSize PyBytes_FromStringAndSize
static CYTHON_INLINE PyObject* __Pyx_PyUnicode_FromString(const char*);
#if PY_MAJOR_VERSION < 3
    #define __Pyx_PyStr_FromString        __Pyx_PyBytes_FromString
    #define __Pyx_PyStr_FromStringAndSize __Pyx_PyBytes_FromStringAndSize
#else
    #define __Pyx_PyStr_FromString        __Pyx_PyUnicode_FromString
    #define __Pyx_PyStr_FromStringAndSize __Pyx_PyUnicode_FromStringAndSize
#endif
#define __Pyx_PyBytes_AsWritableString(s)     ((char*) PyBytes_AS_STRING(s))
#define __Pyx_PyBytes_AsWritableSString(s)    ((signed char*) PyBytes_AS_STRING(s))
#define __Pyx_PyBytes_AsWritableUString(s)    ((unsigned char*) PyBytes_AS_STRING(s))
#define __Pyx_PyBytes_AsString(s)     ((const char*) PyBytes_AS_STRING(s))
#define __Pyx_PyBytes_AsSString(s)    ((const signed char*) PyBytes_AS_STRING(s))
#define __Pyx_PyBytes_AsUString(s)    ((const unsigned char*) PyBytes_AS_STRING(s))
#define __Pyx_PyObject_AsWritableString(s)    ((char*) __Pyx_PyObject_AsString(s))
#define __Pyx_PyObject_AsWritableSString(s)    ((signed char*) __Pyx_PyObject_AsString(s))
#define __Pyx_PyObject_AsWritableUString(s)    ((unsigned char*) __Pyx_PyObject_AsString(s))
#define __Pyx_PyObject_AsSString(s)    ((const signed char*) __Pyx_PyObject_AsString(s))
#define __Pyx_PyObject_AsUString(s)    ((const unsigned char*) __Pyx_PyObject_AsString(s))
#define __Pyx_PyObject_FromCString(s)  __Pyx_PyObject_FromString((const char*)s)
#define __Pyx_PyBytes_FromCString(s)   __Pyx_PyBytes_FromString((const char*)s)
#define __Pyx_PyByteArray_FromCString(s)   __Pyx_PyByteArray_FromString((const char*)s)
#define __Pyx_PyStr_FromCString(s)     __Pyx_PyStr_FromString((const char*)s)
#define __Pyx_PyUnicode_FromCString(s) __Pyx_PyUnicode_FromString((const char*)s)
static CYTHON_INLINE size_t __Pyx_Py_UNICODE_strlen(const Py_UNICODE *u) {
    const Py_UNICODE *u_end = u;
    while (*u_end++) ;
    return (size_t)(u_end - u - 1);
}
#define __Pyx_PyUnicode_FromUnicode(u)       PyUnicode_FromUnicode(u, __Pyx_Py_UNICODE_strlen(u))
#define __Pyx_PyUnicode_FromUnicodeAndLength PyUnicode_FromUnicode
#define __Pyx_PyUnicode_AsUnicode            PyUnicode_AsUnicode
#define __Pyx_NewRef(obj) (Py_INCREF(obj), obj)
#define __Pyx_Owned_Py_None(b) __Pyx_NewRef(Py_None)
static CYTHON_INLINE PyObject * __Pyx_PyBool_FromLong(long b);
static CYTHON_INLINE int __Pyx_PyObject_IsTrue(PyObject*);
static CYTHON_INLINE int __Pyx_PyObject_IsTrueAndDecref(PyObject*);
static CYTHON_INLINE PyObject* __Pyx_PyNumber_IntOrLong(PyObject* x);
#define __Pyx_PySequence_Tuple(obj)\
    (likely(PyTuple_CheckExact(obj)) ? __Pyx_NewRef(obj) : PySequence_Tuple(obj))
static CYTHON_INLINE Py_ssize_t __Pyx_PyIndex_AsSsize_t(PyObject*);
static CYTHON_INLINE PyObject * __Pyx_PyInt_FromSize_t(size_t);
static CYTHON_INLINE Py_hash_t __Pyx_PyIndex_AsHash_t(PyObject*);
#if CYTHON_ASSUME_SAFE_MACROS
#define __pyx_PyFloat_AsDouble(x) (PyFloat_CheckExact(x) ? PyFloat_AS_DOUBLE(x) : PyFloat_AsDouble(x))
#else
#define __pyx_PyFloat_AsDouble(x) PyFloat_AsDouble(x)
#endif
#define __pyx_PyFloat_AsFloat(x) ((float) __pyx_PyFloat_AsDouble(x))
#if PY_MAJOR_VERSION >= 3
#define __Pyx_PyNumber_Int(x) (PyLong_CheckExact(x) ? __Pyx_NewRef(x) : PyNumber_Long(x))
#else
#define __Pyx_PyNumber_Int(x) (PyInt_CheckExact(x) ? __Pyx_NewRef(x) : PyNumber_Int(x))
#endif
#define __Pyx_PyNumber_Float(x) (PyFloat_CheckExact(x) ? __Pyx_NewRef(x) : PyNumber_Float(x))
#if PY_MAJOR_VERSION < 3 && __PYX_DEFAULT_STRING_ENCODING_IS_ASCII
static int __Pyx_sys_getdefaultencoding_not_ascii;
static int __Pyx_init_sys_getdefaultencoding_params(void) {
    PyObject* sys;
    PyObject* default_encoding = NULL;
    PyObject* ascii_chars_u = NULL;
    PyObject* ascii_chars_b = NULL;
    const char* default_encoding_c;
    sys = PyImport_ImportModule("sys");
    if (!sys) goto bad;
    default_encoding = PyObject_CallMethod(sys, (char*) "getdefaultencoding", NULL);
    Py_DECREF(sys);
    if (!default_encoding) goto bad;
    default_encoding_c = PyBytes_AsString(default_encoding);
    if (!default_encoding_c) goto bad;
    if (strcmp(default_encoding_c, "ascii") == 0) {
        __Pyx_sys_getdefaultencoding_not_ascii = 0;
    } else {
        char ascii_chars[128];
        int c;
        for (c = 0; c < 128; c++) {
            ascii_chars[c] = c;
        }
        __Pyx_sys_getdefaultencoding_not_ascii = 1;
        ascii_chars_u = PyUnicode_DecodeASCII(ascii_chars, 128, NULL);
        if (!ascii_chars_u) goto bad;
        ascii_chars_b = PyUnicode_AsEncodedString(ascii_chars_u, default_encoding_c, NULL);
        if (!ascii_chars_b || !PyBytes_Check(ascii_chars_b) || memcmp(ascii_chars, PyBytes_AS_STRING(ascii_chars_b), 128) != 0) {
            PyErr_Format(
                PyExc_ValueError,
                "This module compiled with c_string_encoding=ascii, but default encoding '%.200s' is not a superset of ascii.",
                default_encoding_c);
            goto bad;
        }
        Py_DECREF(ascii_chars_u);
        Py_DECREF(ascii_chars_b);
    }
    Py_DECREF(default_encoding);
    return 0;
bad:
    Py_XDECREF(default_encoding);
    Py_XDECREF(ascii_chars_u);
    Py_XDECREF(ascii_chars_b);
    return -1;
}
#endif
#if __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT && PY_MAJOR_VERSION >= 3
#define __Pyx_PyUnicode_FromStringAndSize(c_str, size) PyUnicode_DecodeUTF8(c_str, size, NULL)
#else
#define __Pyx_PyUnicode_FromStringAndSize(c_str, size) PyUnicode_Decode(c_str, size, __PYX_DEFAULT_STRING_ENCODING, NULL)
#if __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT
static char* __PYX_DEFAULT_STRING_ENCODING;
static int __Pyx_init_sys_getdefaultencoding_params(void) {
    PyObject* sys;
    PyObject* default_encoding = NULL;
    char* default_encoding_c;
    sys = PyImport_ImportModule("sys");
    if (!sys) goto bad;
    default_encoding = PyObject_CallMethod(sys, (char*) (const char*) "getdefaultencoding", NULL);
    Py_DECREF(sys);
    if (!default_encoding) goto bad;
    default_encoding_c = PyBytes_AsString(default_encoding);
    if (!default_encoding_c) goto bad;
    __PYX_DEFAULT_STRING_ENCODING = (char*) malloc(strlen(default_encoding_c) + 1);
    if (!__PYX_DEFAULT_STRING_ENCODING) goto bad;
    strcpy(__PYX_DEFAULT_STRING_ENCODING, default_encoding_c);
    Py_DECREF(default_encoding);
    return 0;
bad:
    Py_XDECREF(default_encoding);
    return -1;
}
#endif
#endif


/* Test for GCC > 2.95 */
#if defined(__GNUC__)     && (__GNUC__ > 2 || (__GNUC__ == 2 && (__GNUC_MINOR__ > 95)))
  #define likely(x)   __builtin_expect(!!(x), 1)
  #define unlikely(x) __builtin_expect(!!(x), 0)
#else /* !__GNUC__ or GCC < 2.95 */
  #define likely(x)   (x)
  #define unlikely(x) (x)
#endif /* __GNUC__ */
static CYTHON_INLINE void __Pyx_pretend_to_initialize(void* ptr) { (void)ptr; }

static PyObject *__pyx_m = NULL;
static PyObject *__pyx_d;
static PyObject *__pyx_b;
static PyObject *__pyx_cython_runtime = NULL;
static PyObject *__pyx_empty_tuple;
static PyObject *__pyx_empty_bytes;
static PyObject *__pyx_empty_unicode;
static int __pyx_lineno;
static int __pyx_clineno = 0;
static const char * __pyx_cfilenm= __FILE__;
static const char *__pyx_filename;


static const char *__pyx_f[] = {
  "spacy\\symbols.pyx",
};

/*--- Type declarations ---*/

/* "spacy/symbols.pxd":1
 * cdef enum symbol_t:             # <<<<<<<<<<<<<<
 *     NIL
 *     IS_ALPHA
 */
enum __pyx_t_5spacy_7symbols_symbol_t {
  __pyx_e_5spacy_7symbols_NIL,
  __pyx_e_5spacy_7symbols_IS_ALPHA,
  __pyx_e_5spacy_7symbols_IS_ASCII,
  __pyx_e_5spacy_7symbols_IS_DIGIT,
  __pyx_e_5spacy_7symbols_IS_LOWER,
  __pyx_e_5spacy_7symbols_IS_PUNCT,
  __pyx_e_5spacy_7symbols_IS_SPACE,
  __pyx_e_5spacy_7symbols_IS_TITLE,
  __pyx_e_5spacy_7symbols_IS_UPPER,
  __pyx_e_5spacy_7symbols_LIKE_URL,
  __pyx_e_5spacy_7symbols_LIKE_NUM,
  __pyx_e_5spacy_7symbols_LIKE_EMAIL,
  __pyx_e_5spacy_7symbols_IS_STOP,
  __pyx_e_5spacy_7symbols_IS_OOV_DEPRECATED,
  __pyx_e_5spacy_7symbols_IS_BRACKET,
  __pyx_e_5spacy_7symbols_IS_QUOTE,
  __pyx_e_5spacy_7symbols_IS_LEFT_PUNCT,
  __pyx_e_5spacy_7symbols_IS_RIGHT_PUNCT,
  __pyx_e_5spacy_7symbols_IS_CURRENCY,
  __pyx_e_5spacy_7symbols_FLAG19 = 19,
  __pyx_e_5spacy_7symbols_FLAG20,
  __pyx_e_5spacy_7symbols_FLAG21,
  __pyx_e_5spacy_7symbols_FLAG22,
  __pyx_e_5spacy_7symbols_FLAG23,
  __pyx_e_5spacy_7symbols_FLAG24,
  __pyx_e_5spacy_7symbols_FLAG25,
  __pyx_e_5spacy_7symbols_FLAG26,
  __pyx_e_5spacy_7symbols_FLAG27,
  __pyx_e_5spacy_7symbols_FLAG28,
  __pyx_e_5spacy_7symbols_FLAG29,
  __pyx_e_5spacy_7symbols_FLAG30,
  __pyx_e_5spacy_7symbols_FLAG31,
  __pyx_e_5spacy_7symbols_FLAG32,
  __pyx_e_5spacy_7symbols_FLAG33,
  __pyx_e_5spacy_7symbols_FLAG34,
  __pyx_e_5spacy_7symbols_FLAG35,
  __pyx_e_5spacy_7symbols_FLAG36,
  __pyx_e_5spacy_7symbols_FLAG37,
  __pyx_e_5spacy_7symbols_FLAG38,
  __pyx_e_5spacy_7symbols_FLAG39,
  __pyx_e_5spacy_7symbols_FLAG40,
  __pyx_e_5spacy_7symbols_FLAG41,
  __pyx_e_5spacy_7symbols_FLAG42,
  __pyx_e_5spacy_7symbols_FLAG43,
  __pyx_e_5spacy_7symbols_FLAG44,
  __pyx_e_5spacy_7symbols_FLAG45,
  __pyx_e_5spacy_7symbols_FLAG46,
  __pyx_e_5spacy_7symbols_FLAG47,
  __pyx_e_5spacy_7symbols_FLAG48,
  __pyx_e_5spacy_7symbols_FLAG49,
  __pyx_e_5spacy_7symbols_FLAG50,
  __pyx_e_5spacy_7symbols_FLAG51,
  __pyx_e_5spacy_7symbols_FLAG52,
  __pyx_e_5spacy_7symbols_FLAG53,
  __pyx_e_5spacy_7symbols_FLAG54,
  __pyx_e_5spacy_7symbols_FLAG55,
  __pyx_e_5spacy_7symbols_FLAG56,
  __pyx_e_5spacy_7symbols_FLAG57,
  __pyx_e_5spacy_7symbols_FLAG58,
  __pyx_e_5spacy_7symbols_FLAG59,
  __pyx_e_5spacy_7symbols_FLAG60,
  __pyx_e_5spacy_7symbols_FLAG61,
  __pyx_e_5spacy_7symbols_FLAG62,
  __pyx_e_5spacy_7symbols_FLAG63,
  __pyx_e_5spacy_7symbols_ID,
  __pyx_e_5spacy_7symbols_ORTH,
  __pyx_e_5spacy_7symbols_LOWER,
  __pyx_e_5spacy_7symbols_NORM,
  __pyx_e_5spacy_7symbols_SHAPE,
  __pyx_e_5spacy_7symbols_PREFIX,
  __pyx_e_5spacy_7symbols_SUFFIX,
  __pyx_e_5spacy_7symbols_LENGTH,
  __pyx_e_5spacy_7symbols_CLUSTER,
  __pyx_e_5spacy_7symbols_LEMMA,
  __pyx_e_5spacy_7symbols_POS,
  __pyx_e_5spacy_7symbols_TAG,
  __pyx_e_5spacy_7symbols_DEP,
  __pyx_e_5spacy_7symbols_ENT_IOB,
  __pyx_e_5spacy_7symbols_ENT_TYPE,
  __pyx_e_5spacy_7symbols_HEAD,
  __pyx_e_5spacy_7symbols_SENT_START,
  __pyx_e_5spacy_7symbols_SPACY,
  __pyx_e_5spacy_7symbols_PROB,
  __pyx_e_5spacy_7symbols_LANG,
  __pyx_e_5spacy_7symbols_ADJ,
  __pyx_e_5spacy_7symbols_ADP,
  __pyx_e_5spacy_7symbols_ADV,
  __pyx_e_5spacy_7symbols_AUX,
  __pyx_e_5spacy_7symbols_CONJ,
  __pyx_e_5spacy_7symbols_CCONJ,
  __pyx_e_5spacy_7symbols_DET,
  __pyx_e_5spacy_7symbols_INTJ,
  __pyx_e_5spacy_7symbols_NOUN,
  __pyx_e_5spacy_7symbols_NUM,
  __pyx_e_5spacy_7symbols_PART,
  __pyx_e_5spacy_7symbols_PRON,
  __pyx_e_5spacy_7symbols_PROPN,
  __pyx_e_5spacy_7symbols_PUNCT,
  __pyx_e_5spacy_7symbols_SCONJ,
  __pyx_e_5spacy_7symbols_SYM,
  __pyx_e_5spacy_7symbols_VERB,
  __pyx_e_5spacy_7symbols_X,
  __pyx_e_5spacy_7symbols_EOL,
  __pyx_e_5spacy_7symbols_SPACE,
  __pyx_e_5spacy_7symbols_DEPRECATED001,
  __pyx_e_5spacy_7symbols_DEPRECATED002,
  __pyx_e_5spacy_7symbols_DEPRECATED003,
  __pyx_e_5spacy_7symbols_DEPRECATED004,
  __pyx_e_5spacy_7symbols_DEPRECATED005,
  __pyx_e_5spacy_7symbols_DEPRECATED006,
  __pyx_e_5spacy_7symbols_DEPRECATED007,
  __pyx_e_5spacy_7symbols_DEPRECATED008,
  __pyx_e_5spacy_7symbols_DEPRECATED009,
  __pyx_e_5spacy_7symbols_DEPRECATED010,
  __pyx_e_5spacy_7symbols_DEPRECATED011,
  __pyx_e_5spacy_7symbols_DEPRECATED012,
  __pyx_e_5spacy_7symbols_DEPRECATED013,
  __pyx_e_5spacy_7symbols_DEPRECATED014,
  __pyx_e_5spacy_7symbols_DEPRECATED015,
  __pyx_e_5spacy_7symbols_DEPRECATED016,
  __pyx_e_5spacy_7symbols_DEPRECATED017,
  __pyx_e_5spacy_7symbols_DEPRECATED018,
  __pyx_e_5spacy_7symbols_DEPRECATED019,
  __pyx_e_5spacy_7symbols_DEPRECATED020,
  __pyx_e_5spacy_7symbols_DEPRECATED021,
  __pyx_e_5spacy_7symbols_DEPRECATED022,
  __pyx_e_5spacy_7symbols_DEPRECATED023,
  __pyx_e_5spacy_7symbols_DEPRECATED024,
  __pyx_e_5spacy_7symbols_DEPRECATED025,
  __pyx_e_5spacy_7symbols_DEPRECATED026,
  __pyx_e_5spacy_7symbols_DEPRECATED027,
  __pyx_e_5spacy_7symbols_DEPRECATED028,
  __pyx_e_5spacy_7symbols_DEPRECATED029,
  __pyx_e_5spacy_7symbols_DEPRECATED030,
  __pyx_e_5spacy_7symbols_DEPRECATED031,
  __pyx_e_5spacy_7symbols_DEPRECATED032,
  __pyx_e_5spacy_7symbols_DEPRECATED033,
  __pyx_e_5spacy_7symbols_DEPRECATED034,
  __pyx_e_5spacy_7symbols_DEPRECATED035,
  __pyx_e_5spacy_7symbols_DEPRECATED036,
  __pyx_e_5spacy_7symbols_DEPRECATED037,
  __pyx_e_5spacy_7symbols_DEPRECATED038,
  __pyx_e_5spacy_7symbols_DEPRECATED039,
  __pyx_e_5spacy_7symbols_DEPRECATED040,
  __pyx_e_5spacy_7symbols_DEPRECATED041,
  __pyx_e_5spacy_7symbols_DEPRECATED042,
  __pyx_e_5spacy_7symbols_DEPRECATED043,
  __pyx_e_5spacy_7symbols_DEPRECATED044,
  __pyx_e_5spacy_7symbols_DEPRECATED045,
  __pyx_e_5spacy_7symbols_DEPRECATED046,
  __pyx_e_5spacy_7symbols_DEPRECATED047,
  __pyx_e_5spacy_7symbols_DEPRECATED048,
  __pyx_e_5spacy_7symbols_DEPRECATED049,
  __pyx_e_5spacy_7symbols_DEPRECATED050,
  __pyx_e_5spacy_7symbols_DEPRECATED051,
  __pyx_e_5spacy_7symbols_DEPRECATED052,
  __pyx_e_5spacy_7symbols_DEPRECATED053,
  __pyx_e_5spacy_7symbols_DEPRECATED054,
  __pyx_e_5spacy_7symbols_DEPRECATED055,
  __pyx_e_5spacy_7symbols_DEPRECATED056,
  __pyx_e_5spacy_7symbols_DEPRECATED057,
  __pyx_e_5spacy_7symbols_DEPRECATED058,
  __pyx_e_5spacy_7symbols_DEPRECATED059,
  __pyx_e_5spacy_7symbols_DEPRECATED060,
  __pyx_e_5spacy_7symbols_DEPRECATED061,
  __pyx_e_5spacy_7symbols_DEPRECATED062,
  __pyx_e_5spacy_7symbols_DEPRECATED063,
  __pyx_e_5spacy_7symbols_DEPRECATED064,
  __pyx_e_5spacy_7symbols_DEPRECATED065,
  __pyx_e_5spacy_7symbols_DEPRECATED066,
  __pyx_e_5spacy_7symbols_DEPRECATED067,
  __pyx_e_5spacy_7symbols_DEPRECATED068,
  __pyx_e_5spacy_7symbols_DEPRECATED069,
  __pyx_e_5spacy_7symbols_DEPRECATED070,
  __pyx_e_5spacy_7symbols_DEPRECATED071,
  __pyx_e_5spacy_7symbols_DEPRECATED072,
  __pyx_e_5spacy_7symbols_DEPRECATED073,
  __pyx_e_5spacy_7symbols_DEPRECATED074,
  __pyx_e_5spacy_7symbols_DEPRECATED075,
  __pyx_e_5spacy_7symbols_DEPRECATED076,
  __pyx_e_5spacy_7symbols_DEPRECATED077,
  __pyx_e_5spacy_7symbols_DEPRECATED078,
  __pyx_e_5spacy_7symbols_DEPRECATED079,
  __pyx_e_5spacy_7symbols_DEPRECATED080,
  __pyx_e_5spacy_7symbols_DEPRECATED081,
  __pyx_e_5spacy_7symbols_DEPRECATED082,
  __pyx_e_5spacy_7symbols_DEPRECATED083,
  __pyx_e_5spacy_7symbols_DEPRECATED084,
  __pyx_e_5spacy_7symbols_DEPRECATED085,
  __pyx_e_5spacy_7symbols_DEPRECATED086,
  __pyx_e_5spacy_7symbols_DEPRECATED087,
  __pyx_e_5spacy_7symbols_DEPRECATED088,
  __pyx_e_5spacy_7symbols_DEPRECATED089,
  __pyx_e_5spacy_7symbols_DEPRECATED090,
  __pyx_e_5spacy_7symbols_DEPRECATED091,
  __pyx_e_5spacy_7symbols_DEPRECATED092,
  __pyx_e_5spacy_7symbols_DEPRECATED093,
  __pyx_e_5spacy_7symbols_DEPRECATED094,
  __pyx_e_5spacy_7symbols_DEPRECATED095,
  __pyx_e_5spacy_7symbols_DEPRECATED096,
  __pyx_e_5spacy_7symbols_DEPRECATED097,
  __pyx_e_5spacy_7symbols_DEPRECATED098,
  __pyx_e_5spacy_7symbols_DEPRECATED099,
  __pyx_e_5spacy_7symbols_DEPRECATED100,
  __pyx_e_5spacy_7symbols_DEPRECATED101,
  __pyx_e_5spacy_7symbols_DEPRECATED102,
  __pyx_e_5spacy_7symbols_DEPRECATED103,
  __pyx_e_5spacy_7symbols_DEPRECATED104,
  __pyx_e_5spacy_7symbols_DEPRECATED105,
  __pyx_e_5spacy_7symbols_DEPRECATED106,
  __pyx_e_5spacy_7symbols_DEPRECATED107,
  __pyx_e_5spacy_7symbols_DEPRECATED108,
  __pyx_e_5spacy_7symbols_DEPRECATED109,
  __pyx_e_5spacy_7symbols_DEPRECATED110,
  __pyx_e_5spacy_7symbols_DEPRECATED111,
  __pyx_e_5spacy_7symbols_DEPRECATED112,
  __pyx_e_5spacy_7symbols_DEPRECATED113,
  __pyx_e_5spacy_7symbols_DEPRECATED114,
  __pyx_e_5spacy_7symbols_DEPRECATED115,
  __pyx_e_5spacy_7symbols_DEPRECATED116,
  __pyx_e_5spacy_7symbols_DEPRECATED117,
  __pyx_e_5spacy_7symbols_DEPRECATED118,
  __pyx_e_5spacy_7symbols_DEPRECATED119,
  __pyx_e_5spacy_7symbols_DEPRECATED120,
  __pyx_e_5spacy_7symbols_DEPRECATED121,
  __pyx_e_5spacy_7symbols_DEPRECATED122,
  __pyx_e_5spacy_7symbols_DEPRECATED123,
  __pyx_e_5spacy_7symbols_DEPRECATED124,
  __pyx_e_5spacy_7symbols_DEPRECATED125,
  __pyx_e_5spacy_7symbols_DEPRECATED126,
  __pyx_e_5spacy_7symbols_DEPRECATED127,
  __pyx_e_5spacy_7symbols_DEPRECATED128,
  __pyx_e_5spacy_7symbols_DEPRECATED129,
  __pyx_e_5spacy_7symbols_DEPRECATED130,
  __pyx_e_5spacy_7symbols_DEPRECATED131,
  __pyx_e_5spacy_7symbols_DEPRECATED132,
  __pyx_e_5spacy_7symbols_DEPRECATED133,
  __pyx_e_5spacy_7symbols_DEPRECATED134,
  __pyx_e_5spacy_7symbols_DEPRECATED135,
  __pyx_e_5spacy_7symbols_DEPRECATED136,
  __pyx_e_5spacy_7symbols_DEPRECATED137,
  __pyx_e_5spacy_7symbols_DEPRECATED138,
  __pyx_e_5spacy_7symbols_DEPRECATED139,
  __pyx_e_5spacy_7symbols_DEPRECATED140,
  __pyx_e_5spacy_7symbols_DEPRECATED141,
  __pyx_e_5spacy_7symbols_DEPRECATED142,
  __pyx_e_5spacy_7symbols_DEPRECATED143,
  __pyx_e_5spacy_7symbols_DEPRECATED144,
  __pyx_e_5spacy_7symbols_DEPRECATED145,
  __pyx_e_5spacy_7symbols_DEPRECATED146,
  __pyx_e_5spacy_7symbols_DEPRECATED147,
  __pyx_e_5spacy_7symbols_DEPRECATED148,
  __pyx_e_5spacy_7symbols_DEPRECATED149,
  __pyx_e_5spacy_7symbols_DEPRECATED150,
  __pyx_e_5spacy_7symbols_DEPRECATED151,
  __pyx_e_5spacy_7symbols_DEPRECATED152,
  __pyx_e_5spacy_7symbols_DEPRECATED153,
  __pyx_e_5spacy_7symbols_DEPRECATED154,
  __pyx_e_5spacy_7symbols_DEPRECATED155,
  __pyx_e_5spacy_7symbols_DEPRECATED156,
  __pyx_e_5spacy_7symbols_DEPRECATED157,
  __pyx_e_5spacy_7symbols_DEPRECATED158,
  __pyx_e_5spacy_7symbols_DEPRECATED159,
  __pyx_e_5spacy_7symbols_DEPRECATED160,
  __pyx_e_5spacy_7symbols_DEPRECATED161,
  __pyx_e_5spacy_7symbols_DEPRECATED162,
  __pyx_e_5spacy_7symbols_DEPRECATED163,
  __pyx_e_5spacy_7symbols_DEPRECATED164,
  __pyx_e_5spacy_7symbols_DEPRECATED165,
  __pyx_e_5spacy_7symbols_DEPRECATED166,
  __pyx_e_5spacy_7symbols_DEPRECATED167,
  __pyx_e_5spacy_7symbols_DEPRECATED168,
  __pyx_e_5spacy_7symbols_DEPRECATED169,
  __pyx_e_5spacy_7symbols_DEPRECATED170,
  __pyx_e_5spacy_7symbols_DEPRECATED171,
  __pyx_e_5spacy_7symbols_DEPRECATED172,
  __pyx_e_5spacy_7symbols_DEPRECATED173,
  __pyx_e_5spacy_7symbols_DEPRECATED174,
  __pyx_e_5spacy_7symbols_DEPRECATED175,
  __pyx_e_5spacy_7symbols_DEPRECATED176,
  __pyx_e_5spacy_7symbols_DEPRECATED177,
  __pyx_e_5spacy_7symbols_DEPRECATED178,
  __pyx_e_5spacy_7symbols_DEPRECATED179,
  __pyx_e_5spacy_7symbols_DEPRECATED180,
  __pyx_e_5spacy_7symbols_DEPRECATED181,
  __pyx_e_5spacy_7symbols_DEPRECATED182,
  __pyx_e_5spacy_7symbols_DEPRECATED183,
  __pyx_e_5spacy_7symbols_DEPRECATED184,
  __pyx_e_5spacy_7symbols_DEPRECATED185,
  __pyx_e_5spacy_7symbols_DEPRECATED186,
  __pyx_e_5spacy_7symbols_DEPRECATED187,
  __pyx_e_5spacy_7symbols_DEPRECATED188,
  __pyx_e_5spacy_7symbols_DEPRECATED189,
  __pyx_e_5spacy_7symbols_DEPRECATED190,
  __pyx_e_5spacy_7symbols_DEPRECATED191,
  __pyx_e_5spacy_7symbols_DEPRECATED192,
  __pyx_e_5spacy_7symbols_DEPRECATED193,
  __pyx_e_5spacy_7symbols_DEPRECATED194,
  __pyx_e_5spacy_7symbols_DEPRECATED195,
  __pyx_e_5spacy_7symbols_DEPRECATED196,
  __pyx_e_5spacy_7symbols_DEPRECATED197,
  __pyx_e_5spacy_7symbols_DEPRECATED198,
  __pyx_e_5spacy_7symbols_DEPRECATED199,
  __pyx_e_5spacy_7symbols_DEPRECATED200,
  __pyx_e_5spacy_7symbols_DEPRECATED201,
  __pyx_e_5spacy_7symbols_DEPRECATED202,
  __pyx_e_5spacy_7symbols_DEPRECATED203,
  __pyx_e_5spacy_7symbols_DEPRECATED204,
  __pyx_e_5spacy_7symbols_DEPRECATED205,
  __pyx_e_5spacy_7symbols_DEPRECATED206,
  __pyx_e_5spacy_7symbols_DEPRECATED207,
  __pyx_e_5spacy_7symbols_DEPRECATED208,
  __pyx_e_5spacy_7symbols_DEPRECATED209,
  __pyx_e_5spacy_7symbols_DEPRECATED210,
  __pyx_e_5spacy_7symbols_DEPRECATED211,
  __pyx_e_5spacy_7symbols_DEPRECATED212,
  __pyx_e_5spacy_7symbols_DEPRECATED213,
  __pyx_e_5spacy_7symbols_DEPRECATED214,
  __pyx_e_5spacy_7symbols_DEPRECATED215,
  __pyx_e_5spacy_7symbols_DEPRECATED216,
  __pyx_e_5spacy_7symbols_DEPRECATED217,
  __pyx_e_5spacy_7symbols_DEPRECATED218,
  __pyx_e_5spacy_7symbols_DEPRECATED219,
  __pyx_e_5spacy_7symbols_DEPRECATED220,
  __pyx_e_5spacy_7symbols_DEPRECATED221,
  __pyx_e_5spacy_7symbols_DEPRECATED222,
  __pyx_e_5spacy_7symbols_DEPRECATED223,
  __pyx_e_5spacy_7symbols_DEPRECATED224,
  __pyx_e_5spacy_7symbols_DEPRECATED225,
  __pyx_e_5spacy_7symbols_DEPRECATED226,
  __pyx_e_5spacy_7symbols_DEPRECATED227,
  __pyx_e_5spacy_7symbols_DEPRECATED228,
  __pyx_e_5spacy_7symbols_DEPRECATED229,
  __pyx_e_5spacy_7symbols_DEPRECATED230,
  __pyx_e_5spacy_7symbols_DEPRECATED231,
  __pyx_e_5spacy_7symbols_DEPRECATED232,
  __pyx_e_5spacy_7symbols_DEPRECATED233,
  __pyx_e_5spacy_7symbols_DEPRECATED234,
  __pyx_e_5spacy_7symbols_DEPRECATED235,
  __pyx_e_5spacy_7symbols_DEPRECATED236,
  __pyx_e_5spacy_7symbols_DEPRECATED237,
  __pyx_e_5spacy_7symbols_DEPRECATED238,
  __pyx_e_5spacy_7symbols_DEPRECATED239,
  __pyx_e_5spacy_7symbols_DEPRECATED240,
  __pyx_e_5spacy_7symbols_DEPRECATED241,
  __pyx_e_5spacy_7symbols_DEPRECATED242,
  __pyx_e_5spacy_7symbols_DEPRECATED243,
  __pyx_e_5spacy_7symbols_DEPRECATED244,
  __pyx_e_5spacy_7symbols_DEPRECATED245,
  __pyx_e_5spacy_7symbols_DEPRECATED246,
  __pyx_e_5spacy_7symbols_DEPRECATED247,
  __pyx_e_5spacy_7symbols_DEPRECATED248,
  __pyx_e_5spacy_7symbols_DEPRECATED249,
  __pyx_e_5spacy_7symbols_DEPRECATED250,
  __pyx_e_5spacy_7symbols_DEPRECATED251,
  __pyx_e_5spacy_7symbols_DEPRECATED252,
  __pyx_e_5spacy_7symbols_DEPRECATED253,
  __pyx_e_5spacy_7symbols_DEPRECATED254,
  __pyx_e_5spacy_7symbols_DEPRECATED255,
  __pyx_e_5spacy_7symbols_DEPRECATED256,
  __pyx_e_5spacy_7symbols_DEPRECATED257,
  __pyx_e_5spacy_7symbols_DEPRECATED258,
  __pyx_e_5spacy_7symbols_DEPRECATED259,
  __pyx_e_5spacy_7symbols_DEPRECATED260,
  __pyx_e_5spacy_7symbols_DEPRECATED261,
  __pyx_e_5spacy_7symbols_DEPRECATED262,
  __pyx_e_5spacy_7symbols_DEPRECATED263,
  __pyx_e_5spacy_7symbols_DEPRECATED264,
  __pyx_e_5spacy_7symbols_DEPRECATED265,
  __pyx_e_5spacy_7symbols_DEPRECATED266,
  __pyx_e_5spacy_7symbols_DEPRECATED267,
  __pyx_e_5spacy_7symbols_DEPRECATED268,
  __pyx_e_5spacy_7symbols_DEPRECATED269,
  __pyx_e_5spacy_7symbols_DEPRECATED270,
  __pyx_e_5spacy_7symbols_DEPRECATED271,
  __pyx_e_5spacy_7symbols_DEPRECATED272,
  __pyx_e_5spacy_7symbols_DEPRECATED273,
  __pyx_e_5spacy_7symbols_DEPRECATED274,
  __pyx_e_5spacy_7symbols_DEPRECATED275,
  __pyx_e_5spacy_7symbols_DEPRECATED276,
  __pyx_e_5spacy_7symbols_PERSON,
  __pyx_e_5spacy_7symbols_NORP,
  __pyx_e_5spacy_7symbols_FACILITY,
  __pyx_e_5spacy_7symbols_ORG,
  __pyx_e_5spacy_7symbols_GPE,
  __pyx_e_5spacy_7symbols_LOC,
  __pyx_e_5spacy_7symbols_PRODUCT,
  __pyx_e_5spacy_7symbols_EVENT,
  __pyx_e_5spacy_7symbols_WORK_OF_ART,
  __pyx_e_5spacy_7symbols_LANGUAGE,
  __pyx_e_5spacy_7symbols_LAW,
  __pyx_e_5spacy_7symbols_DATE,
  __pyx_e_5spacy_7symbols_TIME,
  __pyx_e_5spacy_7symbols_PERCENT,
  __pyx_e_5spacy_7symbols_MONEY,
  __pyx_e_5spacy_7symbols_QUANTITY,
  __pyx_e_5spacy_7symbols_ORDINAL,
  __pyx_e_5spacy_7symbols_CARDINAL,
  __pyx_e_5spacy_7symbols_acomp,
  __pyx_e_5spacy_7symbols_advcl,
  __pyx_e_5spacy_7symbols_advmod,
  __pyx_e_5spacy_7symbols_agent,
  __pyx_e_5spacy_7symbols_amod,
  __pyx_e_5spacy_7symbols_appos,
  __pyx_e_5spacy_7symbols_attr,
  __pyx_e_5spacy_7symbols_aux,
  __pyx_e_5spacy_7symbols_auxpass,
  __pyx_e_5spacy_7symbols_cc,
  __pyx_e_5spacy_7symbols_ccomp,
  __pyx_e_5spacy_7symbols_complm,
  __pyx_e_5spacy_7symbols_conj,
  __pyx_e_5spacy_7symbols_cop,
  __pyx_e_5spacy_7symbols_csubj,
  __pyx_e_5spacy_7symbols_csubjpass,
  __pyx_e_5spacy_7symbols_dep,
  __pyx_e_5spacy_7symbols_det,
  __pyx_e_5spacy_7symbols_dobj,
  __pyx_e_5spacy_7symbols_expl,
  __pyx_e_5spacy_7symbols_hmod,
  __pyx_e_5spacy_7symbols_hyph,
  __pyx_e_5spacy_7symbols_infmod,
  __pyx_e_5spacy_7symbols_intj,
  __pyx_e_5spacy_7symbols_iobj,
  __pyx_e_5spacy_7symbols_mark,
  __pyx_e_5spacy_7symbols_meta,
  __pyx_e_5spacy_7symbols_neg,
  __pyx_e_5spacy_7symbols_nmod,
  __pyx_e_5spacy_7symbols_nn,
  __pyx_e_5spacy_7symbols_npadvmod,
  __pyx_e_5spacy_7symbols_nsubj,
  __pyx_e_5spacy_7symbols_nsubjpass,
  __pyx_e_5spacy_7symbols_num,
  __pyx_e_5spacy_7symbols_number,
  __pyx_e_5spacy_7symbols_oprd,
  __pyx_e_5spacy_7symbols_obj,
  __pyx_e_5spacy_7symbols_obl,
  __pyx_e_5spacy_7symbols_parataxis,
  __pyx_e_5spacy_7symbols_partmod,
  __pyx_e_5spacy_7symbols_pcomp,
  __pyx_e_5spacy_7symbols_pobj,
  __pyx_e_5spacy_7symbols_poss,
  __pyx_e_5spacy_7symbols_possessive,
  __pyx_e_5spacy_7symbols_preconj,
  __pyx_e_5spacy_7symbols_prep,
  __pyx_e_5spacy_7symbols_prt,
  __pyx_e_5spacy_7symbols_punct,
  __pyx_e_5spacy_7symbols_quantmod,
  __pyx_e_5spacy_7symbols_relcl,
  __pyx_e_5spacy_7symbols_rcmod,
  __pyx_e_5spacy_7symbols_root,
  __pyx_e_5spacy_7symbols_xcomp,
  __pyx_e_5spacy_7symbols_acl,
  __pyx_e_5spacy_7symbols_ENT_KB_ID,
  __pyx_e_5spacy_7symbols_MORPH,
  __pyx_e_5spacy_7symbols_ENT_ID,
  __pyx_e_5spacy_7symbols_IDX,
  __pyx_e_5spacy_7symbols__
};

/* --- Runtime support code (head) --- */
/* Refnanny.proto */
#ifndef CYTHON_REFNANNY
  #define CYTHON_REFNANNY 0
#endif
#if CYTHON_REFNANNY
  typedef struct {
    void (*INCREF)(void*, PyObject*, int);
    void (*DECREF)(void*, PyObject*, int);
    void (*GOTREF)(void*, PyObject*, int);
    void (*GIVEREF)(void*, PyObject*, int);
    void* (*SetupContext)(const char*, int, const char*);
    void (*FinishContext)(void**);
  } __Pyx_RefNannyAPIStruct;
  static __Pyx_RefNannyAPIStruct *__Pyx_RefNanny = NULL;
  static __Pyx_RefNannyAPIStruct *__Pyx_RefNannyImportAPI(const char *modname);
  #define __Pyx_RefNannyDeclarations void *__pyx_refnanny = NULL;
#ifdef WITH_THREAD
  #define __Pyx_RefNannySetupContext(name, acquire_gil)\
          if (acquire_gil) {\
              PyGILState_STATE __pyx_gilstate_save = PyGILState_Ensure();\
              __pyx_refnanny = __Pyx_RefNanny->SetupContext((name), __LINE__, __FILE__);\
              PyGILState_Release(__pyx_gilstate_save);\
          } else {\
              __pyx_refnanny = __Pyx_RefNanny->SetupContext((name), __LINE__, __FILE__);\
          }
#else
  #define __Pyx_RefNannySetupContext(name, acquire_gil)\
          __pyx_refnanny = __Pyx_RefNanny->SetupContext((name), __LINE__, __FILE__)
#endif
  #define __Pyx_RefNannyFinishContext()\
          __Pyx_RefNanny->FinishContext(&__pyx_refnanny)
  #define __Pyx_INCREF(r)  __Pyx_RefNanny->INCREF(__pyx_refnanny, (PyObject *)(r), __LINE__)
  #define __Pyx_DECREF(r)  __Pyx_RefNanny->DECREF(__pyx_refnanny, (PyObject *)(r), __LINE__)
  #define __Pyx_GOTREF(r)  __Pyx_RefNanny->GOTREF(__pyx_refnanny, (PyObject *)(r), __LINE__)
  #define __Pyx_GIVEREF(r) __Pyx_RefNanny->GIVEREF(__pyx_refnanny, (PyObject *)(r), __LINE__)
  #define __Pyx_XINCREF(r)  do { if((r) != NULL) {__Pyx_INCREF(r); }} while(0)
  #define __Pyx_XDECREF(r)  do { if((r) != NULL) {__Pyx_DECREF(r); }} while(0)
  #define __Pyx_XGOTREF(r)  do { if((r) != NULL) {__Pyx_GOTREF(r); }} while(0)
  #define __Pyx_XGIVEREF(r) do { if((r) != NULL) {__Pyx_GIVEREF(r);}} while(0)
#else
  #define __Pyx_RefNannyDeclarations
  #define __Pyx_RefNannySetupContext(name, acquire_gil)
  #define __Pyx_RefNannyFinishContext()
  #define __Pyx_INCREF(r) Py_INCREF(r)
  #define __Pyx_DECREF(r) Py_DECREF(r)
  #define __Pyx_GOTREF(r)
  #define __Pyx_GIVEREF(r)
  #define __Pyx_XINCREF(r) Py_XINCREF(r)
  #define __Pyx_XDECREF(r) Py_XDECREF(r)
  #define __Pyx_XGOTREF(r)
  #define __Pyx_XGIVEREF(r)
#endif
#define __Pyx_XDECREF_SET(r, v) do {\
        PyObject *tmp = (PyObject *) r;\
        r = v; __Pyx_XDECREF(tmp);\
    } while (0)
#define __Pyx_DECREF_SET(r, v) do {\
        PyObject *tmp = (PyObject *) r;\
        r = v; __Pyx_DECREF(tmp);\
    } while (0)
#define __Pyx_CLEAR(r)    do { PyObject* tmp = ((PyObject*)(r)); r = NULL; __Pyx_DECREF(tmp);} while(0)
#define __Pyx_XCLEAR(r)   do { if((r) != NULL) {PyObject* tmp = ((PyObject*)(r)); r = NULL; __Pyx_DECREF(tmp);}} while(0)

/* PyObjectGetAttrStr.proto */
#if CYTHON_USE_TYPE_SLOTS
static CYTHON_INLINE PyObject* __Pyx_PyObject_GetAttrStr(PyObject* obj, PyObject* attr_name);
#else
#define __Pyx_PyObject_GetAttrStr(o,n) PyObject_GetAttr(o,n)
#endif

/* GetBuiltinName.proto */
static PyObject *__Pyx_GetBuiltinName(PyObject *name);

/* GetItemInt.proto */
#define __Pyx_GetItemInt(o, i, type, is_signed, to_py_func, is_list, wraparound, boundscheck)\
    (__Pyx_fits_Py_ssize_t(i, type, is_signed) ?\
    __Pyx_GetItemInt_Fast(o, (Py_ssize_t)i, is_list, wraparound, boundscheck) :\
    (is_list ? (PyErr_SetString(PyExc_IndexError, "list index out of range"), (PyObject*)NULL) :\
               __Pyx_GetItemInt_Generic(o, to_py_func(i))))
#define __Pyx_GetItemInt_List(o, i, type, is_signed, to_py_func, is_list, wraparound, boundscheck)\
    (__Pyx_fits_Py_ssize_t(i, type, is_signed) ?\
    __Pyx_GetItemInt_List_Fast(o, (Py_ssize_t)i, wraparound, boundscheck) :\
    (PyErr_SetString(PyExc_IndexError, "list index out of range"), (PyObject*)NULL))
static CYTHON_INLINE PyObject *__Pyx_GetItemInt_List_Fast(PyObject *o, Py_ssize_t i,
                                                              int wraparound, int boundscheck);
#define __Pyx_GetItemInt_Tuple(o, i, type, is_signed, to_py_func, is_list, wraparound, boundscheck)\
    (__Pyx_fits_Py_ssize_t(i, type, is_signed) ?\
    __Pyx_GetItemInt_Tuple_Fast(o, (Py_ssize_t)i, wraparound, boundscheck) :\
    (PyErr_SetString(PyExc_IndexError, "tuple index out of range"), (PyObject*)NULL))
static CYTHON_INLINE PyObject *__Pyx_GetItemInt_Tuple_Fast(PyObject *o, Py_ssize_t i,
                                                              int wraparound, int boundscheck);
static PyObject *__Pyx_GetItemInt_Generic(PyObject *o, PyObject* j);
static CYTHON_INLINE PyObject *__Pyx_GetItemInt_Fast(PyObject *o, Py_ssize_t i,
                                                     int is_list, int wraparound, int boundscheck);

/* PyDictVersioning.proto */
#if CYTHON_USE_DICT_VERSIONS && CYTHON_USE_TYPE_SLOTS
#define __PYX_DICT_VERSION_INIT  ((PY_UINT64_T) -1)
#define __PYX_GET_DICT_VERSION(dict)  (((PyDictObject*)(dict))->ma_version_tag)
#define __PYX_UPDATE_DICT_CACHE(dict, value, cache_var, version_var)\
    (version_var) = __PYX_GET_DICT_VERSION(dict);\
    (cache_var) = (value);
#define __PYX_PY_DICT_LOOKUP_IF_MODIFIED(VAR, DICT, LOOKUP) {\
    static PY_UINT64_T __pyx_dict_version = 0;\
    static PyObject *__pyx_dict_cached_value = NULL;\
    if (likely(__PYX_GET_DICT_VERSION(DICT) == __pyx_dict_version)) {\
        (VAR) = __pyx_dict_cached_value;\
    } else {\
        (VAR) = __pyx_dict_cached_value = (LOOKUP);\
        __pyx_dict_version = __PYX_GET_DICT_VERSION(DICT);\
    }\
}
static CYTHON_INLINE PY_UINT64_T __Pyx_get_tp_dict_version(PyObject *obj);
static CYTHON_INLINE PY_UINT64_T __Pyx_get_object_dict_version(PyObject *obj);
static CYTHON_INLINE int __Pyx_object_dict_version_matches(PyObject* obj, PY_UINT64_T tp_dict_version, PY_UINT64_T obj_dict_version);
#else
#define __PYX_GET_DICT_VERSION(dict)  (0)
#define __PYX_UPDATE_DICT_CACHE(dict, value, cache_var, version_var)
#define __PYX_PY_DICT_LOOKUP_IF_MODIFIED(VAR, DICT, LOOKUP)  (VAR) = (LOOKUP);
#endif

/* GetModuleGlobalName.proto */
#if CYTHON_USE_DICT_VERSIONS
#define __Pyx_GetModuleGlobalName(var, name)  do {\
    static PY_UINT64_T __pyx_dict_version = 0;\
    static PyObject *__pyx_dict_cached_value = NULL;\
    (var) = (likely(__pyx_dict_version == __PYX_GET_DICT_VERSION(__pyx_d))) ?\
        (likely(__pyx_dict_cached_value) ? __Pyx_NewRef(__pyx_dict_cached_value) : __Pyx_GetBuiltinName(name)) :\
        __Pyx__GetModuleGlobalName(name, &__pyx_dict_version, &__pyx_dict_cached_value);\
} while(0)
#define __Pyx_GetModuleGlobalNameUncached(var, name)  do {\
    PY_UINT64_T __pyx_dict_version;\
    PyObject *__pyx_dict_cached_value;\
    (var) = __Pyx__GetModuleGlobalName(name, &__pyx_dict_version, &__pyx_dict_cached_value);\
} while(0)
static PyObject *__Pyx__GetModuleGlobalName(PyObject *name, PY_UINT64_T *dict_version, PyObject **dict_cached_value);
#else
#define __Pyx_GetModuleGlobalName(var, name)  (var) = __Pyx__GetModuleGlobalName(name)
#define __Pyx_GetModuleGlobalNameUncached(var, name)  (var) = __Pyx__GetModuleGlobalName(name)
static CYTHON_INLINE PyObject *__Pyx__GetModuleGlobalName(PyObject *name);
#endif

/* PyFunctionFastCall.proto */
#if CYTHON_FAST_PYCALL
#define __Pyx_PyFunction_FastCall(func, args, nargs)\
    __Pyx_PyFunction_FastCallDict((func), (args), (nargs), NULL)
#if 1 || PY_VERSION_HEX < 0x030600B1
static PyObject *__Pyx_PyFunction_FastCallDict(PyObject *func, PyObject **args, Py_ssize_t nargs, PyObject *kwargs);
#else
#define __Pyx_PyFunction_FastCallDict(func, args, nargs, kwargs) _PyFunction_FastCallDict(func, args, nargs, kwargs)
#endif
#define __Pyx_BUILD_ASSERT_EXPR(cond)\
    (sizeof(char [1 - 2*!(cond)]) - 1)
#ifndef Py_MEMBER_SIZE
#define Py_MEMBER_SIZE(type, member) sizeof(((type *)0)->member)
#endif
#if CYTHON_FAST_PYCALL
  static size_t __pyx_pyframe_localsplus_offset = 0;
  #include "frameobject.h"
#if PY_VERSION_HEX >= 0x030b00a6
  #ifndef Py_BUILD_CORE
    #define Py_BUILD_CORE 1
  #endif
  #include "internal/pycore_frame.h"
#endif
  #define __Pxy_PyFrame_Initialize_Offsets()\
    ((void)__Pyx_BUILD_ASSERT_EXPR(sizeof(PyFrameObject) == offsetof(PyFrameObject, f_localsplus) + Py_MEMBER_SIZE(PyFrameObject, f_localsplus)),\
     (void)(__pyx_pyframe_localsplus_offset = ((size_t)PyFrame_Type.tp_basicsize) - Py_MEMBER_SIZE(PyFrameObject, f_localsplus)))
  #define __Pyx_PyFrame_GetLocalsplus(frame)\
    (assert(__pyx_pyframe_localsplus_offset), (PyObject **)(((char *)(frame)) + __pyx_pyframe_localsplus_offset))
#endif // CYTHON_FAST_PYCALL
#endif

/* PyObjectCall.proto */
#if CYTHON_COMPILING_IN_CPYTHON
static CYTHON_INLINE PyObject* __Pyx_PyObject_Call(PyObject *func, PyObject *arg, PyObject *kw);
#else
#define __Pyx_PyObject_Call(func, arg, kw) PyObject_Call(func, arg, kw)
#endif

/* PyObjectCallMethO.proto */
#if CYTHON_COMPILING_IN_CPYTHON
static CYTHON_INLINE PyObject* __Pyx_PyObject_CallMethO(PyObject *func, PyObject *arg);
#endif

/* PyObjectCallNoArg.proto */
#if CYTHON_COMPILING_IN_CPYTHON
static CYTHON_INLINE PyObject* __Pyx_PyObject_CallNoArg(PyObject *func);
#else
#define __Pyx_PyObject_CallNoArg(func) __Pyx_PyObject_Call(func, __pyx_empty_tuple, NULL)
#endif

/* ListCompAppend.proto */
#if CYTHON_USE_PYLIST_INTERNALS && CYTHON_ASSUME_SAFE_MACROS
static CYTHON_INLINE int __Pyx_ListComp_Append(PyObject* list, PyObject* x) {
    PyListObject* L = (PyListObject*) list;
    Py_ssize_t len = Py_SIZE(list);
    if (likely(L->allocated > len)) {
        Py_INCREF(x);
        PyList_SET_ITEM(list, len, x);
        __Pyx_SET_SIZE(list, len + 1);
        return 0;
    }
    return PyList_Append(list, x);
}
#else
#define __Pyx_ListComp_Append(L,x) PyList_Append(L,x)
#endif

/* PyCFunctionFastCall.proto */
#if CYTHON_FAST_PYCCALL
static CYTHON_INLINE PyObject *__Pyx_PyCFunction_FastCall(PyObject *func, PyObject **args, Py_ssize_t nargs);
#else
#define __Pyx_PyCFunction_FastCall(func, args, nargs)  (assert(0), NULL)
#endif

/* PyObjectCallOneArg.proto */
static CYTHON_INLINE PyObject* __Pyx_PyObject_CallOneArg(PyObject *func, PyObject *arg);

/* PyThreadStateGet.proto */
#if CYTHON_FAST_THREAD_STATE
#define __Pyx_PyThreadState_declare  PyThreadState *__pyx_tstate;
#define __Pyx_PyThreadState_assign  __pyx_tstate = __Pyx_PyThreadState_Current;
#define __Pyx_PyErr_Occurred()  __pyx_tstate->curexc_type
#else
#define __Pyx_PyThreadState_declare
#define __Pyx_PyThreadState_assign
#define __Pyx_PyErr_Occurred()  PyErr_Occurred()
#endif

/* PyErrFetchRestore.proto */
#if CYTHON_FAST_THREAD_STATE
#define __Pyx_PyErr_Clear() __Pyx_ErrRestore(NULL, NULL, NULL)
#define __Pyx_ErrRestoreWithState(type, value, tb)  __Pyx_ErrRestoreInState(PyThreadState_GET(), type, value, tb)
#define __Pyx_ErrFetchWithState(type, value, tb)    __Pyx_ErrFetchInState(PyThreadState_GET(), type, value, tb)
#define __Pyx_ErrRestore(type, value, tb)  __Pyx_ErrRestoreInState(__pyx_tstate, type, value, tb)
#define __Pyx_ErrFetch(type, value, tb)    __Pyx_ErrFetchInState(__pyx_tstate, type, value, tb)
static CYTHON_INLINE void __Pyx_ErrRestoreInState(PyThreadState *tstate, PyObject *type, PyObject *value, PyObject *tb);
static CYTHON_INLINE void __Pyx_ErrFetchInState(PyThreadState *tstate, PyObject **type, PyObject **value, PyObject **tb);
#if CYTHON_COMPILING_IN_CPYTHON
#define __Pyx_PyErr_SetNone(exc) (Py_INCREF(exc), __Pyx_ErrRestore((exc), NULL, NULL))
#else
#define __Pyx_PyErr_SetNone(exc) PyErr_SetNone(exc)
#endif
#else
#define __Pyx_PyErr_Clear() PyErr_Clear()
#define __Pyx_PyErr_SetNone(exc) PyErr_SetNone(exc)
#define __Pyx_ErrRestoreWithState(type, value, tb)  PyErr_Restore(type, value, tb)
#define __Pyx_ErrFetchWithState(type, value, tb)  PyErr_Fetch(type, value, tb)
#define __Pyx_ErrRestoreInState(tstate, type, value, tb)  PyErr_Restore(type, value, tb)
#define __Pyx_ErrFetchInState(tstate, type, value, tb)  PyErr_Fetch(type, value, tb)
#define __Pyx_ErrRestore(type, value, tb)  PyErr_Restore(type, value, tb)
#define __Pyx_ErrFetch(type, value, tb)  PyErr_Fetch(type, value, tb)
#endif

/* CLineInTraceback.proto */
#ifdef CYTHON_CLINE_IN_TRACEBACK
#define __Pyx_CLineForTraceback(tstate, c_line)  (((CYTHON_CLINE_IN_TRACEBACK)) ? c_line : 0)
#else
static int __Pyx_CLineForTraceback(PyThreadState *tstate, int c_line);
#endif

/* CodeObjectCache.proto */
typedef struct {
    PyCodeObject* code_object;
    int code_line;
} __Pyx_CodeObjectCacheEntry;
struct __Pyx_CodeObjectCache {
    int count;
    int max_count;
    __Pyx_CodeObjectCacheEntry* entries;
};
static struct __Pyx_CodeObjectCache __pyx_code_cache = {0,0,NULL};
static int __pyx_bisect_code_objects(__Pyx_CodeObjectCacheEntry* entries, int count, int code_line);
static PyCodeObject *__pyx_find_code_object(int code_line);
static void __pyx_insert_code_object(int code_line, PyCodeObject* code_object);

/* AddTraceback.proto */
static void __Pyx_AddTraceback(const char *funcname, int c_line,
                               int py_line, const char *filename);

/* GCCDiagnostics.proto */
#if defined(__GNUC__) && (__GNUC__ > 4 || (__GNUC__ == 4 && __GNUC_MINOR__ >= 6))
#define __Pyx_HAS_GCC_DIAGNOSTIC
#endif

/* CIntToPy.proto */
static CYTHON_INLINE PyObject* __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(enum __pyx_t_5spacy_7symbols_symbol_t value);

/* CIntToPy.proto */
static CYTHON_INLINE PyObject* __Pyx_PyInt_From_long(long value);

/* GetAttr.proto */
static CYTHON_INLINE PyObject *__Pyx_GetAttr(PyObject *, PyObject *);

/* Globals.proto */
static PyObject* __Pyx_Globals(void);

/* CIntFromPy.proto */
static CYTHON_INLINE long __Pyx_PyInt_As_long(PyObject *);

/* CIntFromPy.proto */
static CYTHON_INLINE int __Pyx_PyInt_As_int(PyObject *);

/* FastTypeChecks.proto */
#if CYTHON_COMPILING_IN_CPYTHON
#define __Pyx_TypeCheck(obj, type) __Pyx_IsSubtype(Py_TYPE(obj), (PyTypeObject *)type)
static CYTHON_INLINE int __Pyx_IsSubtype(PyTypeObject *a, PyTypeObject *b);
static CYTHON_INLINE int __Pyx_PyErr_GivenExceptionMatches(PyObject *err, PyObject *type);
static CYTHON_INLINE int __Pyx_PyErr_GivenExceptionMatches2(PyObject *err, PyObject *type1, PyObject *type2);
#else
#define __Pyx_TypeCheck(obj, type) PyObject_TypeCheck(obj, (PyTypeObject *)type)
#define __Pyx_PyErr_GivenExceptionMatches(err, type) PyErr_GivenExceptionMatches(err, type)
#define __Pyx_PyErr_GivenExceptionMatches2(err, type1, type2) (PyErr_GivenExceptionMatches(err, type1) || PyErr_GivenExceptionMatches(err, type2))
#endif
#define __Pyx_PyException_Check(obj) __Pyx_TypeCheck(obj, PyExc_Exception)

/* CheckBinaryVersion.proto */
static int __Pyx_check_binary_version(void);

/* InitStrings.proto */
static int __Pyx_InitStrings(__Pyx_StringTabEntry *t);


/* Module declarations from 'spacy.symbols' */
#define __Pyx_MODULE_NAME "spacy.symbols"
extern int __pyx_module_is_main_spacy__symbols;
int __pyx_module_is_main_spacy__symbols = 0;

/* Implementation of 'spacy.symbols' */
static PyObject *__pyx_builtin_sorted;
static const char __pyx_k_[] = "";
static const char __pyx_k_X[] = "X";
static const char __pyx_k_x[] = "x";
static const char __pyx_k_ID[] = "ID";
static const char __pyx_k__2[] = "_";
static const char __pyx_k_cc[] = "cc";
static const char __pyx_k_it[] = "it";
static const char __pyx_k_nn[] = "nn";
static const char __pyx_k_ADJ[] = "ADJ";
static const char __pyx_k_ADP[] = "ADP";
static const char __pyx_k_ADV[] = "ADV";
static const char __pyx_k_AUX[] = "AUX";
static const char __pyx_k_DEP[] = "DEP";
static const char __pyx_k_DET[] = "DET";
static const char __pyx_k_EOL[] = "EOL";
static const char __pyx_k_GPE[] = "GPE";
static const char __pyx_k_IDS[] = "IDS";
static const char __pyx_k_IDX[] = "IDX";
static const char __pyx_k_LAW[] = "LAW";
static const char __pyx_k_LOC[] = "LOC";
static const char __pyx_k_NUM[] = "NUM";
static const char __pyx_k_ORG[] = "ORG";
static const char __pyx_k_POS[] = "POS";
static const char __pyx_k_SYM[] = "SYM";
static const char __pyx_k_TAG[] = "TAG";
static const char __pyx_k_acl[] = "acl";
static const char __pyx_k_aux[] = "aux";
static const char __pyx_k_cop[] = "cop";
static const char __pyx_k_dep[] = "dep";
static const char __pyx_k_det[] = "det";
static const char __pyx_k_key[] = "key";
static const char __pyx_k_neg[] = "neg";
static const char __pyx_k_num[] = "num";
static const char __pyx_k_obj[] = "obj";
static const char __pyx_k_obl[] = "obl";
static const char __pyx_k_prt[] = "prt";
static const char __pyx_k_CONJ[] = "CONJ";
static const char __pyx_k_DATE[] = "DATE";
static const char __pyx_k_HEAD[] = "HEAD";
static const char __pyx_k_INTJ[] = "INTJ";
static const char __pyx_k_LANG[] = "LANG";
static const char __pyx_k_NORM[] = "NORM";
static const char __pyx_k_NORP[] = "NORP";
static const char __pyx_k_NOUN[] = "NOUN";
static const char __pyx_k_ORTH[] = "ORTH";
static const char __pyx_k_PART[] = "PART";
static const char __pyx_k_PROB[] = "PROB";
static const char __pyx_k_PRON[] = "PRON";
static const char __pyx_k_TIME[] = "TIME";
static const char __pyx_k_VERB[] = "VERB";
static const char __pyx_k_amod[] = "amod";
static const char __pyx_k_attr[] = "attr";
static const char __pyx_k_conj[] = "conj";
static const char __pyx_k_dobj[] = "dobj";
static const char __pyx_k_expl[] = "expl";
static const char __pyx_k_hmod[] = "hmod";
static const char __pyx_k_hyph[] = "hyph";
static const char __pyx_k_intj[] = "intj";
static const char __pyx_k_iobj[] = "iobj";
static const char __pyx_k_main[] = "__main__";
static const char __pyx_k_mark[] = "mark";
static const char __pyx_k_meta[] = "meta";
static const char __pyx_k_name[] = "__name__";
static const char __pyx_k_nmod[] = "nmod";
static const char __pyx_k_oprd[] = "oprd";
static const char __pyx_k_pobj[] = "pobj";
static const char __pyx_k_poss[] = "poss";
static const char __pyx_k_prep[] = "prep";
static const char __pyx_k_root[] = "root";
static const char __pyx_k_test[] = "__test__";
static const char __pyx_k_CCONJ[] = "CCONJ";
static const char __pyx_k_EVENT[] = "EVENT";
static const char __pyx_k_LEMMA[] = "LEMMA";
static const char __pyx_k_LOWER[] = "LOWER";
static const char __pyx_k_MONEY[] = "MONEY";
static const char __pyx_k_MORPH[] = "MORPH";
static const char __pyx_k_NAMES[] = "NAMES";
static const char __pyx_k_PROPN[] = "PROPN";
static const char __pyx_k_PUNCT[] = "PUNCT";
static const char __pyx_k_SCONJ[] = "SCONJ";
static const char __pyx_k_SHAPE[] = "SHAPE";
static const char __pyx_k_SPACE[] = "SPACE";
static const char __pyx_k_SPACY[] = "SPACY";
static const char __pyx_k_acomp[] = "acomp";
static const char __pyx_k_advcl[] = "advcl";
static const char __pyx_k_agent[] = "agent";
static const char __pyx_k_appos[] = "appos";
static const char __pyx_k_ccomp[] = "ccomp";
static const char __pyx_k_csubj[] = "csubj";
static const char __pyx_k_items[] = "items";
static const char __pyx_k_nsubj[] = "nsubj";
static const char __pyx_k_pcomp[] = "pcomp";
static const char __pyx_k_punct[] = "punct";
static const char __pyx_k_rcmod[] = "rcmod";
static const char __pyx_k_relcl[] = "relcl";
static const char __pyx_k_xcomp[] = "xcomp";
static const char __pyx_k_ENT_ID[] = "ENT_ID";
static const char __pyx_k_FLAG19[] = "FLAG19";
static const char __pyx_k_FLAG20[] = "FLAG20";
static const char __pyx_k_FLAG21[] = "FLAG21";
static const char __pyx_k_FLAG22[] = "FLAG22";
static const char __pyx_k_FLAG23[] = "FLAG23";
static const char __pyx_k_FLAG24[] = "FLAG24";
static const char __pyx_k_FLAG25[] = "FLAG25";
static const char __pyx_k_FLAG26[] = "FLAG26";
static const char __pyx_k_FLAG27[] = "FLAG27";
static const char __pyx_k_FLAG28[] = "FLAG28";
static const char __pyx_k_FLAG29[] = "FLAG29";
static const char __pyx_k_FLAG30[] = "FLAG30";
static const char __pyx_k_FLAG31[] = "FLAG31";
static const char __pyx_k_FLAG32[] = "FLAG32";
static const char __pyx_k_FLAG33[] = "FLAG33";
static const char __pyx_k_FLAG34[] = "FLAG34";
static const char __pyx_k_FLAG35[] = "FLAG35";
static const char __pyx_k_FLAG36[] = "FLAG36";
static const char __pyx_k_FLAG37[] = "FLAG37";
static const char __pyx_k_FLAG38[] = "FLAG38";
static const char __pyx_k_FLAG39[] = "FLAG39";
static const char __pyx_k_FLAG40[] = "FLAG40";
static const char __pyx_k_FLAG41[] = "FLAG41";
static const char __pyx_k_FLAG42[] = "FLAG42";
static const char __pyx_k_FLAG43[] = "FLAG43";
static const char __pyx_k_FLAG44[] = "FLAG44";
static const char __pyx_k_FLAG45[] = "FLAG45";
static const char __pyx_k_FLAG46[] = "FLAG46";
static const char __pyx_k_FLAG47[] = "FLAG47";
static const char __pyx_k_FLAG48[] = "FLAG48";
static const char __pyx_k_FLAG49[] = "FLAG49";
static const char __pyx_k_FLAG50[] = "FLAG50";
static const char __pyx_k_FLAG51[] = "FLAG51";
static const char __pyx_k_FLAG52[] = "FLAG52";
static const char __pyx_k_FLAG53[] = "FLAG53";
static const char __pyx_k_FLAG54[] = "FLAG54";
static const char __pyx_k_FLAG55[] = "FLAG55";
static const char __pyx_k_FLAG56[] = "FLAG56";
static const char __pyx_k_FLAG57[] = "FLAG57";
static const char __pyx_k_FLAG58[] = "FLAG58";
static const char __pyx_k_FLAG59[] = "FLAG59";
static const char __pyx_k_FLAG60[] = "FLAG60";
static const char __pyx_k_FLAG61[] = "FLAG61";
static const char __pyx_k_FLAG62[] = "FLAG62";
static const char __pyx_k_FLAG63[] = "FLAG63";
static const char __pyx_k_LENGTH[] = "LENGTH";
static const char __pyx_k_PERSON[] = "PERSON";
static const char __pyx_k_PREFIX[] = "PREFIX";
static const char __pyx_k_SUFFIX[] = "SUFFIX";
static const char __pyx_k_advmod[] = "advmod";
static const char __pyx_k_complm[] = "complm";
static const char __pyx_k_infmod[] = "infmod";
static const char __pyx_k_number[] = "number";
static const char __pyx_k_sorted[] = "sorted";
static const char __pyx_k_update[] = "update";
static const char __pyx_k_CLUSTER[] = "CLUSTER";
static const char __pyx_k_ENT_IOB[] = "ENT_IOB";
static const char __pyx_k_IS_STOP[] = "IS_STOP";
static const char __pyx_k_ORDINAL[] = "ORDINAL";
static const char __pyx_k_PERCENT[] = "PERCENT";
static const char __pyx_k_PRODUCT[] = "PRODUCT";
static const char __pyx_k_auxpass[] = "auxpass";
static const char __pyx_k_partmod[] = "partmod";
static const char __pyx_k_preconj[] = "preconj";
static const char __pyx_k_CARDINAL[] = "CARDINAL";
static const char __pyx_k_ENT_TYPE[] = "ENT_TYPE";
static const char __pyx_k_FACILITY[] = "FACILITY";
static const char __pyx_k_IS_ALPHA[] = "IS_ALPHA";
static const char __pyx_k_IS_ASCII[] = "IS_ASCII";
static const char __pyx_k_IS_DIGIT[] = "IS_DIGIT";
static const char __pyx_k_IS_LOWER[] = "IS_LOWER";
static const char __pyx_k_IS_PUNCT[] = "IS_PUNCT";
static const char __pyx_k_IS_QUOTE[] = "IS_QUOTE";
static const char __pyx_k_IS_SPACE[] = "IS_SPACE";
static const char __pyx_k_IS_TITLE[] = "IS_TITLE";
static const char __pyx_k_IS_UPPER[] = "IS_UPPER";
static const char __pyx_k_LANGUAGE[] = "LANGUAGE";
static const char __pyx_k_LIKE_NUM[] = "LIKE_NUM";
static const char __pyx_k_LIKE_URL[] = "LIKE_URL";
static const char __pyx_k_QUANTITY[] = "QUANTITY";
static const char __pyx_k_npadvmod[] = "npadvmod";
static const char __pyx_k_quantmod[] = "quantmod";
static const char __pyx_k_ENT_KB_ID[] = "ENT_KB_ID";
static const char __pyx_k_csubjpass[] = "csubjpass";
static const char __pyx_k_nsubjpass[] = "nsubjpass";
static const char __pyx_k_parataxis[] = "parataxis";
static const char __pyx_k_sort_nums[] = "sort_nums";
static const char __pyx_k_IS_BRACKET[] = "IS_BRACKET";
static const char __pyx_k_LIKE_EMAIL[] = "LIKE_EMAIL";
static const char __pyx_k_SENT_START[] = "SENT_START";
static const char __pyx_k_possessive[] = "possessive";
static const char __pyx_k_IS_CURRENCY[] = "IS_CURRENCY";
static const char __pyx_k_WORK_OF_ART[] = "WORK_OF_ART";
static const char __pyx_k_DEPRECATED001[] = "DEPRECATED001";
static const char __pyx_k_DEPRECATED002[] = "DEPRECATED002";
static const char __pyx_k_DEPRECATED003[] = "DEPRECATED003";
static const char __pyx_k_DEPRECATED004[] = "DEPRECATED004";
static const char __pyx_k_DEPRECATED005[] = "DEPRECATED005";
static const char __pyx_k_DEPRECATED006[] = "DEPRECATED006";
static const char __pyx_k_DEPRECATED007[] = "DEPRECATED007";
static const char __pyx_k_DEPRECATED008[] = "DEPRECATED008";
static const char __pyx_k_DEPRECATED009[] = "DEPRECATED009";
static const char __pyx_k_DEPRECATED010[] = "DEPRECATED010";
static const char __pyx_k_DEPRECATED011[] = "DEPRECATED011";
static const char __pyx_k_DEPRECATED012[] = "DEPRECATED012";
static const char __pyx_k_DEPRECATED013[] = "DEPRECATED013";
static const char __pyx_k_DEPRECATED014[] = "DEPRECATED014";
static const char __pyx_k_DEPRECATED015[] = "DEPRECATED015";
static const char __pyx_k_DEPRECATED016[] = "DEPRECATED016";
static const char __pyx_k_DEPRECATED017[] = "DEPRECATED017";
static const char __pyx_k_DEPRECATED018[] = "DEPRECATED018";
static const char __pyx_k_DEPRECATED019[] = "DEPRECATED019";
static const char __pyx_k_DEPRECATED020[] = "DEPRECATED020";
static const char __pyx_k_DEPRECATED021[] = "DEPRECATED021";
static const char __pyx_k_DEPRECATED022[] = "DEPRECATED022";
static const char __pyx_k_DEPRECATED023[] = "DEPRECATED023";
static const char __pyx_k_DEPRECATED024[] = "DEPRECATED024";
static const char __pyx_k_DEPRECATED025[] = "DEPRECATED025";
static const char __pyx_k_DEPRECATED026[] = "DEPRECATED026";
static const char __pyx_k_DEPRECATED027[] = "DEPRECATED027";
static const char __pyx_k_DEPRECATED028[] = "DEPRECATED028";
static const char __pyx_k_DEPRECATED029[] = "DEPRECATED029";
static const char __pyx_k_DEPRECATED030[] = "DEPRECATED030";
static const char __pyx_k_DEPRECATED031[] = "DEPRECATED031";
static const char __pyx_k_DEPRECATED032[] = "DEPRECATED032";
static const char __pyx_k_DEPRECATED033[] = "DEPRECATED033";
static const char __pyx_k_DEPRECATED034[] = "DEPRECATED034";
static const char __pyx_k_DEPRECATED035[] = "DEPRECATED035";
static const char __pyx_k_DEPRECATED036[] = "DEPRECATED036";
static const char __pyx_k_DEPRECATED037[] = "DEPRECATED037";
static const char __pyx_k_DEPRECATED038[] = "DEPRECATED038";
static const char __pyx_k_DEPRECATED039[] = "DEPRECATED039";
static const char __pyx_k_DEPRECATED040[] = "DEPRECATED040";
static const char __pyx_k_DEPRECATED041[] = "DEPRECATED041";
static const char __pyx_k_DEPRECATED042[] = "DEPRECATED042";
static const char __pyx_k_DEPRECATED043[] = "DEPRECATED043";
static const char __pyx_k_DEPRECATED044[] = "DEPRECATED044";
static const char __pyx_k_DEPRECATED045[] = "DEPRECATED045";
static const char __pyx_k_DEPRECATED046[] = "DEPRECATED046";
static const char __pyx_k_DEPRECATED047[] = "DEPRECATED047";
static const char __pyx_k_DEPRECATED048[] = "DEPRECATED048";
static const char __pyx_k_DEPRECATED049[] = "DEPRECATED049";
static const char __pyx_k_DEPRECATED050[] = "DEPRECATED050";
static const char __pyx_k_DEPRECATED051[] = "DEPRECATED051";
static const char __pyx_k_DEPRECATED052[] = "DEPRECATED052";
static const char __pyx_k_DEPRECATED053[] = "DEPRECATED053";
static const char __pyx_k_DEPRECATED054[] = "DEPRECATED054";
static const char __pyx_k_DEPRECATED055[] = "DEPRECATED055";
static const char __pyx_k_DEPRECATED056[] = "DEPRECATED056";
static const char __pyx_k_DEPRECATED057[] = "DEPRECATED057";
static const char __pyx_k_DEPRECATED058[] = "DEPRECATED058";
static const char __pyx_k_DEPRECATED059[] = "DEPRECATED059";
static const char __pyx_k_DEPRECATED060[] = "DEPRECATED060";
static const char __pyx_k_DEPRECATED061[] = "DEPRECATED061";
static const char __pyx_k_DEPRECATED062[] = "DEPRECATED062";
static const char __pyx_k_DEPRECATED063[] = "DEPRECATED063";
static const char __pyx_k_DEPRECATED064[] = "DEPRECATED064";
static const char __pyx_k_DEPRECATED065[] = "DEPRECATED065";
static const char __pyx_k_DEPRECATED066[] = "DEPRECATED066";
static const char __pyx_k_DEPRECATED067[] = "DEPRECATED067";
static const char __pyx_k_DEPRECATED068[] = "DEPRECATED068";
static const char __pyx_k_DEPRECATED069[] = "DEPRECATED069";
static const char __pyx_k_DEPRECATED070[] = "DEPRECATED070";
static const char __pyx_k_DEPRECATED071[] = "DEPRECATED071";
static const char __pyx_k_DEPRECATED072[] = "DEPRECATED072";
static const char __pyx_k_DEPRECATED073[] = "DEPRECATED073";
static const char __pyx_k_DEPRECATED074[] = "DEPRECATED074";
static const char __pyx_k_DEPRECATED075[] = "DEPRECATED075";
static const char __pyx_k_DEPRECATED076[] = "DEPRECATED076";
static const char __pyx_k_DEPRECATED077[] = "DEPRECATED077";
static const char __pyx_k_DEPRECATED078[] = "DEPRECATED078";
static const char __pyx_k_DEPRECATED079[] = "DEPRECATED079";
static const char __pyx_k_DEPRECATED080[] = "DEPRECATED080";
static const char __pyx_k_DEPRECATED081[] = "DEPRECATED081";
static const char __pyx_k_DEPRECATED082[] = "DEPRECATED082";
static const char __pyx_k_DEPRECATED083[] = "DEPRECATED083";
static const char __pyx_k_DEPRECATED084[] = "DEPRECATED084";
static const char __pyx_k_DEPRECATED085[] = "DEPRECATED085";
static const char __pyx_k_DEPRECATED086[] = "DEPRECATED086";
static const char __pyx_k_DEPRECATED087[] = "DEPRECATED087";
static const char __pyx_k_DEPRECATED088[] = "DEPRECATED088";
static const char __pyx_k_DEPRECATED089[] = "DEPRECATED089";
static const char __pyx_k_DEPRECATED090[] = "DEPRECATED090";
static const char __pyx_k_DEPRECATED091[] = "DEPRECATED091";
static const char __pyx_k_DEPRECATED092[] = "DEPRECATED092";
static const char __pyx_k_DEPRECATED093[] = "DEPRECATED093";
static const char __pyx_k_DEPRECATED094[] = "DEPRECATED094";
static const char __pyx_k_DEPRECATED095[] = "DEPRECATED095";
static const char __pyx_k_DEPRECATED096[] = "DEPRECATED096";
static const char __pyx_k_DEPRECATED097[] = "DEPRECATED097";
static const char __pyx_k_DEPRECATED098[] = "DEPRECATED098";
static const char __pyx_k_DEPRECATED099[] = "DEPRECATED099";
static const char __pyx_k_DEPRECATED100[] = "DEPRECATED100";
static const char __pyx_k_DEPRECATED101[] = "DEPRECATED101";
static const char __pyx_k_DEPRECATED102[] = "DEPRECATED102";
static const char __pyx_k_DEPRECATED103[] = "DEPRECATED103";
static const char __pyx_k_DEPRECATED104[] = "DEPRECATED104";
static const char __pyx_k_DEPRECATED105[] = "DEPRECATED105";
static const char __pyx_k_DEPRECATED106[] = "DEPRECATED106";
static const char __pyx_k_DEPRECATED107[] = "DEPRECATED107";
static const char __pyx_k_DEPRECATED108[] = "DEPRECATED108";
static const char __pyx_k_DEPRECATED109[] = "DEPRECATED109";
static const char __pyx_k_DEPRECATED110[] = "DEPRECATED110";
static const char __pyx_k_DEPRECATED111[] = "DEPRECATED111";
static const char __pyx_k_DEPRECATED112[] = "DEPRECATED112";
static const char __pyx_k_DEPRECATED113[] = "DEPRECATED113";
static const char __pyx_k_DEPRECATED114[] = "DEPRECATED114";
static const char __pyx_k_DEPRECATED115[] = "DEPRECATED115";
static const char __pyx_k_DEPRECATED116[] = "DEPRECATED116";
static const char __pyx_k_DEPRECATED117[] = "DEPRECATED117";
static const char __pyx_k_DEPRECATED118[] = "DEPRECATED118";
static const char __pyx_k_DEPRECATED119[] = "DEPRECATED119";
static const char __pyx_k_DEPRECATED120[] = "DEPRECATED120";
static const char __pyx_k_DEPRECATED121[] = "DEPRECATED121";
static const char __pyx_k_DEPRECATED122[] = "DEPRECATED122";
static const char __pyx_k_DEPRECATED123[] = "DEPRECATED123";
static const char __pyx_k_DEPRECATED124[] = "DEPRECATED124";
static const char __pyx_k_DEPRECATED125[] = "DEPRECATED125";
static const char __pyx_k_DEPRECATED126[] = "DEPRECATED126";
static const char __pyx_k_DEPRECATED127[] = "DEPRECATED127";
static const char __pyx_k_DEPRECATED128[] = "DEPRECATED128";
static const char __pyx_k_DEPRECATED129[] = "DEPRECATED129";
static const char __pyx_k_DEPRECATED130[] = "DEPRECATED130";
static const char __pyx_k_DEPRECATED131[] = "DEPRECATED131";
static const char __pyx_k_DEPRECATED132[] = "DEPRECATED132";
static const char __pyx_k_DEPRECATED133[] = "DEPRECATED133";
static const char __pyx_k_DEPRECATED134[] = "DEPRECATED134";
static const char __pyx_k_DEPRECATED135[] = "DEPRECATED135";
static const char __pyx_k_DEPRECATED136[] = "DEPRECATED136";
static const char __pyx_k_DEPRECATED137[] = "DEPRECATED137";
static const char __pyx_k_DEPRECATED138[] = "DEPRECATED138";
static const char __pyx_k_DEPRECATED139[] = "DEPRECATED139";
static const char __pyx_k_DEPRECATED140[] = "DEPRECATED140";
static const char __pyx_k_DEPRECATED141[] = "DEPRECATED141";
static const char __pyx_k_DEPRECATED142[] = "DEPRECATED142";
static const char __pyx_k_DEPRECATED143[] = "DEPRECATED143";
static const char __pyx_k_DEPRECATED144[] = "DEPRECATED144";
static const char __pyx_k_DEPRECATED145[] = "DEPRECATED145";
static const char __pyx_k_DEPRECATED146[] = "DEPRECATED146";
static const char __pyx_k_DEPRECATED147[] = "DEPRECATED147";
static const char __pyx_k_DEPRECATED148[] = "DEPRECATED148";
static const char __pyx_k_DEPRECATED149[] = "DEPRECATED149";
static const char __pyx_k_DEPRECATED150[] = "DEPRECATED150";
static const char __pyx_k_DEPRECATED151[] = "DEPRECATED151";
static const char __pyx_k_DEPRECATED152[] = "DEPRECATED152";
static const char __pyx_k_DEPRECATED153[] = "DEPRECATED153";
static const char __pyx_k_DEPRECATED154[] = "DEPRECATED154";
static const char __pyx_k_DEPRECATED155[] = "DEPRECATED155";
static const char __pyx_k_DEPRECATED156[] = "DEPRECATED156";
static const char __pyx_k_DEPRECATED157[] = "DEPRECATED157";
static const char __pyx_k_DEPRECATED158[] = "DEPRECATED158";
static const char __pyx_k_DEPRECATED159[] = "DEPRECATED159";
static const char __pyx_k_DEPRECATED160[] = "DEPRECATED160";
static const char __pyx_k_DEPRECATED161[] = "DEPRECATED161";
static const char __pyx_k_DEPRECATED162[] = "DEPRECATED162";
static const char __pyx_k_DEPRECATED163[] = "DEPRECATED163";
static const char __pyx_k_DEPRECATED164[] = "DEPRECATED164";
static const char __pyx_k_DEPRECATED165[] = "DEPRECATED165";
static const char __pyx_k_DEPRECATED166[] = "DEPRECATED166";
static const char __pyx_k_DEPRECATED167[] = "DEPRECATED167";
static const char __pyx_k_DEPRECATED168[] = "DEPRECATED168";
static const char __pyx_k_DEPRECATED169[] = "DEPRECATED169";
static const char __pyx_k_DEPRECATED170[] = "DEPRECATED170";
static const char __pyx_k_DEPRECATED171[] = "DEPRECATED171";
static const char __pyx_k_DEPRECATED172[] = "DEPRECATED172";
static const char __pyx_k_DEPRECATED173[] = "DEPRECATED173";
static const char __pyx_k_DEPRECATED174[] = "DEPRECATED174";
static const char __pyx_k_DEPRECATED175[] = "DEPRECATED175";
static const char __pyx_k_DEPRECATED176[] = "DEPRECATED176";
static const char __pyx_k_DEPRECATED177[] = "DEPRECATED177";
static const char __pyx_k_DEPRECATED178[] = "DEPRECATED178";
static const char __pyx_k_DEPRECATED179[] = "DEPRECATED179";
static const char __pyx_k_DEPRECATED180[] = "DEPRECATED180";
static const char __pyx_k_DEPRECATED181[] = "DEPRECATED181";
static const char __pyx_k_DEPRECATED182[] = "DEPRECATED182";
static const char __pyx_k_DEPRECATED183[] = "DEPRECATED183";
static const char __pyx_k_DEPRECATED184[] = "DEPRECATED184";
static const char __pyx_k_DEPRECATED185[] = "DEPRECATED185";
static const char __pyx_k_DEPRECATED186[] = "DEPRECATED186";
static const char __pyx_k_DEPRECATED187[] = "DEPRECATED187";
static const char __pyx_k_DEPRECATED188[] = "DEPRECATED188";
static const char __pyx_k_DEPRECATED189[] = "DEPRECATED189";
static const char __pyx_k_DEPRECATED190[] = "DEPRECATED190";
static const char __pyx_k_DEPRECATED191[] = "DEPRECATED191";
static const char __pyx_k_DEPRECATED192[] = "DEPRECATED192";
static const char __pyx_k_DEPRECATED193[] = "DEPRECATED193";
static const char __pyx_k_DEPRECATED194[] = "DEPRECATED194";
static const char __pyx_k_DEPRECATED195[] = "DEPRECATED195";
static const char __pyx_k_DEPRECATED196[] = "DEPRECATED196";
static const char __pyx_k_DEPRECATED197[] = "DEPRECATED197";
static const char __pyx_k_DEPRECATED198[] = "DEPRECATED198";
static const char __pyx_k_DEPRECATED199[] = "DEPRECATED199";
static const char __pyx_k_DEPRECATED200[] = "DEPRECATED200";
static const char __pyx_k_DEPRECATED201[] = "DEPRECATED201";
static const char __pyx_k_DEPRECATED202[] = "DEPRECATED202";
static const char __pyx_k_DEPRECATED203[] = "DEPRECATED203";
static const char __pyx_k_DEPRECATED204[] = "DEPRECATED204";
static const char __pyx_k_DEPRECATED205[] = "DEPRECATED205";
static const char __pyx_k_DEPRECATED206[] = "DEPRECATED206";
static const char __pyx_k_DEPRECATED207[] = "DEPRECATED207";
static const char __pyx_k_DEPRECATED208[] = "DEPRECATED208";
static const char __pyx_k_DEPRECATED209[] = "DEPRECATED209";
static const char __pyx_k_DEPRECATED210[] = "DEPRECATED210";
static const char __pyx_k_DEPRECATED211[] = "DEPRECATED211";
static const char __pyx_k_DEPRECATED212[] = "DEPRECATED212";
static const char __pyx_k_DEPRECATED213[] = "DEPRECATED213";
static const char __pyx_k_DEPRECATED214[] = "DEPRECATED214";
static const char __pyx_k_DEPRECATED215[] = "DEPRECATED215";
static const char __pyx_k_DEPRECATED216[] = "DEPRECATED216";
static const char __pyx_k_DEPRECATED217[] = "DEPRECATED217";
static const char __pyx_k_DEPRECATED218[] = "DEPRECATED218";
static const char __pyx_k_DEPRECATED219[] = "DEPRECATED219";
static const char __pyx_k_DEPRECATED220[] = "DEPRECATED220";
static const char __pyx_k_DEPRECATED221[] = "DEPRECATED221";
static const char __pyx_k_DEPRECATED222[] = "DEPRECATED222";
static const char __pyx_k_DEPRECATED223[] = "DEPRECATED223";
static const char __pyx_k_DEPRECATED224[] = "DEPRECATED224";
static const char __pyx_k_DEPRECATED225[] = "DEPRECATED225";
static const char __pyx_k_DEPRECATED226[] = "DEPRECATED226";
static const char __pyx_k_DEPRECATED227[] = "DEPRECATED227";
static const char __pyx_k_DEPRECATED228[] = "DEPRECATED228";
static const char __pyx_k_DEPRECATED229[] = "DEPRECATED229";
static const char __pyx_k_DEPRECATED230[] = "DEPRECATED230";
static const char __pyx_k_DEPRECATED231[] = "DEPRECATED231";
static const char __pyx_k_DEPRECATED232[] = "DEPRECATED232";
static const char __pyx_k_DEPRECATED233[] = "DEPRECATED233";
static const char __pyx_k_DEPRECATED234[] = "DEPRECATED234";
static const char __pyx_k_DEPRECATED235[] = "DEPRECATED235";
static const char __pyx_k_DEPRECATED236[] = "DEPRECATED236";
static const char __pyx_k_DEPRECATED237[] = "DEPRECATED237";
static const char __pyx_k_DEPRECATED238[] = "DEPRECATED238";
static const char __pyx_k_DEPRECATED239[] = "DEPRECATED239";
static const char __pyx_k_DEPRECATED240[] = "DEPRECATED240";
static const char __pyx_k_DEPRECATED241[] = "DEPRECATED241";
static const char __pyx_k_DEPRECATED242[] = "DEPRECATED242";
static const char __pyx_k_DEPRECATED243[] = "DEPRECATED243";
static const char __pyx_k_DEPRECATED244[] = "DEPRECATED244";
static const char __pyx_k_DEPRECATED245[] = "DEPRECATED245";
static const char __pyx_k_DEPRECATED246[] = "DEPRECATED246";
static const char __pyx_k_DEPRECATED247[] = "DEPRECATED247";
static const char __pyx_k_DEPRECATED248[] = "DEPRECATED248";
static const char __pyx_k_DEPRECATED249[] = "DEPRECATED249";
static const char __pyx_k_DEPRECATED250[] = "DEPRECATED250";
static const char __pyx_k_DEPRECATED251[] = "DEPRECATED251";
static const char __pyx_k_DEPRECATED252[] = "DEPRECATED252";
static const char __pyx_k_DEPRECATED253[] = "DEPRECATED253";
static const char __pyx_k_DEPRECATED254[] = "DEPRECATED254";
static const char __pyx_k_DEPRECATED255[] = "DEPRECATED255";
static const char __pyx_k_DEPRECATED256[] = "DEPRECATED256";
static const char __pyx_k_DEPRECATED257[] = "DEPRECATED257";
static const char __pyx_k_DEPRECATED258[] = "DEPRECATED258";
static const char __pyx_k_DEPRECATED259[] = "DEPRECATED259";
static const char __pyx_k_DEPRECATED260[] = "DEPRECATED260";
static const char __pyx_k_DEPRECATED261[] = "DEPRECATED261";
static const char __pyx_k_DEPRECATED262[] = "DEPRECATED262";
static const char __pyx_k_DEPRECATED263[] = "DEPRECATED263";
static const char __pyx_k_DEPRECATED264[] = "DEPRECATED264";
static const char __pyx_k_DEPRECATED265[] = "DEPRECATED265";
static const char __pyx_k_DEPRECATED266[] = "DEPRECATED266";
static const char __pyx_k_DEPRECATED267[] = "DEPRECATED267";
static const char __pyx_k_DEPRECATED268[] = "DEPRECATED268";
static const char __pyx_k_DEPRECATED269[] = "DEPRECATED269";
static const char __pyx_k_DEPRECATED270[] = "DEPRECATED270";
static const char __pyx_k_DEPRECATED271[] = "DEPRECATED271";
static const char __pyx_k_DEPRECATED272[] = "DEPRECATED272";
static const char __pyx_k_DEPRECATED273[] = "DEPRECATED273";
static const char __pyx_k_DEPRECATED274[] = "DEPRECATED274";
static const char __pyx_k_DEPRECATED275[] = "DEPRECATED275";
static const char __pyx_k_DEPRECATED276[] = "DEPRECATED276";
static const char __pyx_k_IS_LEFT_PUNCT[] = "IS_LEFT_PUNCT";
static const char __pyx_k_spacy_symbols[] = "spacy.symbols";
static const char __pyx_k_IS_RIGHT_PUNCT[] = "IS_RIGHT_PUNCT";
static const char __pyx_k_IS_OOV_DEPRECATED[] = "IS_OOV_DEPRECATED";
static const char __pyx_k_spacy_symbols_pyx[] = "spacy\\symbols.pyx";
static const char __pyx_k_cline_in_traceback[] = "cline_in_traceback";
static PyObject *__pyx_kp_s_;
static PyObject *__pyx_n_s_ADJ;
static PyObject *__pyx_n_s_ADP;
static PyObject *__pyx_n_s_ADV;
static PyObject *__pyx_n_s_AUX;
static PyObject *__pyx_n_s_CARDINAL;
static PyObject *__pyx_n_s_CCONJ;
static PyObject *__pyx_n_s_CLUSTER;
static PyObject *__pyx_n_s_CONJ;
static PyObject *__pyx_n_s_DATE;
static PyObject *__pyx_n_s_DEP;
static PyObject *__pyx_n_s_DEPRECATED001;
static PyObject *__pyx_n_s_DEPRECATED002;
static PyObject *__pyx_n_s_DEPRECATED003;
static PyObject *__pyx_n_s_DEPRECATED004;
static PyObject *__pyx_n_s_DEPRECATED005;
static PyObject *__pyx_n_s_DEPRECATED006;
static PyObject *__pyx_n_s_DEPRECATED007;
static PyObject *__pyx_n_s_DEPRECATED008;
static PyObject *__pyx_n_s_DEPRECATED009;
static PyObject *__pyx_n_s_DEPRECATED010;
static PyObject *__pyx_n_s_DEPRECATED011;
static PyObject *__pyx_n_s_DEPRECATED012;
static PyObject *__pyx_n_s_DEPRECATED013;
static PyObject *__pyx_n_s_DEPRECATED014;
static PyObject *__pyx_n_s_DEPRECATED015;
static PyObject *__pyx_n_s_DEPRECATED016;
static PyObject *__pyx_n_s_DEPRECATED017;
static PyObject *__pyx_n_s_DEPRECATED018;
static PyObject *__pyx_n_s_DEPRECATED019;
static PyObject *__pyx_n_s_DEPRECATED020;
static PyObject *__pyx_n_s_DEPRECATED021;
static PyObject *__pyx_n_s_DEPRECATED022;
static PyObject *__pyx_n_s_DEPRECATED023;
static PyObject *__pyx_n_s_DEPRECATED024;
static PyObject *__pyx_n_s_DEPRECATED025;
static PyObject *__pyx_n_s_DEPRECATED026;
static PyObject *__pyx_n_s_DEPRECATED027;
static PyObject *__pyx_n_s_DEPRECATED028;
static PyObject *__pyx_n_s_DEPRECATED029;
static PyObject *__pyx_n_s_DEPRECATED030;
static PyObject *__pyx_n_s_DEPRECATED031;
static PyObject *__pyx_n_s_DEPRECATED032;
static PyObject *__pyx_n_s_DEPRECATED033;
static PyObject *__pyx_n_s_DEPRECATED034;
static PyObject *__pyx_n_s_DEPRECATED035;
static PyObject *__pyx_n_s_DEPRECATED036;
static PyObject *__pyx_n_s_DEPRECATED037;
static PyObject *__pyx_n_s_DEPRECATED038;
static PyObject *__pyx_n_s_DEPRECATED039;
static PyObject *__pyx_n_s_DEPRECATED040;
static PyObject *__pyx_n_s_DEPRECATED041;
static PyObject *__pyx_n_s_DEPRECATED042;
static PyObject *__pyx_n_s_DEPRECATED043;
static PyObject *__pyx_n_s_DEPRECATED044;
static PyObject *__pyx_n_s_DEPRECATED045;
static PyObject *__pyx_n_s_DEPRECATED046;
static PyObject *__pyx_n_s_DEPRECATED047;
static PyObject *__pyx_n_s_DEPRECATED048;
static PyObject *__pyx_n_s_DEPRECATED049;
static PyObject *__pyx_n_s_DEPRECATED050;
static PyObject *__pyx_n_s_DEPRECATED051;
static PyObject *__pyx_n_s_DEPRECATED052;
static PyObject *__pyx_n_s_DEPRECATED053;
static PyObject *__pyx_n_s_DEPRECATED054;
static PyObject *__pyx_n_s_DEPRECATED055;
static PyObject *__pyx_n_s_DEPRECATED056;
static PyObject *__pyx_n_s_DEPRECATED057;
static PyObject *__pyx_n_s_DEPRECATED058;
static PyObject *__pyx_n_s_DEPRECATED059;
static PyObject *__pyx_n_s_DEPRECATED060;
static PyObject *__pyx_n_s_DEPRECATED061;
static PyObject *__pyx_n_s_DEPRECATED062;
static PyObject *__pyx_n_s_DEPRECATED063;
static PyObject *__pyx_n_s_DEPRECATED064;
static PyObject *__pyx_n_s_DEPRECATED065;
static PyObject *__pyx_n_s_DEPRECATED066;
static PyObject *__pyx_n_s_DEPRECATED067;
static PyObject *__pyx_n_s_DEPRECATED068;
static PyObject *__pyx_n_s_DEPRECATED069;
static PyObject *__pyx_n_s_DEPRECATED070;
static PyObject *__pyx_n_s_DEPRECATED071;
static PyObject *__pyx_n_s_DEPRECATED072;
static PyObject *__pyx_n_s_DEPRECATED073;
static PyObject *__pyx_n_s_DEPRECATED074;
static PyObject *__pyx_n_s_DEPRECATED075;
static PyObject *__pyx_n_s_DEPRECATED076;
static PyObject *__pyx_n_s_DEPRECATED077;
static PyObject *__pyx_n_s_DEPRECATED078;
static PyObject *__pyx_n_s_DEPRECATED079;
static PyObject *__pyx_n_s_DEPRECATED080;
static PyObject *__pyx_n_s_DEPRECATED081;
static PyObject *__pyx_n_s_DEPRECATED082;
static PyObject *__pyx_n_s_DEPRECATED083;
static PyObject *__pyx_n_s_DEPRECATED084;
static PyObject *__pyx_n_s_DEPRECATED085;
static PyObject *__pyx_n_s_DEPRECATED086;
static PyObject *__pyx_n_s_DEPRECATED087;
static PyObject *__pyx_n_s_DEPRECATED088;
static PyObject *__pyx_n_s_DEPRECATED089;
static PyObject *__pyx_n_s_DEPRECATED090;
static PyObject *__pyx_n_s_DEPRECATED091;
static PyObject *__pyx_n_s_DEPRECATED092;
static PyObject *__pyx_n_s_DEPRECATED093;
static PyObject *__pyx_n_s_DEPRECATED094;
static PyObject *__pyx_n_s_DEPRECATED095;
static PyObject *__pyx_n_s_DEPRECATED096;
static PyObject *__pyx_n_s_DEPRECATED097;
static PyObject *__pyx_n_s_DEPRECATED098;
static PyObject *__pyx_n_s_DEPRECATED099;
static PyObject *__pyx_n_s_DEPRECATED100;
static PyObject *__pyx_n_s_DEPRECATED101;
static PyObject *__pyx_n_s_DEPRECATED102;
static PyObject *__pyx_n_s_DEPRECATED103;
static PyObject *__pyx_n_s_DEPRECATED104;
static PyObject *__pyx_n_s_DEPRECATED105;
static PyObject *__pyx_n_s_DEPRECATED106;
static PyObject *__pyx_n_s_DEPRECATED107;
static PyObject *__pyx_n_s_DEPRECATED108;
static PyObject *__pyx_n_s_DEPRECATED109;
static PyObject *__pyx_n_s_DEPRECATED110;
static PyObject *__pyx_n_s_DEPRECATED111;
static PyObject *__pyx_n_s_DEPRECATED112;
static PyObject *__pyx_n_s_DEPRECATED113;
static PyObject *__pyx_n_s_DEPRECATED114;
static PyObject *__pyx_n_s_DEPRECATED115;
static PyObject *__pyx_n_s_DEPRECATED116;
static PyObject *__pyx_n_s_DEPRECATED117;
static PyObject *__pyx_n_s_DEPRECATED118;
static PyObject *__pyx_n_s_DEPRECATED119;
static PyObject *__pyx_n_s_DEPRECATED120;
static PyObject *__pyx_n_s_DEPRECATED121;
static PyObject *__pyx_n_s_DEPRECATED122;
static PyObject *__pyx_n_s_DEPRECATED123;
static PyObject *__pyx_n_s_DEPRECATED124;
static PyObject *__pyx_n_s_DEPRECATED125;
static PyObject *__pyx_n_s_DEPRECATED126;
static PyObject *__pyx_n_s_DEPRECATED127;
static PyObject *__pyx_n_s_DEPRECATED128;
static PyObject *__pyx_n_s_DEPRECATED129;
static PyObject *__pyx_n_s_DEPRECATED130;
static PyObject *__pyx_n_s_DEPRECATED131;
static PyObject *__pyx_n_s_DEPRECATED132;
static PyObject *__pyx_n_s_DEPRECATED133;
static PyObject *__pyx_n_s_DEPRECATED134;
static PyObject *__pyx_n_s_DEPRECATED135;
static PyObject *__pyx_n_s_DEPRECATED136;
static PyObject *__pyx_n_s_DEPRECATED137;
static PyObject *__pyx_n_s_DEPRECATED138;
static PyObject *__pyx_n_s_DEPRECATED139;
static PyObject *__pyx_n_s_DEPRECATED140;
static PyObject *__pyx_n_s_DEPRECATED141;
static PyObject *__pyx_n_s_DEPRECATED142;
static PyObject *__pyx_n_s_DEPRECATED143;
static PyObject *__pyx_n_s_DEPRECATED144;
static PyObject *__pyx_n_s_DEPRECATED145;
static PyObject *__pyx_n_s_DEPRECATED146;
static PyObject *__pyx_n_s_DEPRECATED147;
static PyObject *__pyx_n_s_DEPRECATED148;
static PyObject *__pyx_n_s_DEPRECATED149;
static PyObject *__pyx_n_s_DEPRECATED150;
static PyObject *__pyx_n_s_DEPRECATED151;
static PyObject *__pyx_n_s_DEPRECATED152;
static PyObject *__pyx_n_s_DEPRECATED153;
static PyObject *__pyx_n_s_DEPRECATED154;
static PyObject *__pyx_n_s_DEPRECATED155;
static PyObject *__pyx_n_s_DEPRECATED156;
static PyObject *__pyx_n_s_DEPRECATED157;
static PyObject *__pyx_n_s_DEPRECATED158;
static PyObject *__pyx_n_s_DEPRECATED159;
static PyObject *__pyx_n_s_DEPRECATED160;
static PyObject *__pyx_n_s_DEPRECATED161;
static PyObject *__pyx_n_s_DEPRECATED162;
static PyObject *__pyx_n_s_DEPRECATED163;
static PyObject *__pyx_n_s_DEPRECATED164;
static PyObject *__pyx_n_s_DEPRECATED165;
static PyObject *__pyx_n_s_DEPRECATED166;
static PyObject *__pyx_n_s_DEPRECATED167;
static PyObject *__pyx_n_s_DEPRECATED168;
static PyObject *__pyx_n_s_DEPRECATED169;
static PyObject *__pyx_n_s_DEPRECATED170;
static PyObject *__pyx_n_s_DEPRECATED171;
static PyObject *__pyx_n_s_DEPRECATED172;
static PyObject *__pyx_n_s_DEPRECATED173;
static PyObject *__pyx_n_s_DEPRECATED174;
static PyObject *__pyx_n_s_DEPRECATED175;
static PyObject *__pyx_n_s_DEPRECATED176;
static PyObject *__pyx_n_s_DEPRECATED177;
static PyObject *__pyx_n_s_DEPRECATED178;
static PyObject *__pyx_n_s_DEPRECATED179;
static PyObject *__pyx_n_s_DEPRECATED180;
static PyObject *__pyx_n_s_DEPRECATED181;
static PyObject *__pyx_n_s_DEPRECATED182;
static PyObject *__pyx_n_s_DEPRECATED183;
static PyObject *__pyx_n_s_DEPRECATED184;
static PyObject *__pyx_n_s_DEPRECATED185;
static PyObject *__pyx_n_s_DEPRECATED186;
static PyObject *__pyx_n_s_DEPRECATED187;
static PyObject *__pyx_n_s_DEPRECATED188;
static PyObject *__pyx_n_s_DEPRECATED189;
static PyObject *__pyx_n_s_DEPRECATED190;
static PyObject *__pyx_n_s_DEPRECATED191;
static PyObject *__pyx_n_s_DEPRECATED192;
static PyObject *__pyx_n_s_DEPRECATED193;
static PyObject *__pyx_n_s_DEPRECATED194;
static PyObject *__pyx_n_s_DEPRECATED195;
static PyObject *__pyx_n_s_DEPRECATED196;
static PyObject *__pyx_n_s_DEPRECATED197;
static PyObject *__pyx_n_s_DEPRECATED198;
static PyObject *__pyx_n_s_DEPRECATED199;
static PyObject *__pyx_n_s_DEPRECATED200;
static PyObject *__pyx_n_s_DEPRECATED201;
static PyObject *__pyx_n_s_DEPRECATED202;
static PyObject *__pyx_n_s_DEPRECATED203;
static PyObject *__pyx_n_s_DEPRECATED204;
static PyObject *__pyx_n_s_DEPRECATED205;
static PyObject *__pyx_n_s_DEPRECATED206;
static PyObject *__pyx_n_s_DEPRECATED207;
static PyObject *__pyx_n_s_DEPRECATED208;
static PyObject *__pyx_n_s_DEPRECATED209;
static PyObject *__pyx_n_s_DEPRECATED210;
static PyObject *__pyx_n_s_DEPRECATED211;
static PyObject *__pyx_n_s_DEPRECATED212;
static PyObject *__pyx_n_s_DEPRECATED213;
static PyObject *__pyx_n_s_DEPRECATED214;
static PyObject *__pyx_n_s_DEPRECATED215;
static PyObject *__pyx_n_s_DEPRECATED216;
static PyObject *__pyx_n_s_DEPRECATED217;
static PyObject *__pyx_n_s_DEPRECATED218;
static PyObject *__pyx_n_s_DEPRECATED219;
static PyObject *__pyx_n_s_DEPRECATED220;
static PyObject *__pyx_n_s_DEPRECATED221;
static PyObject *__pyx_n_s_DEPRECATED222;
static PyObject *__pyx_n_s_DEPRECATED223;
static PyObject *__pyx_n_s_DEPRECATED224;
static PyObject *__pyx_n_s_DEPRECATED225;
static PyObject *__pyx_n_s_DEPRECATED226;
static PyObject *__pyx_n_s_DEPRECATED227;
static PyObject *__pyx_n_s_DEPRECATED228;
static PyObject *__pyx_n_s_DEPRECATED229;
static PyObject *__pyx_n_s_DEPRECATED230;
static PyObject *__pyx_n_s_DEPRECATED231;
static PyObject *__pyx_n_s_DEPRECATED232;
static PyObject *__pyx_n_s_DEPRECATED233;
static PyObject *__pyx_n_s_DEPRECATED234;
static PyObject *__pyx_n_s_DEPRECATED235;
static PyObject *__pyx_n_s_DEPRECATED236;
static PyObject *__pyx_n_s_DEPRECATED237;
static PyObject *__pyx_n_s_DEPRECATED238;
static PyObject *__pyx_n_s_DEPRECATED239;
static PyObject *__pyx_n_s_DEPRECATED240;
static PyObject *__pyx_n_s_DEPRECATED241;
static PyObject *__pyx_n_s_DEPRECATED242;
static PyObject *__pyx_n_s_DEPRECATED243;
static PyObject *__pyx_n_s_DEPRECATED244;
static PyObject *__pyx_n_s_DEPRECATED245;
static PyObject *__pyx_n_s_DEPRECATED246;
static PyObject *__pyx_n_s_DEPRECATED247;
static PyObject *__pyx_n_s_DEPRECATED248;
static PyObject *__pyx_n_s_DEPRECATED249;
static PyObject *__pyx_n_s_DEPRECATED250;
static PyObject *__pyx_n_s_DEPRECATED251;
static PyObject *__pyx_n_s_DEPRECATED252;
static PyObject *__pyx_n_s_DEPRECATED253;
static PyObject *__pyx_n_s_DEPRECATED254;
static PyObject *__pyx_n_s_DEPRECATED255;
static PyObject *__pyx_n_s_DEPRECATED256;
static PyObject *__pyx_n_s_DEPRECATED257;
static PyObject *__pyx_n_s_DEPRECATED258;
static PyObject *__pyx_n_s_DEPRECATED259;
static PyObject *__pyx_n_s_DEPRECATED260;
static PyObject *__pyx_n_s_DEPRECATED261;
static PyObject *__pyx_n_s_DEPRECATED262;
static PyObject *__pyx_n_s_DEPRECATED263;
static PyObject *__pyx_n_s_DEPRECATED264;
static PyObject *__pyx_n_s_DEPRECATED265;
static PyObject *__pyx_n_s_DEPRECATED266;
static PyObject *__pyx_n_s_DEPRECATED267;
static PyObject *__pyx_n_s_DEPRECATED268;
static PyObject *__pyx_n_s_DEPRECATED269;
static PyObject *__pyx_n_s_DEPRECATED270;
static PyObject *__pyx_n_s_DEPRECATED271;
static PyObject *__pyx_n_s_DEPRECATED272;
static PyObject *__pyx_n_s_DEPRECATED273;
static PyObject *__pyx_n_s_DEPRECATED274;
static PyObject *__pyx_n_s_DEPRECATED275;
static PyObject *__pyx_n_s_DEPRECATED276;
static PyObject *__pyx_n_s_DET;
static PyObject *__pyx_n_s_ENT_ID;
static PyObject *__pyx_n_s_ENT_IOB;
static PyObject *__pyx_n_s_ENT_KB_ID;
static PyObject *__pyx_n_s_ENT_TYPE;
static PyObject *__pyx_n_s_EOL;
static PyObject *__pyx_n_s_EVENT;
static PyObject *__pyx_n_s_FACILITY;
static PyObject *__pyx_n_s_FLAG19;
static PyObject *__pyx_n_s_FLAG20;
static PyObject *__pyx_n_s_FLAG21;
static PyObject *__pyx_n_s_FLAG22;
static PyObject *__pyx_n_s_FLAG23;
static PyObject *__pyx_n_s_FLAG24;
static PyObject *__pyx_n_s_FLAG25;
static PyObject *__pyx_n_s_FLAG26;
static PyObject *__pyx_n_s_FLAG27;
static PyObject *__pyx_n_s_FLAG28;
static PyObject *__pyx_n_s_FLAG29;
static PyObject *__pyx_n_s_FLAG30;
static PyObject *__pyx_n_s_FLAG31;
static PyObject *__pyx_n_s_FLAG32;
static PyObject *__pyx_n_s_FLAG33;
static PyObject *__pyx_n_s_FLAG34;
static PyObject *__pyx_n_s_FLAG35;
static PyObject *__pyx_n_s_FLAG36;
static PyObject *__pyx_n_s_FLAG37;
static PyObject *__pyx_n_s_FLAG38;
static PyObject *__pyx_n_s_FLAG39;
static PyObject *__pyx_n_s_FLAG40;
static PyObject *__pyx_n_s_FLAG41;
static PyObject *__pyx_n_s_FLAG42;
static PyObject *__pyx_n_s_FLAG43;
static PyObject *__pyx_n_s_FLAG44;
static PyObject *__pyx_n_s_FLAG45;
static PyObject *__pyx_n_s_FLAG46;
static PyObject *__pyx_n_s_FLAG47;
static PyObject *__pyx_n_s_FLAG48;
static PyObject *__pyx_n_s_FLAG49;
static PyObject *__pyx_n_s_FLAG50;
static PyObject *__pyx_n_s_FLAG51;
static PyObject *__pyx_n_s_FLAG52;
static PyObject *__pyx_n_s_FLAG53;
static PyObject *__pyx_n_s_FLAG54;
static PyObject *__pyx_n_s_FLAG55;
static PyObject *__pyx_n_s_FLAG56;
static PyObject *__pyx_n_s_FLAG57;
static PyObject *__pyx_n_s_FLAG58;
static PyObject *__pyx_n_s_FLAG59;
static PyObject *__pyx_n_s_FLAG60;
static PyObject *__pyx_n_s_FLAG61;
static PyObject *__pyx_n_s_FLAG62;
static PyObject *__pyx_n_s_FLAG63;
static PyObject *__pyx_n_s_GPE;
static PyObject *__pyx_n_s_HEAD;
static PyObject *__pyx_n_s_ID;
static PyObject *__pyx_n_s_IDS;
static PyObject *__pyx_n_s_IDX;
static PyObject *__pyx_n_s_INTJ;
static PyObject *__pyx_n_s_IS_ALPHA;
static PyObject *__pyx_n_s_IS_ASCII;
static PyObject *__pyx_n_s_IS_BRACKET;
static PyObject *__pyx_n_s_IS_CURRENCY;
static PyObject *__pyx_n_s_IS_DIGIT;
static PyObject *__pyx_n_s_IS_LEFT_PUNCT;
static PyObject *__pyx_n_s_IS_LOWER;
static PyObject *__pyx_n_s_IS_OOV_DEPRECATED;
static PyObject *__pyx_n_s_IS_PUNCT;
static PyObject *__pyx_n_s_IS_QUOTE;
static PyObject *__pyx_n_s_IS_RIGHT_PUNCT;
static PyObject *__pyx_n_s_IS_SPACE;
static PyObject *__pyx_n_s_IS_STOP;
static PyObject *__pyx_n_s_IS_TITLE;
static PyObject *__pyx_n_s_IS_UPPER;
static PyObject *__pyx_n_s_LANG;
static PyObject *__pyx_n_s_LANGUAGE;
static PyObject *__pyx_n_s_LAW;
static PyObject *__pyx_n_s_LEMMA;
static PyObject *__pyx_n_s_LENGTH;
static PyObject *__pyx_n_s_LIKE_EMAIL;
static PyObject *__pyx_n_s_LIKE_NUM;
static PyObject *__pyx_n_s_LIKE_URL;
static PyObject *__pyx_n_s_LOC;
static PyObject *__pyx_n_s_LOWER;
static PyObject *__pyx_n_s_MONEY;
static PyObject *__pyx_n_s_MORPH;
static PyObject *__pyx_n_s_NAMES;
static PyObject *__pyx_n_s_NORM;
static PyObject *__pyx_n_s_NORP;
static PyObject *__pyx_n_s_NOUN;
static PyObject *__pyx_n_s_NUM;
static PyObject *__pyx_n_s_ORDINAL;
static PyObject *__pyx_n_s_ORG;
static PyObject *__pyx_n_s_ORTH;
static PyObject *__pyx_n_s_PART;
static PyObject *__pyx_n_s_PERCENT;
static PyObject *__pyx_n_s_PERSON;
static PyObject *__pyx_n_s_POS;
static PyObject *__pyx_n_s_PREFIX;
static PyObject *__pyx_n_s_PROB;
static PyObject *__pyx_n_s_PRODUCT;
static PyObject *__pyx_n_s_PRON;
static PyObject *__pyx_n_s_PROPN;
static PyObject *__pyx_n_s_PUNCT;
static PyObject *__pyx_n_s_QUANTITY;
static PyObject *__pyx_n_s_SCONJ;
static PyObject *__pyx_n_s_SENT_START;
static PyObject *__pyx_n_s_SHAPE;
static PyObject *__pyx_n_s_SPACE;
static PyObject *__pyx_n_s_SPACY;
static PyObject *__pyx_n_s_SUFFIX;
static PyObject *__pyx_n_s_SYM;
static PyObject *__pyx_n_s_TAG;
static PyObject *__pyx_n_s_TIME;
static PyObject *__pyx_n_s_VERB;
static PyObject *__pyx_n_s_WORK_OF_ART;
static PyObject *__pyx_n_s_X;
static PyObject *__pyx_n_s__2;
static PyObject *__pyx_n_s_acl;
static PyObject *__pyx_n_s_acomp;
static PyObject *__pyx_n_s_advcl;
static PyObject *__pyx_n_s_advmod;
static PyObject *__pyx_n_s_agent;
static PyObject *__pyx_n_s_amod;
static PyObject *__pyx_n_s_appos;
static PyObject *__pyx_n_s_attr;
static PyObject *__pyx_n_s_aux;
static PyObject *__pyx_n_s_auxpass;
static PyObject *__pyx_n_s_cc;
static PyObject *__pyx_n_s_ccomp;
static PyObject *__pyx_n_s_cline_in_traceback;
static PyObject *__pyx_n_s_complm;
static PyObject *__pyx_n_s_conj;
static PyObject *__pyx_n_s_cop;
static PyObject *__pyx_n_s_csubj;
static PyObject *__pyx_n_s_csubjpass;
static PyObject *__pyx_n_s_dep;
static PyObject *__pyx_n_s_det;
static PyObject *__pyx_n_s_dobj;
static PyObject *__pyx_n_s_expl;
static PyObject *__pyx_n_s_hmod;
static PyObject *__pyx_n_s_hyph;
static PyObject *__pyx_n_s_infmod;
static PyObject *__pyx_n_s_intj;
static PyObject *__pyx_n_s_iobj;
static PyObject *__pyx_n_s_it;
static PyObject *__pyx_n_s_items;
static PyObject *__pyx_n_s_key;
static PyObject *__pyx_n_s_main;
static PyObject *__pyx_n_s_mark;
static PyObject *__pyx_n_s_meta;
static PyObject *__pyx_n_s_name;
static PyObject *__pyx_n_s_neg;
static PyObject *__pyx_n_s_nmod;
static PyObject *__pyx_n_s_nn;
static PyObject *__pyx_n_s_npadvmod;
static PyObject *__pyx_n_s_nsubj;
static PyObject *__pyx_n_s_nsubjpass;
static PyObject *__pyx_n_s_num;
static PyObject *__pyx_n_s_number;
static PyObject *__pyx_n_s_obj;
static PyObject *__pyx_n_s_obl;
static PyObject *__pyx_n_s_oprd;
static PyObject *__pyx_n_s_parataxis;
static PyObject *__pyx_n_s_partmod;
static PyObject *__pyx_n_s_pcomp;
static PyObject *__pyx_n_s_pobj;
static PyObject *__pyx_n_s_poss;
static PyObject *__pyx_n_s_possessive;
static PyObject *__pyx_n_s_preconj;
static PyObject *__pyx_n_s_prep;
static PyObject *__pyx_n_s_prt;
static PyObject *__pyx_n_s_punct;
static PyObject *__pyx_n_s_quantmod;
static PyObject *__pyx_n_s_rcmod;
static PyObject *__pyx_n_s_relcl;
static PyObject *__pyx_n_s_root;
static PyObject *__pyx_n_s_sort_nums;
static PyObject *__pyx_n_s_sorted;
static PyObject *__pyx_n_s_spacy_symbols;
static PyObject *__pyx_kp_s_spacy_symbols_pyx;
static PyObject *__pyx_n_s_test;
static PyObject *__pyx_n_s_update;
static PyObject *__pyx_n_s_x;
static PyObject *__pyx_n_s_xcomp;
static PyObject *__pyx_pf_5spacy_7symbols_sort_nums(CYTHON_UNUSED PyObject *__pyx_self, PyObject *__pyx_v_x); /* proto */
static PyObject *__pyx_tuple__3;
static PyObject *__pyx_codeobj__4;
/* Late includes */

/* "spacy/symbols.pyx":472
 * 
 * 
 * def sort_nums(x):             # <<<<<<<<<<<<<<
 *     return x[1]
 * 
 */

/* Python wrapper */
static PyObject *__pyx_pw_5spacy_7symbols_1sort_nums(PyObject *__pyx_self, PyObject *__pyx_v_x); /*proto*/
static char __pyx_doc_5spacy_7symbols_sort_nums[] = "sort_nums(x)";
static PyMethodDef __pyx_mdef_5spacy_7symbols_1sort_nums = {"sort_nums", (PyCFunction)__pyx_pw_5spacy_7symbols_1sort_nums, METH_O, __pyx_doc_5spacy_7symbols_sort_nums};
static PyObject *__pyx_pw_5spacy_7symbols_1sort_nums(PyObject *__pyx_self, PyObject *__pyx_v_x) {
  PyObject *__pyx_r = 0;
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("sort_nums (wrapper)", 0);
  __pyx_r = __pyx_pf_5spacy_7symbols_sort_nums(__pyx_self, ((PyObject *)__pyx_v_x));

  /* function exit code */
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

static PyObject *__pyx_pf_5spacy_7symbols_sort_nums(CYTHON_UNUSED PyObject *__pyx_self, PyObject *__pyx_v_x) {
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;
  __Pyx_RefNannySetupContext("sort_nums", 0);

  /* "spacy/symbols.pyx":473
 * 
 * def sort_nums(x):
 *     return x[1]             # <<<<<<<<<<<<<<
 * 
 * 
 */
  __Pyx_XDECREF(__pyx_r);
  __pyx_t_1 = __Pyx_GetItemInt(__pyx_v_x, 1, long, 1, __Pyx_PyInt_From_long, 0, 0, 1); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 473, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_r = __pyx_t_1;
  __pyx_t_1 = 0;
  goto __pyx_L0;

  /* "spacy/symbols.pyx":472
 * 
 * 
 * def sort_nums(x):             # <<<<<<<<<<<<<<
 *     return x[1]
 * 
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_AddTraceback("spacy.symbols.sort_nums", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = NULL;
  __pyx_L0:;
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

static PyMethodDef __pyx_methods[] = {
  {0, 0, 0, 0}
};

#if PY_MAJOR_VERSION >= 3
#if CYTHON_PEP489_MULTI_PHASE_INIT
static PyObject* __pyx_pymod_create(PyObject *spec, PyModuleDef *def); /*proto*/
static int __pyx_pymod_exec_symbols(PyObject* module); /*proto*/
static PyModuleDef_Slot __pyx_moduledef_slots[] = {
  {Py_mod_create, (void*)__pyx_pymod_create},
  {Py_mod_exec, (void*)__pyx_pymod_exec_symbols},
  {0, NULL}
};
#endif

static struct PyModuleDef __pyx_moduledef = {
    PyModuleDef_HEAD_INIT,
    "symbols",
    0, /* m_doc */
  #if CYTHON_PEP489_MULTI_PHASE_INIT
    0, /* m_size */
  #else
    -1, /* m_size */
  #endif
    __pyx_methods /* m_methods */,
  #if CYTHON_PEP489_MULTI_PHASE_INIT
    __pyx_moduledef_slots, /* m_slots */
  #else
    NULL, /* m_reload */
  #endif
    NULL, /* m_traverse */
    NULL, /* m_clear */
    NULL /* m_free */
};
#endif
#ifndef CYTHON_SMALL_CODE
#if defined(__clang__)
    #define CYTHON_SMALL_CODE
#elif defined(__GNUC__) && (__GNUC__ > 4 || (__GNUC__ == 4 && __GNUC_MINOR__ >= 3))
    #define CYTHON_SMALL_CODE __attribute__((cold))
#else
    #define CYTHON_SMALL_CODE
#endif
#endif

static __Pyx_StringTabEntry __pyx_string_tab[] = {
  {&__pyx_kp_s_, __pyx_k_, sizeof(__pyx_k_), 0, 0, 1, 0},
  {&__pyx_n_s_ADJ, __pyx_k_ADJ, sizeof(__pyx_k_ADJ), 0, 0, 1, 1},
  {&__pyx_n_s_ADP, __pyx_k_ADP, sizeof(__pyx_k_ADP), 0, 0, 1, 1},
  {&__pyx_n_s_ADV, __pyx_k_ADV, sizeof(__pyx_k_ADV), 0, 0, 1, 1},
  {&__pyx_n_s_AUX, __pyx_k_AUX, sizeof(__pyx_k_AUX), 0, 0, 1, 1},
  {&__pyx_n_s_CARDINAL, __pyx_k_CARDINAL, sizeof(__pyx_k_CARDINAL), 0, 0, 1, 1},
  {&__pyx_n_s_CCONJ, __pyx_k_CCONJ, sizeof(__pyx_k_CCONJ), 0, 0, 1, 1},
  {&__pyx_n_s_CLUSTER, __pyx_k_CLUSTER, sizeof(__pyx_k_CLUSTER), 0, 0, 1, 1},
  {&__pyx_n_s_CONJ, __pyx_k_CONJ, sizeof(__pyx_k_CONJ), 0, 0, 1, 1},
  {&__pyx_n_s_DATE, __pyx_k_DATE, sizeof(__pyx_k_DATE), 0, 0, 1, 1},
  {&__pyx_n_s_DEP, __pyx_k_DEP, sizeof(__pyx_k_DEP), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED001, __pyx_k_DEPRECATED001, sizeof(__pyx_k_DEPRECATED001), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED002, __pyx_k_DEPRECATED002, sizeof(__pyx_k_DEPRECATED002), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED003, __pyx_k_DEPRECATED003, sizeof(__pyx_k_DEPRECATED003), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED004, __pyx_k_DEPRECATED004, sizeof(__pyx_k_DEPRECATED004), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED005, __pyx_k_DEPRECATED005, sizeof(__pyx_k_DEPRECATED005), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED006, __pyx_k_DEPRECATED006, sizeof(__pyx_k_DEPRECATED006), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED007, __pyx_k_DEPRECATED007, sizeof(__pyx_k_DEPRECATED007), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED008, __pyx_k_DEPRECATED008, sizeof(__pyx_k_DEPRECATED008), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED009, __pyx_k_DEPRECATED009, sizeof(__pyx_k_DEPRECATED009), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED010, __pyx_k_DEPRECATED010, sizeof(__pyx_k_DEPRECATED010), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED011, __pyx_k_DEPRECATED011, sizeof(__pyx_k_DEPRECATED011), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED012, __pyx_k_DEPRECATED012, sizeof(__pyx_k_DEPRECATED012), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED013, __pyx_k_DEPRECATED013, sizeof(__pyx_k_DEPRECATED013), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED014, __pyx_k_DEPRECATED014, sizeof(__pyx_k_DEPRECATED014), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED015, __pyx_k_DEPRECATED015, sizeof(__pyx_k_DEPRECATED015), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED016, __pyx_k_DEPRECATED016, sizeof(__pyx_k_DEPRECATED016), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED017, __pyx_k_DEPRECATED017, sizeof(__pyx_k_DEPRECATED017), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED018, __pyx_k_DEPRECATED018, sizeof(__pyx_k_DEPRECATED018), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED019, __pyx_k_DEPRECATED019, sizeof(__pyx_k_DEPRECATED019), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED020, __pyx_k_DEPRECATED020, sizeof(__pyx_k_DEPRECATED020), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED021, __pyx_k_DEPRECATED021, sizeof(__pyx_k_DEPRECATED021), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED022, __pyx_k_DEPRECATED022, sizeof(__pyx_k_DEPRECATED022), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED023, __pyx_k_DEPRECATED023, sizeof(__pyx_k_DEPRECATED023), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED024, __pyx_k_DEPRECATED024, sizeof(__pyx_k_DEPRECATED024), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED025, __pyx_k_DEPRECATED025, sizeof(__pyx_k_DEPRECATED025), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED026, __pyx_k_DEPRECATED026, sizeof(__pyx_k_DEPRECATED026), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED027, __pyx_k_DEPRECATED027, sizeof(__pyx_k_DEPRECATED027), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED028, __pyx_k_DEPRECATED028, sizeof(__pyx_k_DEPRECATED028), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED029, __pyx_k_DEPRECATED029, sizeof(__pyx_k_DEPRECATED029), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED030, __pyx_k_DEPRECATED030, sizeof(__pyx_k_DEPRECATED030), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED031, __pyx_k_DEPRECATED031, sizeof(__pyx_k_DEPRECATED031), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED032, __pyx_k_DEPRECATED032, sizeof(__pyx_k_DEPRECATED032), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED033, __pyx_k_DEPRECATED033, sizeof(__pyx_k_DEPRECATED033), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED034, __pyx_k_DEPRECATED034, sizeof(__pyx_k_DEPRECATED034), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED035, __pyx_k_DEPRECATED035, sizeof(__pyx_k_DEPRECATED035), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED036, __pyx_k_DEPRECATED036, sizeof(__pyx_k_DEPRECATED036), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED037, __pyx_k_DEPRECATED037, sizeof(__pyx_k_DEPRECATED037), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED038, __pyx_k_DEPRECATED038, sizeof(__pyx_k_DEPRECATED038), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED039, __pyx_k_DEPRECATED039, sizeof(__pyx_k_DEPRECATED039), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED040, __pyx_k_DEPRECATED040, sizeof(__pyx_k_DEPRECATED040), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED041, __pyx_k_DEPRECATED041, sizeof(__pyx_k_DEPRECATED041), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED042, __pyx_k_DEPRECATED042, sizeof(__pyx_k_DEPRECATED042), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED043, __pyx_k_DEPRECATED043, sizeof(__pyx_k_DEPRECATED043), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED044, __pyx_k_DEPRECATED044, sizeof(__pyx_k_DEPRECATED044), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED045, __pyx_k_DEPRECATED045, sizeof(__pyx_k_DEPRECATED045), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED046, __pyx_k_DEPRECATED046, sizeof(__pyx_k_DEPRECATED046), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED047, __pyx_k_DEPRECATED047, sizeof(__pyx_k_DEPRECATED047), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED048, __pyx_k_DEPRECATED048, sizeof(__pyx_k_DEPRECATED048), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED049, __pyx_k_DEPRECATED049, sizeof(__pyx_k_DEPRECATED049), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED050, __pyx_k_DEPRECATED050, sizeof(__pyx_k_DEPRECATED050), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED051, __pyx_k_DEPRECATED051, sizeof(__pyx_k_DEPRECATED051), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED052, __pyx_k_DEPRECATED052, sizeof(__pyx_k_DEPRECATED052), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED053, __pyx_k_DEPRECATED053, sizeof(__pyx_k_DEPRECATED053), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED054, __pyx_k_DEPRECATED054, sizeof(__pyx_k_DEPRECATED054), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED055, __pyx_k_DEPRECATED055, sizeof(__pyx_k_DEPRECATED055), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED056, __pyx_k_DEPRECATED056, sizeof(__pyx_k_DEPRECATED056), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED057, __pyx_k_DEPRECATED057, sizeof(__pyx_k_DEPRECATED057), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED058, __pyx_k_DEPRECATED058, sizeof(__pyx_k_DEPRECATED058), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED059, __pyx_k_DEPRECATED059, sizeof(__pyx_k_DEPRECATED059), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED060, __pyx_k_DEPRECATED060, sizeof(__pyx_k_DEPRECATED060), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED061, __pyx_k_DEPRECATED061, sizeof(__pyx_k_DEPRECATED061), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED062, __pyx_k_DEPRECATED062, sizeof(__pyx_k_DEPRECATED062), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED063, __pyx_k_DEPRECATED063, sizeof(__pyx_k_DEPRECATED063), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED064, __pyx_k_DEPRECATED064, sizeof(__pyx_k_DEPRECATED064), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED065, __pyx_k_DEPRECATED065, sizeof(__pyx_k_DEPRECATED065), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED066, __pyx_k_DEPRECATED066, sizeof(__pyx_k_DEPRECATED066), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED067, __pyx_k_DEPRECATED067, sizeof(__pyx_k_DEPRECATED067), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED068, __pyx_k_DEPRECATED068, sizeof(__pyx_k_DEPRECATED068), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED069, __pyx_k_DEPRECATED069, sizeof(__pyx_k_DEPRECATED069), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED070, __pyx_k_DEPRECATED070, sizeof(__pyx_k_DEPRECATED070), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED071, __pyx_k_DEPRECATED071, sizeof(__pyx_k_DEPRECATED071), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED072, __pyx_k_DEPRECATED072, sizeof(__pyx_k_DEPRECATED072), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED073, __pyx_k_DEPRECATED073, sizeof(__pyx_k_DEPRECATED073), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED074, __pyx_k_DEPRECATED074, sizeof(__pyx_k_DEPRECATED074), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED075, __pyx_k_DEPRECATED075, sizeof(__pyx_k_DEPRECATED075), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED076, __pyx_k_DEPRECATED076, sizeof(__pyx_k_DEPRECATED076), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED077, __pyx_k_DEPRECATED077, sizeof(__pyx_k_DEPRECATED077), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED078, __pyx_k_DEPRECATED078, sizeof(__pyx_k_DEPRECATED078), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED079, __pyx_k_DEPRECATED079, sizeof(__pyx_k_DEPRECATED079), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED080, __pyx_k_DEPRECATED080, sizeof(__pyx_k_DEPRECATED080), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED081, __pyx_k_DEPRECATED081, sizeof(__pyx_k_DEPRECATED081), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED082, __pyx_k_DEPRECATED082, sizeof(__pyx_k_DEPRECATED082), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED083, __pyx_k_DEPRECATED083, sizeof(__pyx_k_DEPRECATED083), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED084, __pyx_k_DEPRECATED084, sizeof(__pyx_k_DEPRECATED084), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED085, __pyx_k_DEPRECATED085, sizeof(__pyx_k_DEPRECATED085), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED086, __pyx_k_DEPRECATED086, sizeof(__pyx_k_DEPRECATED086), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED087, __pyx_k_DEPRECATED087, sizeof(__pyx_k_DEPRECATED087), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED088, __pyx_k_DEPRECATED088, sizeof(__pyx_k_DEPRECATED088), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED089, __pyx_k_DEPRECATED089, sizeof(__pyx_k_DEPRECATED089), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED090, __pyx_k_DEPRECATED090, sizeof(__pyx_k_DEPRECATED090), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED091, __pyx_k_DEPRECATED091, sizeof(__pyx_k_DEPRECATED091), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED092, __pyx_k_DEPRECATED092, sizeof(__pyx_k_DEPRECATED092), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED093, __pyx_k_DEPRECATED093, sizeof(__pyx_k_DEPRECATED093), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED094, __pyx_k_DEPRECATED094, sizeof(__pyx_k_DEPRECATED094), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED095, __pyx_k_DEPRECATED095, sizeof(__pyx_k_DEPRECATED095), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED096, __pyx_k_DEPRECATED096, sizeof(__pyx_k_DEPRECATED096), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED097, __pyx_k_DEPRECATED097, sizeof(__pyx_k_DEPRECATED097), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED098, __pyx_k_DEPRECATED098, sizeof(__pyx_k_DEPRECATED098), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED099, __pyx_k_DEPRECATED099, sizeof(__pyx_k_DEPRECATED099), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED100, __pyx_k_DEPRECATED100, sizeof(__pyx_k_DEPRECATED100), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED101, __pyx_k_DEPRECATED101, sizeof(__pyx_k_DEPRECATED101), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED102, __pyx_k_DEPRECATED102, sizeof(__pyx_k_DEPRECATED102), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED103, __pyx_k_DEPRECATED103, sizeof(__pyx_k_DEPRECATED103), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED104, __pyx_k_DEPRECATED104, sizeof(__pyx_k_DEPRECATED104), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED105, __pyx_k_DEPRECATED105, sizeof(__pyx_k_DEPRECATED105), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED106, __pyx_k_DEPRECATED106, sizeof(__pyx_k_DEPRECATED106), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED107, __pyx_k_DEPRECATED107, sizeof(__pyx_k_DEPRECATED107), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED108, __pyx_k_DEPRECATED108, sizeof(__pyx_k_DEPRECATED108), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED109, __pyx_k_DEPRECATED109, sizeof(__pyx_k_DEPRECATED109), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED110, __pyx_k_DEPRECATED110, sizeof(__pyx_k_DEPRECATED110), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED111, __pyx_k_DEPRECATED111, sizeof(__pyx_k_DEPRECATED111), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED112, __pyx_k_DEPRECATED112, sizeof(__pyx_k_DEPRECATED112), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED113, __pyx_k_DEPRECATED113, sizeof(__pyx_k_DEPRECATED113), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED114, __pyx_k_DEPRECATED114, sizeof(__pyx_k_DEPRECATED114), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED115, __pyx_k_DEPRECATED115, sizeof(__pyx_k_DEPRECATED115), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED116, __pyx_k_DEPRECATED116, sizeof(__pyx_k_DEPRECATED116), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED117, __pyx_k_DEPRECATED117, sizeof(__pyx_k_DEPRECATED117), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED118, __pyx_k_DEPRECATED118, sizeof(__pyx_k_DEPRECATED118), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED119, __pyx_k_DEPRECATED119, sizeof(__pyx_k_DEPRECATED119), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED120, __pyx_k_DEPRECATED120, sizeof(__pyx_k_DEPRECATED120), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED121, __pyx_k_DEPRECATED121, sizeof(__pyx_k_DEPRECATED121), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED122, __pyx_k_DEPRECATED122, sizeof(__pyx_k_DEPRECATED122), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED123, __pyx_k_DEPRECATED123, sizeof(__pyx_k_DEPRECATED123), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED124, __pyx_k_DEPRECATED124, sizeof(__pyx_k_DEPRECATED124), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED125, __pyx_k_DEPRECATED125, sizeof(__pyx_k_DEPRECATED125), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED126, __pyx_k_DEPRECATED126, sizeof(__pyx_k_DEPRECATED126), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED127, __pyx_k_DEPRECATED127, sizeof(__pyx_k_DEPRECATED127), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED128, __pyx_k_DEPRECATED128, sizeof(__pyx_k_DEPRECATED128), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED129, __pyx_k_DEPRECATED129, sizeof(__pyx_k_DEPRECATED129), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED130, __pyx_k_DEPRECATED130, sizeof(__pyx_k_DEPRECATED130), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED131, __pyx_k_DEPRECATED131, sizeof(__pyx_k_DEPRECATED131), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED132, __pyx_k_DEPRECATED132, sizeof(__pyx_k_DEPRECATED132), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED133, __pyx_k_DEPRECATED133, sizeof(__pyx_k_DEPRECATED133), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED134, __pyx_k_DEPRECATED134, sizeof(__pyx_k_DEPRECATED134), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED135, __pyx_k_DEPRECATED135, sizeof(__pyx_k_DEPRECATED135), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED136, __pyx_k_DEPRECATED136, sizeof(__pyx_k_DEPRECATED136), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED137, __pyx_k_DEPRECATED137, sizeof(__pyx_k_DEPRECATED137), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED138, __pyx_k_DEPRECATED138, sizeof(__pyx_k_DEPRECATED138), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED139, __pyx_k_DEPRECATED139, sizeof(__pyx_k_DEPRECATED139), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED140, __pyx_k_DEPRECATED140, sizeof(__pyx_k_DEPRECATED140), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED141, __pyx_k_DEPRECATED141, sizeof(__pyx_k_DEPRECATED141), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED142, __pyx_k_DEPRECATED142, sizeof(__pyx_k_DEPRECATED142), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED143, __pyx_k_DEPRECATED143, sizeof(__pyx_k_DEPRECATED143), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED144, __pyx_k_DEPRECATED144, sizeof(__pyx_k_DEPRECATED144), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED145, __pyx_k_DEPRECATED145, sizeof(__pyx_k_DEPRECATED145), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED146, __pyx_k_DEPRECATED146, sizeof(__pyx_k_DEPRECATED146), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED147, __pyx_k_DEPRECATED147, sizeof(__pyx_k_DEPRECATED147), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED148, __pyx_k_DEPRECATED148, sizeof(__pyx_k_DEPRECATED148), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED149, __pyx_k_DEPRECATED149, sizeof(__pyx_k_DEPRECATED149), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED150, __pyx_k_DEPRECATED150, sizeof(__pyx_k_DEPRECATED150), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED151, __pyx_k_DEPRECATED151, sizeof(__pyx_k_DEPRECATED151), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED152, __pyx_k_DEPRECATED152, sizeof(__pyx_k_DEPRECATED152), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED153, __pyx_k_DEPRECATED153, sizeof(__pyx_k_DEPRECATED153), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED154, __pyx_k_DEPRECATED154, sizeof(__pyx_k_DEPRECATED154), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED155, __pyx_k_DEPRECATED155, sizeof(__pyx_k_DEPRECATED155), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED156, __pyx_k_DEPRECATED156, sizeof(__pyx_k_DEPRECATED156), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED157, __pyx_k_DEPRECATED157, sizeof(__pyx_k_DEPRECATED157), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED158, __pyx_k_DEPRECATED158, sizeof(__pyx_k_DEPRECATED158), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED159, __pyx_k_DEPRECATED159, sizeof(__pyx_k_DEPRECATED159), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED160, __pyx_k_DEPRECATED160, sizeof(__pyx_k_DEPRECATED160), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED161, __pyx_k_DEPRECATED161, sizeof(__pyx_k_DEPRECATED161), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED162, __pyx_k_DEPRECATED162, sizeof(__pyx_k_DEPRECATED162), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED163, __pyx_k_DEPRECATED163, sizeof(__pyx_k_DEPRECATED163), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED164, __pyx_k_DEPRECATED164, sizeof(__pyx_k_DEPRECATED164), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED165, __pyx_k_DEPRECATED165, sizeof(__pyx_k_DEPRECATED165), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED166, __pyx_k_DEPRECATED166, sizeof(__pyx_k_DEPRECATED166), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED167, __pyx_k_DEPRECATED167, sizeof(__pyx_k_DEPRECATED167), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED168, __pyx_k_DEPRECATED168, sizeof(__pyx_k_DEPRECATED168), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED169, __pyx_k_DEPRECATED169, sizeof(__pyx_k_DEPRECATED169), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED170, __pyx_k_DEPRECATED170, sizeof(__pyx_k_DEPRECATED170), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED171, __pyx_k_DEPRECATED171, sizeof(__pyx_k_DEPRECATED171), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED172, __pyx_k_DEPRECATED172, sizeof(__pyx_k_DEPRECATED172), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED173, __pyx_k_DEPRECATED173, sizeof(__pyx_k_DEPRECATED173), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED174, __pyx_k_DEPRECATED174, sizeof(__pyx_k_DEPRECATED174), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED175, __pyx_k_DEPRECATED175, sizeof(__pyx_k_DEPRECATED175), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED176, __pyx_k_DEPRECATED176, sizeof(__pyx_k_DEPRECATED176), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED177, __pyx_k_DEPRECATED177, sizeof(__pyx_k_DEPRECATED177), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED178, __pyx_k_DEPRECATED178, sizeof(__pyx_k_DEPRECATED178), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED179, __pyx_k_DEPRECATED179, sizeof(__pyx_k_DEPRECATED179), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED180, __pyx_k_DEPRECATED180, sizeof(__pyx_k_DEPRECATED180), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED181, __pyx_k_DEPRECATED181, sizeof(__pyx_k_DEPRECATED181), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED182, __pyx_k_DEPRECATED182, sizeof(__pyx_k_DEPRECATED182), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED183, __pyx_k_DEPRECATED183, sizeof(__pyx_k_DEPRECATED183), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED184, __pyx_k_DEPRECATED184, sizeof(__pyx_k_DEPRECATED184), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED185, __pyx_k_DEPRECATED185, sizeof(__pyx_k_DEPRECATED185), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED186, __pyx_k_DEPRECATED186, sizeof(__pyx_k_DEPRECATED186), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED187, __pyx_k_DEPRECATED187, sizeof(__pyx_k_DEPRECATED187), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED188, __pyx_k_DEPRECATED188, sizeof(__pyx_k_DEPRECATED188), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED189, __pyx_k_DEPRECATED189, sizeof(__pyx_k_DEPRECATED189), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED190, __pyx_k_DEPRECATED190, sizeof(__pyx_k_DEPRECATED190), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED191, __pyx_k_DEPRECATED191, sizeof(__pyx_k_DEPRECATED191), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED192, __pyx_k_DEPRECATED192, sizeof(__pyx_k_DEPRECATED192), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED193, __pyx_k_DEPRECATED193, sizeof(__pyx_k_DEPRECATED193), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED194, __pyx_k_DEPRECATED194, sizeof(__pyx_k_DEPRECATED194), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED195, __pyx_k_DEPRECATED195, sizeof(__pyx_k_DEPRECATED195), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED196, __pyx_k_DEPRECATED196, sizeof(__pyx_k_DEPRECATED196), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED197, __pyx_k_DEPRECATED197, sizeof(__pyx_k_DEPRECATED197), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED198, __pyx_k_DEPRECATED198, sizeof(__pyx_k_DEPRECATED198), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED199, __pyx_k_DEPRECATED199, sizeof(__pyx_k_DEPRECATED199), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED200, __pyx_k_DEPRECATED200, sizeof(__pyx_k_DEPRECATED200), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED201, __pyx_k_DEPRECATED201, sizeof(__pyx_k_DEPRECATED201), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED202, __pyx_k_DEPRECATED202, sizeof(__pyx_k_DEPRECATED202), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED203, __pyx_k_DEPRECATED203, sizeof(__pyx_k_DEPRECATED203), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED204, __pyx_k_DEPRECATED204, sizeof(__pyx_k_DEPRECATED204), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED205, __pyx_k_DEPRECATED205, sizeof(__pyx_k_DEPRECATED205), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED206, __pyx_k_DEPRECATED206, sizeof(__pyx_k_DEPRECATED206), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED207, __pyx_k_DEPRECATED207, sizeof(__pyx_k_DEPRECATED207), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED208, __pyx_k_DEPRECATED208, sizeof(__pyx_k_DEPRECATED208), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED209, __pyx_k_DEPRECATED209, sizeof(__pyx_k_DEPRECATED209), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED210, __pyx_k_DEPRECATED210, sizeof(__pyx_k_DEPRECATED210), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED211, __pyx_k_DEPRECATED211, sizeof(__pyx_k_DEPRECATED211), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED212, __pyx_k_DEPRECATED212, sizeof(__pyx_k_DEPRECATED212), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED213, __pyx_k_DEPRECATED213, sizeof(__pyx_k_DEPRECATED213), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED214, __pyx_k_DEPRECATED214, sizeof(__pyx_k_DEPRECATED214), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED215, __pyx_k_DEPRECATED215, sizeof(__pyx_k_DEPRECATED215), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED216, __pyx_k_DEPRECATED216, sizeof(__pyx_k_DEPRECATED216), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED217, __pyx_k_DEPRECATED217, sizeof(__pyx_k_DEPRECATED217), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED218, __pyx_k_DEPRECATED218, sizeof(__pyx_k_DEPRECATED218), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED219, __pyx_k_DEPRECATED219, sizeof(__pyx_k_DEPRECATED219), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED220, __pyx_k_DEPRECATED220, sizeof(__pyx_k_DEPRECATED220), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED221, __pyx_k_DEPRECATED221, sizeof(__pyx_k_DEPRECATED221), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED222, __pyx_k_DEPRECATED222, sizeof(__pyx_k_DEPRECATED222), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED223, __pyx_k_DEPRECATED223, sizeof(__pyx_k_DEPRECATED223), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED224, __pyx_k_DEPRECATED224, sizeof(__pyx_k_DEPRECATED224), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED225, __pyx_k_DEPRECATED225, sizeof(__pyx_k_DEPRECATED225), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED226, __pyx_k_DEPRECATED226, sizeof(__pyx_k_DEPRECATED226), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED227, __pyx_k_DEPRECATED227, sizeof(__pyx_k_DEPRECATED227), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED228, __pyx_k_DEPRECATED228, sizeof(__pyx_k_DEPRECATED228), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED229, __pyx_k_DEPRECATED229, sizeof(__pyx_k_DEPRECATED229), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED230, __pyx_k_DEPRECATED230, sizeof(__pyx_k_DEPRECATED230), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED231, __pyx_k_DEPRECATED231, sizeof(__pyx_k_DEPRECATED231), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED232, __pyx_k_DEPRECATED232, sizeof(__pyx_k_DEPRECATED232), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED233, __pyx_k_DEPRECATED233, sizeof(__pyx_k_DEPRECATED233), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED234, __pyx_k_DEPRECATED234, sizeof(__pyx_k_DEPRECATED234), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED235, __pyx_k_DEPRECATED235, sizeof(__pyx_k_DEPRECATED235), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED236, __pyx_k_DEPRECATED236, sizeof(__pyx_k_DEPRECATED236), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED237, __pyx_k_DEPRECATED237, sizeof(__pyx_k_DEPRECATED237), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED238, __pyx_k_DEPRECATED238, sizeof(__pyx_k_DEPRECATED238), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED239, __pyx_k_DEPRECATED239, sizeof(__pyx_k_DEPRECATED239), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED240, __pyx_k_DEPRECATED240, sizeof(__pyx_k_DEPRECATED240), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED241, __pyx_k_DEPRECATED241, sizeof(__pyx_k_DEPRECATED241), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED242, __pyx_k_DEPRECATED242, sizeof(__pyx_k_DEPRECATED242), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED243, __pyx_k_DEPRECATED243, sizeof(__pyx_k_DEPRECATED243), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED244, __pyx_k_DEPRECATED244, sizeof(__pyx_k_DEPRECATED244), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED245, __pyx_k_DEPRECATED245, sizeof(__pyx_k_DEPRECATED245), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED246, __pyx_k_DEPRECATED246, sizeof(__pyx_k_DEPRECATED246), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED247, __pyx_k_DEPRECATED247, sizeof(__pyx_k_DEPRECATED247), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED248, __pyx_k_DEPRECATED248, sizeof(__pyx_k_DEPRECATED248), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED249, __pyx_k_DEPRECATED249, sizeof(__pyx_k_DEPRECATED249), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED250, __pyx_k_DEPRECATED250, sizeof(__pyx_k_DEPRECATED250), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED251, __pyx_k_DEPRECATED251, sizeof(__pyx_k_DEPRECATED251), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED252, __pyx_k_DEPRECATED252, sizeof(__pyx_k_DEPRECATED252), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED253, __pyx_k_DEPRECATED253, sizeof(__pyx_k_DEPRECATED253), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED254, __pyx_k_DEPRECATED254, sizeof(__pyx_k_DEPRECATED254), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED255, __pyx_k_DEPRECATED255, sizeof(__pyx_k_DEPRECATED255), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED256, __pyx_k_DEPRECATED256, sizeof(__pyx_k_DEPRECATED256), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED257, __pyx_k_DEPRECATED257, sizeof(__pyx_k_DEPRECATED257), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED258, __pyx_k_DEPRECATED258, sizeof(__pyx_k_DEPRECATED258), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED259, __pyx_k_DEPRECATED259, sizeof(__pyx_k_DEPRECATED259), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED260, __pyx_k_DEPRECATED260, sizeof(__pyx_k_DEPRECATED260), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED261, __pyx_k_DEPRECATED261, sizeof(__pyx_k_DEPRECATED261), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED262, __pyx_k_DEPRECATED262, sizeof(__pyx_k_DEPRECATED262), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED263, __pyx_k_DEPRECATED263, sizeof(__pyx_k_DEPRECATED263), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED264, __pyx_k_DEPRECATED264, sizeof(__pyx_k_DEPRECATED264), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED265, __pyx_k_DEPRECATED265, sizeof(__pyx_k_DEPRECATED265), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED266, __pyx_k_DEPRECATED266, sizeof(__pyx_k_DEPRECATED266), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED267, __pyx_k_DEPRECATED267, sizeof(__pyx_k_DEPRECATED267), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED268, __pyx_k_DEPRECATED268, sizeof(__pyx_k_DEPRECATED268), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED269, __pyx_k_DEPRECATED269, sizeof(__pyx_k_DEPRECATED269), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED270, __pyx_k_DEPRECATED270, sizeof(__pyx_k_DEPRECATED270), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED271, __pyx_k_DEPRECATED271, sizeof(__pyx_k_DEPRECATED271), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED272, __pyx_k_DEPRECATED272, sizeof(__pyx_k_DEPRECATED272), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED273, __pyx_k_DEPRECATED273, sizeof(__pyx_k_DEPRECATED273), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED274, __pyx_k_DEPRECATED274, sizeof(__pyx_k_DEPRECATED274), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED275, __pyx_k_DEPRECATED275, sizeof(__pyx_k_DEPRECATED275), 0, 0, 1, 1},
  {&__pyx_n_s_DEPRECATED276, __pyx_k_DEPRECATED276, sizeof(__pyx_k_DEPRECATED276), 0, 0, 1, 1},
  {&__pyx_n_s_DET, __pyx_k_DET, sizeof(__pyx_k_DET), 0, 0, 1, 1},
  {&__pyx_n_s_ENT_ID, __pyx_k_ENT_ID, sizeof(__pyx_k_ENT_ID), 0, 0, 1, 1},
  {&__pyx_n_s_ENT_IOB, __pyx_k_ENT_IOB, sizeof(__pyx_k_ENT_IOB), 0, 0, 1, 1},
  {&__pyx_n_s_ENT_KB_ID, __pyx_k_ENT_KB_ID, sizeof(__pyx_k_ENT_KB_ID), 0, 0, 1, 1},
  {&__pyx_n_s_ENT_TYPE, __pyx_k_ENT_TYPE, sizeof(__pyx_k_ENT_TYPE), 0, 0, 1, 1},
  {&__pyx_n_s_EOL, __pyx_k_EOL, sizeof(__pyx_k_EOL), 0, 0, 1, 1},
  {&__pyx_n_s_EVENT, __pyx_k_EVENT, sizeof(__pyx_k_EVENT), 0, 0, 1, 1},
  {&__pyx_n_s_FACILITY, __pyx_k_FACILITY, sizeof(__pyx_k_FACILITY), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG19, __pyx_k_FLAG19, sizeof(__pyx_k_FLAG19), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG20, __pyx_k_FLAG20, sizeof(__pyx_k_FLAG20), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG21, __pyx_k_FLAG21, sizeof(__pyx_k_FLAG21), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG22, __pyx_k_FLAG22, sizeof(__pyx_k_FLAG22), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG23, __pyx_k_FLAG23, sizeof(__pyx_k_FLAG23), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG24, __pyx_k_FLAG24, sizeof(__pyx_k_FLAG24), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG25, __pyx_k_FLAG25, sizeof(__pyx_k_FLAG25), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG26, __pyx_k_FLAG26, sizeof(__pyx_k_FLAG26), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG27, __pyx_k_FLAG27, sizeof(__pyx_k_FLAG27), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG28, __pyx_k_FLAG28, sizeof(__pyx_k_FLAG28), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG29, __pyx_k_FLAG29, sizeof(__pyx_k_FLAG29), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG30, __pyx_k_FLAG30, sizeof(__pyx_k_FLAG30), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG31, __pyx_k_FLAG31, sizeof(__pyx_k_FLAG31), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG32, __pyx_k_FLAG32, sizeof(__pyx_k_FLAG32), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG33, __pyx_k_FLAG33, sizeof(__pyx_k_FLAG33), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG34, __pyx_k_FLAG34, sizeof(__pyx_k_FLAG34), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG35, __pyx_k_FLAG35, sizeof(__pyx_k_FLAG35), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG36, __pyx_k_FLAG36, sizeof(__pyx_k_FLAG36), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG37, __pyx_k_FLAG37, sizeof(__pyx_k_FLAG37), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG38, __pyx_k_FLAG38, sizeof(__pyx_k_FLAG38), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG39, __pyx_k_FLAG39, sizeof(__pyx_k_FLAG39), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG40, __pyx_k_FLAG40, sizeof(__pyx_k_FLAG40), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG41, __pyx_k_FLAG41, sizeof(__pyx_k_FLAG41), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG42, __pyx_k_FLAG42, sizeof(__pyx_k_FLAG42), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG43, __pyx_k_FLAG43, sizeof(__pyx_k_FLAG43), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG44, __pyx_k_FLAG44, sizeof(__pyx_k_FLAG44), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG45, __pyx_k_FLAG45, sizeof(__pyx_k_FLAG45), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG46, __pyx_k_FLAG46, sizeof(__pyx_k_FLAG46), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG47, __pyx_k_FLAG47, sizeof(__pyx_k_FLAG47), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG48, __pyx_k_FLAG48, sizeof(__pyx_k_FLAG48), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG49, __pyx_k_FLAG49, sizeof(__pyx_k_FLAG49), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG50, __pyx_k_FLAG50, sizeof(__pyx_k_FLAG50), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG51, __pyx_k_FLAG51, sizeof(__pyx_k_FLAG51), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG52, __pyx_k_FLAG52, sizeof(__pyx_k_FLAG52), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG53, __pyx_k_FLAG53, sizeof(__pyx_k_FLAG53), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG54, __pyx_k_FLAG54, sizeof(__pyx_k_FLAG54), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG55, __pyx_k_FLAG55, sizeof(__pyx_k_FLAG55), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG56, __pyx_k_FLAG56, sizeof(__pyx_k_FLAG56), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG57, __pyx_k_FLAG57, sizeof(__pyx_k_FLAG57), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG58, __pyx_k_FLAG58, sizeof(__pyx_k_FLAG58), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG59, __pyx_k_FLAG59, sizeof(__pyx_k_FLAG59), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG60, __pyx_k_FLAG60, sizeof(__pyx_k_FLAG60), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG61, __pyx_k_FLAG61, sizeof(__pyx_k_FLAG61), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG62, __pyx_k_FLAG62, sizeof(__pyx_k_FLAG62), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG63, __pyx_k_FLAG63, sizeof(__pyx_k_FLAG63), 0, 0, 1, 1},
  {&__pyx_n_s_GPE, __pyx_k_GPE, sizeof(__pyx_k_GPE), 0, 0, 1, 1},
  {&__pyx_n_s_HEAD, __pyx_k_HEAD, sizeof(__pyx_k_HEAD), 0, 0, 1, 1},
  {&__pyx_n_s_ID, __pyx_k_ID, sizeof(__pyx_k_ID), 0, 0, 1, 1},
  {&__pyx_n_s_IDS, __pyx_k_IDS, sizeof(__pyx_k_IDS), 0, 0, 1, 1},
  {&__pyx_n_s_IDX, __pyx_k_IDX, sizeof(__pyx_k_IDX), 0, 0, 1, 1},
  {&__pyx_n_s_INTJ, __pyx_k_INTJ, sizeof(__pyx_k_INTJ), 0, 0, 1, 1},
  {&__pyx_n_s_IS_ALPHA, __pyx_k_IS_ALPHA, sizeof(__pyx_k_IS_ALPHA), 0, 0, 1, 1},
  {&__pyx_n_s_IS_ASCII, __pyx_k_IS_ASCII, sizeof(__pyx_k_IS_ASCII), 0, 0, 1, 1},
  {&__pyx_n_s_IS_BRACKET, __pyx_k_IS_BRACKET, sizeof(__pyx_k_IS_BRACKET), 0, 0, 1, 1},
  {&__pyx_n_s_IS_CURRENCY, __pyx_k_IS_CURRENCY, sizeof(__pyx_k_IS_CURRENCY), 0, 0, 1, 1},
  {&__pyx_n_s_IS_DIGIT, __pyx_k_IS_DIGIT, sizeof(__pyx_k_IS_DIGIT), 0, 0, 1, 1},
  {&__pyx_n_s_IS_LEFT_PUNCT, __pyx_k_IS_LEFT_PUNCT, sizeof(__pyx_k_IS_LEFT_PUNCT), 0, 0, 1, 1},
  {&__pyx_n_s_IS_LOWER, __pyx_k_IS_LOWER, sizeof(__pyx_k_IS_LOWER), 0, 0, 1, 1},
  {&__pyx_n_s_IS_OOV_DEPRECATED, __pyx_k_IS_OOV_DEPRECATED, sizeof(__pyx_k_IS_OOV_DEPRECATED), 0, 0, 1, 1},
  {&__pyx_n_s_IS_PUNCT, __pyx_k_IS_PUNCT, sizeof(__pyx_k_IS_PUNCT), 0, 0, 1, 1},
  {&__pyx_n_s_IS_QUOTE, __pyx_k_IS_QUOTE, sizeof(__pyx_k_IS_QUOTE), 0, 0, 1, 1},
  {&__pyx_n_s_IS_RIGHT_PUNCT, __pyx_k_IS_RIGHT_PUNCT, sizeof(__pyx_k_IS_RIGHT_PUNCT), 0, 0, 1, 1},
  {&__pyx_n_s_IS_SPACE, __pyx_k_IS_SPACE, sizeof(__pyx_k_IS_SPACE), 0, 0, 1, 1},
  {&__pyx_n_s_IS_STOP, __pyx_k_IS_STOP, sizeof(__pyx_k_IS_STOP), 0, 0, 1, 1},
  {&__pyx_n_s_IS_TITLE, __pyx_k_IS_TITLE, sizeof(__pyx_k_IS_TITLE), 0, 0, 1, 1},
  {&__pyx_n_s_IS_UPPER, __pyx_k_IS_UPPER, sizeof(__pyx_k_IS_UPPER), 0, 0, 1, 1},
  {&__pyx_n_s_LANG, __pyx_k_LANG, sizeof(__pyx_k_LANG), 0, 0, 1, 1},
  {&__pyx_n_s_LANGUAGE, __pyx_k_LANGUAGE, sizeof(__pyx_k_LANGUAGE), 0, 0, 1, 1},
  {&__pyx_n_s_LAW, __pyx_k_LAW, sizeof(__pyx_k_LAW), 0, 0, 1, 1},
  {&__pyx_n_s_LEMMA, __pyx_k_LEMMA, sizeof(__pyx_k_LEMMA), 0, 0, 1, 1},
  {&__pyx_n_s_LENGTH, __pyx_k_LENGTH, sizeof(__pyx_k_LENGTH), 0, 0, 1, 1},
  {&__pyx_n_s_LIKE_EMAIL, __pyx_k_LIKE_EMAIL, sizeof(__pyx_k_LIKE_EMAIL), 0, 0, 1, 1},
  {&__pyx_n_s_LIKE_NUM, __pyx_k_LIKE_NUM, sizeof(__pyx_k_LIKE_NUM), 0, 0, 1, 1},
  {&__pyx_n_s_LIKE_URL, __pyx_k_LIKE_URL, sizeof(__pyx_k_LIKE_URL), 0, 0, 1, 1},
  {&__pyx_n_s_LOC, __pyx_k_LOC, sizeof(__pyx_k_LOC), 0, 0, 1, 1},
  {&__pyx_n_s_LOWER, __pyx_k_LOWER, sizeof(__pyx_k_LOWER), 0, 0, 1, 1},
  {&__pyx_n_s_MONEY, __pyx_k_MONEY, sizeof(__pyx_k_MONEY), 0, 0, 1, 1},
  {&__pyx_n_s_MORPH, __pyx_k_MORPH, sizeof(__pyx_k_MORPH), 0, 0, 1, 1},
  {&__pyx_n_s_NAMES, __pyx_k_NAMES, sizeof(__pyx_k_NAMES), 0, 0, 1, 1},
  {&__pyx_n_s_NORM, __pyx_k_NORM, sizeof(__pyx_k_NORM), 0, 0, 1, 1},
  {&__pyx_n_s_NORP, __pyx_k_NORP, sizeof(__pyx_k_NORP), 0, 0, 1, 1},
  {&__pyx_n_s_NOUN, __pyx_k_NOUN, sizeof(__pyx_k_NOUN), 0, 0, 1, 1},
  {&__pyx_n_s_NUM, __pyx_k_NUM, sizeof(__pyx_k_NUM), 0, 0, 1, 1},
  {&__pyx_n_s_ORDINAL, __pyx_k_ORDINAL, sizeof(__pyx_k_ORDINAL), 0, 0, 1, 1},
  {&__pyx_n_s_ORG, __pyx_k_ORG, sizeof(__pyx_k_ORG), 0, 0, 1, 1},
  {&__pyx_n_s_ORTH, __pyx_k_ORTH, sizeof(__pyx_k_ORTH), 0, 0, 1, 1},
  {&__pyx_n_s_PART, __pyx_k_PART, sizeof(__pyx_k_PART), 0, 0, 1, 1},
  {&__pyx_n_s_PERCENT, __pyx_k_PERCENT, sizeof(__pyx_k_PERCENT), 0, 0, 1, 1},
  {&__pyx_n_s_PERSON, __pyx_k_PERSON, sizeof(__pyx_k_PERSON), 0, 0, 1, 1},
  {&__pyx_n_s_POS, __pyx_k_POS, sizeof(__pyx_k_POS), 0, 0, 1, 1},
  {&__pyx_n_s_PREFIX, __pyx_k_PREFIX, sizeof(__pyx_k_PREFIX), 0, 0, 1, 1},
  {&__pyx_n_s_PROB, __pyx_k_PROB, sizeof(__pyx_k_PROB), 0, 0, 1, 1},
  {&__pyx_n_s_PRODUCT, __pyx_k_PRODUCT, sizeof(__pyx_k_PRODUCT), 0, 0, 1, 1},
  {&__pyx_n_s_PRON, __pyx_k_PRON, sizeof(__pyx_k_PRON), 0, 0, 1, 1},
  {&__pyx_n_s_PROPN, __pyx_k_PROPN, sizeof(__pyx_k_PROPN), 0, 0, 1, 1},
  {&__pyx_n_s_PUNCT, __pyx_k_PUNCT, sizeof(__pyx_k_PUNCT), 0, 0, 1, 1},
  {&__pyx_n_s_QUANTITY, __pyx_k_QUANTITY, sizeof(__pyx_k_QUANTITY), 0, 0, 1, 1},
  {&__pyx_n_s_SCONJ, __pyx_k_SCONJ, sizeof(__pyx_k_SCONJ), 0, 0, 1, 1},
  {&__pyx_n_s_SENT_START, __pyx_k_SENT_START, sizeof(__pyx_k_SENT_START), 0, 0, 1, 1},
  {&__pyx_n_s_SHAPE, __pyx_k_SHAPE, sizeof(__pyx_k_SHAPE), 0, 0, 1, 1},
  {&__pyx_n_s_SPACE, __pyx_k_SPACE, sizeof(__pyx_k_SPACE), 0, 0, 1, 1},
  {&__pyx_n_s_SPACY, __pyx_k_SPACY, sizeof(__pyx_k_SPACY), 0, 0, 1, 1},
  {&__pyx_n_s_SUFFIX, __pyx_k_SUFFIX, sizeof(__pyx_k_SUFFIX), 0, 0, 1, 1},
  {&__pyx_n_s_SYM, __pyx_k_SYM, sizeof(__pyx_k_SYM), 0, 0, 1, 1},
  {&__pyx_n_s_TAG, __pyx_k_TAG, sizeof(__pyx_k_TAG), 0, 0, 1, 1},
  {&__pyx_n_s_TIME, __pyx_k_TIME, sizeof(__pyx_k_TIME), 0, 0, 1, 1},
  {&__pyx_n_s_VERB, __pyx_k_VERB, sizeof(__pyx_k_VERB), 0, 0, 1, 1},
  {&__pyx_n_s_WORK_OF_ART, __pyx_k_WORK_OF_ART, sizeof(__pyx_k_WORK_OF_ART), 0, 0, 1, 1},
  {&__pyx_n_s_X, __pyx_k_X, sizeof(__pyx_k_X), 0, 0, 1, 1},
  {&__pyx_n_s__2, __pyx_k__2, sizeof(__pyx_k__2), 0, 0, 1, 1},
  {&__pyx_n_s_acl, __pyx_k_acl, sizeof(__pyx_k_acl), 0, 0, 1, 1},
  {&__pyx_n_s_acomp, __pyx_k_acomp, sizeof(__pyx_k_acomp), 0, 0, 1, 1},
  {&__pyx_n_s_advcl, __pyx_k_advcl, sizeof(__pyx_k_advcl), 0, 0, 1, 1},
  {&__pyx_n_s_advmod, __pyx_k_advmod, sizeof(__pyx_k_advmod), 0, 0, 1, 1},
  {&__pyx_n_s_agent, __pyx_k_agent, sizeof(__pyx_k_agent), 0, 0, 1, 1},
  {&__pyx_n_s_amod, __pyx_k_amod, sizeof(__pyx_k_amod), 0, 0, 1, 1},
  {&__pyx_n_s_appos, __pyx_k_appos, sizeof(__pyx_k_appos), 0, 0, 1, 1},
  {&__pyx_n_s_attr, __pyx_k_attr, sizeof(__pyx_k_attr), 0, 0, 1, 1},
  {&__pyx_n_s_aux, __pyx_k_aux, sizeof(__pyx_k_aux), 0, 0, 1, 1},
  {&__pyx_n_s_auxpass, __pyx_k_auxpass, sizeof(__pyx_k_auxpass), 0, 0, 1, 1},
  {&__pyx_n_s_cc, __pyx_k_cc, sizeof(__pyx_k_cc), 0, 0, 1, 1},
  {&__pyx_n_s_ccomp, __pyx_k_ccomp, sizeof(__pyx_k_ccomp), 0, 0, 1, 1},
  {&__pyx_n_s_cline_in_traceback, __pyx_k_cline_in_traceback, sizeof(__pyx_k_cline_in_traceback), 0, 0, 1, 1},
  {&__pyx_n_s_complm, __pyx_k_complm, sizeof(__pyx_k_complm), 0, 0, 1, 1},
  {&__pyx_n_s_conj, __pyx_k_conj, sizeof(__pyx_k_conj), 0, 0, 1, 1},
  {&__pyx_n_s_cop, __pyx_k_cop, sizeof(__pyx_k_cop), 0, 0, 1, 1},
  {&__pyx_n_s_csubj, __pyx_k_csubj, sizeof(__pyx_k_csubj), 0, 0, 1, 1},
  {&__pyx_n_s_csubjpass, __pyx_k_csubjpass, sizeof(__pyx_k_csubjpass), 0, 0, 1, 1},
  {&__pyx_n_s_dep, __pyx_k_dep, sizeof(__pyx_k_dep), 0, 0, 1, 1},
  {&__pyx_n_s_det, __pyx_k_det, sizeof(__pyx_k_det), 0, 0, 1, 1},
  {&__pyx_n_s_dobj, __pyx_k_dobj, sizeof(__pyx_k_dobj), 0, 0, 1, 1},
  {&__pyx_n_s_expl, __pyx_k_expl, sizeof(__pyx_k_expl), 0, 0, 1, 1},
  {&__pyx_n_s_hmod, __pyx_k_hmod, sizeof(__pyx_k_hmod), 0, 0, 1, 1},
  {&__pyx_n_s_hyph, __pyx_k_hyph, sizeof(__pyx_k_hyph), 0, 0, 1, 1},
  {&__pyx_n_s_infmod, __pyx_k_infmod, sizeof(__pyx_k_infmod), 0, 0, 1, 1},
  {&__pyx_n_s_intj, __pyx_k_intj, sizeof(__pyx_k_intj), 0, 0, 1, 1},
  {&__pyx_n_s_iobj, __pyx_k_iobj, sizeof(__pyx_k_iobj), 0, 0, 1, 1},
  {&__pyx_n_s_it, __pyx_k_it, sizeof(__pyx_k_it), 0, 0, 1, 1},
  {&__pyx_n_s_items, __pyx_k_items, sizeof(__pyx_k_items), 0, 0, 1, 1},
  {&__pyx_n_s_key, __pyx_k_key, sizeof(__pyx_k_key), 0, 0, 1, 1},
  {&__pyx_n_s_main, __pyx_k_main, sizeof(__pyx_k_main), 0, 0, 1, 1},
  {&__pyx_n_s_mark, __pyx_k_mark, sizeof(__pyx_k_mark), 0, 0, 1, 1},
  {&__pyx_n_s_meta, __pyx_k_meta, sizeof(__pyx_k_meta), 0, 0, 1, 1},
  {&__pyx_n_s_name, __pyx_k_name, sizeof(__pyx_k_name), 0, 0, 1, 1},
  {&__pyx_n_s_neg, __pyx_k_neg, sizeof(__pyx_k_neg), 0, 0, 1, 1},
  {&__pyx_n_s_nmod, __pyx_k_nmod, sizeof(__pyx_k_nmod), 0, 0, 1, 1},
  {&__pyx_n_s_nn, __pyx_k_nn, sizeof(__pyx_k_nn), 0, 0, 1, 1},
  {&__pyx_n_s_npadvmod, __pyx_k_npadvmod, sizeof(__pyx_k_npadvmod), 0, 0, 1, 1},
  {&__pyx_n_s_nsubj, __pyx_k_nsubj, sizeof(__pyx_k_nsubj), 0, 0, 1, 1},
  {&__pyx_n_s_nsubjpass, __pyx_k_nsubjpass, sizeof(__pyx_k_nsubjpass), 0, 0, 1, 1},
  {&__pyx_n_s_num, __pyx_k_num, sizeof(__pyx_k_num), 0, 0, 1, 1},
  {&__pyx_n_s_number, __pyx_k_number, sizeof(__pyx_k_number), 0, 0, 1, 1},
  {&__pyx_n_s_obj, __pyx_k_obj, sizeof(__pyx_k_obj), 0, 0, 1, 1},
  {&__pyx_n_s_obl, __pyx_k_obl, sizeof(__pyx_k_obl), 0, 0, 1, 1},
  {&__pyx_n_s_oprd, __pyx_k_oprd, sizeof(__pyx_k_oprd), 0, 0, 1, 1},
  {&__pyx_n_s_parataxis, __pyx_k_parataxis, sizeof(__pyx_k_parataxis), 0, 0, 1, 1},
  {&__pyx_n_s_partmod, __pyx_k_partmod, sizeof(__pyx_k_partmod), 0, 0, 1, 1},
  {&__pyx_n_s_pcomp, __pyx_k_pcomp, sizeof(__pyx_k_pcomp), 0, 0, 1, 1},
  {&__pyx_n_s_pobj, __pyx_k_pobj, sizeof(__pyx_k_pobj), 0, 0, 1, 1},
  {&__pyx_n_s_poss, __pyx_k_poss, sizeof(__pyx_k_poss), 0, 0, 1, 1},
  {&__pyx_n_s_possessive, __pyx_k_possessive, sizeof(__pyx_k_possessive), 0, 0, 1, 1},
  {&__pyx_n_s_preconj, __pyx_k_preconj, sizeof(__pyx_k_preconj), 0, 0, 1, 1},
  {&__pyx_n_s_prep, __pyx_k_prep, sizeof(__pyx_k_prep), 0, 0, 1, 1},
  {&__pyx_n_s_prt, __pyx_k_prt, sizeof(__pyx_k_prt), 0, 0, 1, 1},
  {&__pyx_n_s_punct, __pyx_k_punct, sizeof(__pyx_k_punct), 0, 0, 1, 1},
  {&__pyx_n_s_quantmod, __pyx_k_quantmod, sizeof(__pyx_k_quantmod), 0, 0, 1, 1},
  {&__pyx_n_s_rcmod, __pyx_k_rcmod, sizeof(__pyx_k_rcmod), 0, 0, 1, 1},
  {&__pyx_n_s_relcl, __pyx_k_relcl, sizeof(__pyx_k_relcl), 0, 0, 1, 1},
  {&__pyx_n_s_root, __pyx_k_root, sizeof(__pyx_k_root), 0, 0, 1, 1},
  {&__pyx_n_s_sort_nums, __pyx_k_sort_nums, sizeof(__pyx_k_sort_nums), 0, 0, 1, 1},
  {&__pyx_n_s_sorted, __pyx_k_sorted, sizeof(__pyx_k_sorted), 0, 0, 1, 1},
  {&__pyx_n_s_spacy_symbols, __pyx_k_spacy_symbols, sizeof(__pyx_k_spacy_symbols), 0, 0, 1, 1},
  {&__pyx_kp_s_spacy_symbols_pyx, __pyx_k_spacy_symbols_pyx, sizeof(__pyx_k_spacy_symbols_pyx), 0, 0, 1, 0},
  {&__pyx_n_s_test, __pyx_k_test, sizeof(__pyx_k_test), 0, 0, 1, 1},
  {&__pyx_n_s_update, __pyx_k_update, sizeof(__pyx_k_update), 0, 0, 1, 1},
  {&__pyx_n_s_x, __pyx_k_x, sizeof(__pyx_k_x), 0, 0, 1, 1},
  {&__pyx_n_s_xcomp, __pyx_k_xcomp, sizeof(__pyx_k_xcomp), 0, 0, 1, 1},
  {0, 0, 0, 0, 0, 0, 0}
};
static CYTHON_SMALL_CODE int __Pyx_InitCachedBuiltins(void) {
  __pyx_builtin_sorted = __Pyx_GetBuiltinName(__pyx_n_s_sorted); if (!__pyx_builtin_sorted) __PYX_ERR(0, 476, __pyx_L1_error)
  return 0;
  __pyx_L1_error:;
  return -1;
}

static CYTHON_SMALL_CODE int __Pyx_InitCachedConstants(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_InitCachedConstants", 0);

  /* "spacy/symbols.pyx":472
 * 
 * 
 * def sort_nums(x):             # <<<<<<<<<<<<<<
 *     return x[1]
 * 
 */
  __pyx_tuple__3 = PyTuple_Pack(1, __pyx_n_s_x); if (unlikely(!__pyx_tuple__3)) __PYX_ERR(0, 472, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_tuple__3);
  __Pyx_GIVEREF(__pyx_tuple__3);
  __pyx_codeobj__4 = (PyObject*)__Pyx_PyCode_New(1, 0, 1, 0, CO_OPTIMIZED|CO_NEWLOCALS, __pyx_empty_bytes, __pyx_empty_tuple, __pyx_empty_tuple, __pyx_tuple__3, __pyx_empty_tuple, __pyx_empty_tuple, __pyx_kp_s_spacy_symbols_pyx, __pyx_n_s_sort_nums, 472, __pyx_empty_bytes); if (unlikely(!__pyx_codeobj__4)) __PYX_ERR(0, 472, __pyx_L1_error)
  __Pyx_RefNannyFinishContext();
  return 0;
  __pyx_L1_error:;
  __Pyx_RefNannyFinishContext();
  return -1;
}

static CYTHON_SMALL_CODE int __Pyx_InitGlobals(void) {
  if (__Pyx_InitStrings(__pyx_string_tab) < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  return 0;
  __pyx_L1_error:;
  return -1;
}

static CYTHON_SMALL_CODE int __Pyx_modinit_global_init_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_variable_export_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_function_export_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_type_init_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_type_import_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_variable_import_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_function_import_code(void); /*proto*/

static int __Pyx_modinit_global_init_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_global_init_code", 0);
  /*--- Global init code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}

static int __Pyx_modinit_variable_export_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_variable_export_code", 0);
  /*--- Variable export code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}

static int __Pyx_modinit_function_export_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_function_export_code", 0);
  /*--- Function export code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}

static int __Pyx_modinit_type_init_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_type_init_code", 0);
  /*--- Type init code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}

static int __Pyx_modinit_type_import_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_type_import_code", 0);
  /*--- Type import code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}

static int __Pyx_modinit_variable_import_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_variable_import_code", 0);
  /*--- Variable import code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}

static int __Pyx_modinit_function_import_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_function_import_code", 0);
  /*--- Function import code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}


#ifndef CYTHON_NO_PYINIT_EXPORT
#define __Pyx_PyMODINIT_FUNC PyMODINIT_FUNC
#elif PY_MAJOR_VERSION < 3
#ifdef __cplusplus
#define __Pyx_PyMODINIT_FUNC extern "C" void
#else
#define __Pyx_PyMODINIT_FUNC void
#endif
#else
#ifdef __cplusplus
#define __Pyx_PyMODINIT_FUNC extern "C" PyObject *
#else
#define __Pyx_PyMODINIT_FUNC PyObject *
#endif
#endif


#if PY_MAJOR_VERSION < 3
__Pyx_PyMODINIT_FUNC initsymbols(void) CYTHON_SMALL_CODE; /*proto*/
__Pyx_PyMODINIT_FUNC initsymbols(void)
#else
__Pyx_PyMODINIT_FUNC PyInit_symbols(void) CYTHON_SMALL_CODE; /*proto*/
__Pyx_PyMODINIT_FUNC PyInit_symbols(void)
#if CYTHON_PEP489_MULTI_PHASE_INIT
{
  return PyModuleDef_Init(&__pyx_moduledef);
}
static CYTHON_SMALL_CODE int __Pyx_check_single_interpreter(void) {
    #if PY_VERSION_HEX >= 0x030700A1
    static PY_INT64_T main_interpreter_id = -1;
    PY_INT64_T current_id = PyInterpreterState_GetID(PyThreadState_Get()->interp);
    if (main_interpreter_id == -1) {
        main_interpreter_id = current_id;
        return (unlikely(current_id == -1)) ? -1 : 0;
    } else if (unlikely(main_interpreter_id != current_id))
    #else
    static PyInterpreterState *main_interpreter = NULL;
    PyInterpreterState *current_interpreter = PyThreadState_Get()->interp;
    if (!main_interpreter) {
        main_interpreter = current_interpreter;
    } else if (unlikely(main_interpreter != current_interpreter))
    #endif
    {
        PyErr_SetString(
            PyExc_ImportError,
            "Interpreter change detected - this module can only be loaded into one interpreter per process.");
        return -1;
    }
    return 0;
}
static CYTHON_SMALL_CODE int __Pyx_copy_spec_to_module(PyObject *spec, PyObject *moddict, const char* from_name, const char* to_name, int allow_none) {
    PyObject *value = PyObject_GetAttrString(spec, from_name);
    int result = 0;
    if (likely(value)) {
        if (allow_none || value != Py_None) {
            result = PyDict_SetItemString(moddict, to_name, value);
        }
        Py_DECREF(value);
    } else if (PyErr_ExceptionMatches(PyExc_AttributeError)) {
        PyErr_Clear();
    } else {
        result = -1;
    }
    return result;
}
static CYTHON_SMALL_CODE PyObject* __pyx_pymod_create(PyObject *spec, CYTHON_UNUSED PyModuleDef *def) {
    PyObject *module = NULL, *moddict, *modname;
    if (__Pyx_check_single_interpreter())
        return NULL;
    if (__pyx_m)
        return __Pyx_NewRef(__pyx_m);
    modname = PyObject_GetAttrString(spec, "name");
    if (unlikely(!modname)) goto bad;
    module = PyModule_NewObject(modname);
    Py_DECREF(modname);
    if (unlikely(!module)) goto bad;
    moddict = PyModule_GetDict(module);
    if (unlikely(!moddict)) goto bad;
    if (unlikely(__Pyx_copy_spec_to_module(spec, moddict, "loader", "__loader__", 1) < 0)) goto bad;
    if (unlikely(__Pyx_copy_spec_to_module(spec, moddict, "origin", "__file__", 1) < 0)) goto bad;
    if (unlikely(__Pyx_copy_spec_to_module(spec, moddict, "parent", "__package__", 1) < 0)) goto bad;
    if (unlikely(__Pyx_copy_spec_to_module(spec, moddict, "submodule_search_locations", "__path__", 0) < 0)) goto bad;
    return module;
bad:
    Py_XDECREF(module);
    return NULL;
}


static CYTHON_SMALL_CODE int __pyx_pymod_exec_symbols(PyObject *__pyx_pyinit_module)
#endif
#endif
{
  PyObject *__pyx_t_1 = NULL;
  PyObject *__pyx_t_2 = NULL;
  PyObject *__pyx_t_3 = NULL;
  PyObject *__pyx_t_4 = NULL;
  Py_ssize_t __pyx_t_5;
  PyObject *(*__pyx_t_6)(PyObject *);
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;
  __Pyx_RefNannyDeclarations
  #if CYTHON_PEP489_MULTI_PHASE_INIT
  if (__pyx_m) {
    if (__pyx_m == __pyx_pyinit_module) return 0;
    PyErr_SetString(PyExc_RuntimeError, "Module 'symbols' has already been imported. Re-initialisation is not supported.");
    return -1;
  }
  #elif PY_MAJOR_VERSION >= 3
  if (__pyx_m) return __Pyx_NewRef(__pyx_m);
  #endif
  #if CYTHON_REFNANNY
__Pyx_RefNanny = __Pyx_RefNannyImportAPI("refnanny");
if (!__Pyx_RefNanny) {
  PyErr_Clear();
  __Pyx_RefNanny = __Pyx_RefNannyImportAPI("Cython.Runtime.refnanny");
  if (!__Pyx_RefNanny)
      Py_FatalError("failed to import 'refnanny' module");
}
#endif
  __Pyx_RefNannySetupContext("__Pyx_PyMODINIT_FUNC PyInit_symbols(void)", 0);
  if (__Pyx_check_binary_version() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #ifdef __Pxy_PyFrame_Initialize_Offsets
  __Pxy_PyFrame_Initialize_Offsets();
  #endif
  __pyx_empty_tuple = PyTuple_New(0); if (unlikely(!__pyx_empty_tuple)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_empty_bytes = PyBytes_FromStringAndSize("", 0); if (unlikely(!__pyx_empty_bytes)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_empty_unicode = PyUnicode_FromStringAndSize("", 0); if (unlikely(!__pyx_empty_unicode)) __PYX_ERR(0, 1, __pyx_L1_error)
  #ifdef __Pyx_CyFunction_USED
  if (__pyx_CyFunction_init() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  #ifdef __Pyx_FusedFunction_USED
  if (__pyx_FusedFunction_init() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  #ifdef __Pyx_Coroutine_USED
  if (__pyx_Coroutine_init() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  #ifdef __Pyx_Generator_USED
  if (__pyx_Generator_init() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  #ifdef __Pyx_AsyncGen_USED
  if (__pyx_AsyncGen_init() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  #ifdef __Pyx_StopAsyncIteration_USED
  if (__pyx_StopAsyncIteration_init() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  /*--- Library function declarations ---*/
  /*--- Threads initialization code ---*/
  #if defined(WITH_THREAD) && PY_VERSION_HEX < 0x030700F0 && defined(__PYX_FORCE_INIT_THREADS) && __PYX_FORCE_INIT_THREADS
  PyEval_InitThreads();
  #endif
  /*--- Module creation code ---*/
  #if CYTHON_PEP489_MULTI_PHASE_INIT
  __pyx_m = __pyx_pyinit_module;
  Py_INCREF(__pyx_m);
  #else
  #if PY_MAJOR_VERSION < 3
  __pyx_m = Py_InitModule4("symbols", __pyx_methods, 0, 0, PYTHON_API_VERSION); Py_XINCREF(__pyx_m);
  #else
  __pyx_m = PyModule_Create(&__pyx_moduledef);
  #endif
  if (unlikely(!__pyx_m)) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  __pyx_d = PyModule_GetDict(__pyx_m); if (unlikely(!__pyx_d)) __PYX_ERR(0, 1, __pyx_L1_error)
  Py_INCREF(__pyx_d);
  __pyx_b = PyImport_AddModule(__Pyx_BUILTIN_MODULE_NAME); if (unlikely(!__pyx_b)) __PYX_ERR(0, 1, __pyx_L1_error)
  Py_INCREF(__pyx_b);
  __pyx_cython_runtime = PyImport_AddModule((char *) "cython_runtime"); if (unlikely(!__pyx_cython_runtime)) __PYX_ERR(0, 1, __pyx_L1_error)
  Py_INCREF(__pyx_cython_runtime);
  if (PyObject_SetAttrString(__pyx_m, "__builtins__", __pyx_b) < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  /*--- Initialize various global constants etc. ---*/
  if (__Pyx_InitGlobals() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #if PY_MAJOR_VERSION < 3 && (__PYX_DEFAULT_STRING_ENCODING_IS_ASCII || __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT)
  if (__Pyx_init_sys_getdefaultencoding_params() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  if (__pyx_module_is_main_spacy__symbols) {
    if (PyObject_SetAttr(__pyx_m, __pyx_n_s_name, __pyx_n_s_main) < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  }
  #if PY_MAJOR_VERSION >= 3
  {
    PyObject *modules = PyImport_GetModuleDict(); if (unlikely(!modules)) __PYX_ERR(0, 1, __pyx_L1_error)
    if (!PyDict_GetItemString(modules, "spacy.symbols")) {
      if (unlikely(PyDict_SetItemString(modules, "spacy.symbols", __pyx_m) < 0)) __PYX_ERR(0, 1, __pyx_L1_error)
    }
  }
  #endif
  /*--- Builtin init code ---*/
  if (__Pyx_InitCachedBuiltins() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  /*--- Constants init code ---*/
  if (__Pyx_InitCachedConstants() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  /*--- Global type/function init code ---*/
  (void)__Pyx_modinit_global_init_code();
  (void)__Pyx_modinit_variable_export_code();
  (void)__Pyx_modinit_function_export_code();
  (void)__Pyx_modinit_type_init_code();
  (void)__Pyx_modinit_type_import_code();
  (void)__Pyx_modinit_variable_import_code();
  (void)__Pyx_modinit_function_import_code();
  /*--- Execution code ---*/
  #if defined(__Pyx_Generator_USED) || defined(__Pyx_Coroutine_USED)
  if (__Pyx_patch_abc() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif

  /* "spacy/symbols.pyx":3
 * # cython: optimize.unpack_method_calls=False
 * IDS = {
 *     "": NIL,             # <<<<<<<<<<<<<<
 *     "IS_ALPHA": IS_ALPHA,
 *     "IS_ASCII": IS_ASCII,
 */
  __pyx_t_1 = __Pyx_PyDict_NewPresized(457); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_NIL); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_kp_s_, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":4
 * IDS = {
 *     "": NIL,
 *     "IS_ALPHA": IS_ALPHA,             # <<<<<<<<<<<<<<
 *     "IS_ASCII": IS_ASCII,
 *     "IS_DIGIT": IS_DIGIT,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_IS_ALPHA); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 4, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_IS_ALPHA, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":5
 *     "": NIL,
 *     "IS_ALPHA": IS_ALPHA,
 *     "IS_ASCII": IS_ASCII,             # <<<<<<<<<<<<<<
 *     "IS_DIGIT": IS_DIGIT,
 *     "IS_LOWER": IS_LOWER,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_IS_ASCII); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 5, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_IS_ASCII, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":6
 *     "IS_ALPHA": IS_ALPHA,
 *     "IS_ASCII": IS_ASCII,
 *     "IS_DIGIT": IS_DIGIT,             # <<<<<<<<<<<<<<
 *     "IS_LOWER": IS_LOWER,
 *     "IS_PUNCT": IS_PUNCT,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_IS_DIGIT); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_IS_DIGIT, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":7
 *     "IS_ASCII": IS_ASCII,
 *     "IS_DIGIT": IS_DIGIT,
 *     "IS_LOWER": IS_LOWER,             # <<<<<<<<<<<<<<
 *     "IS_PUNCT": IS_PUNCT,
 *     "IS_SPACE": IS_SPACE,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_IS_LOWER); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 7, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_IS_LOWER, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":8
 *     "IS_DIGIT": IS_DIGIT,
 *     "IS_LOWER": IS_LOWER,
 *     "IS_PUNCT": IS_PUNCT,             # <<<<<<<<<<<<<<
 *     "IS_SPACE": IS_SPACE,
 *     "IS_TITLE": IS_TITLE,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_IS_PUNCT); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 8, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_IS_PUNCT, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":9
 *     "IS_LOWER": IS_LOWER,
 *     "IS_PUNCT": IS_PUNCT,
 *     "IS_SPACE": IS_SPACE,             # <<<<<<<<<<<<<<
 *     "IS_TITLE": IS_TITLE,
 *     "IS_UPPER": IS_UPPER,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_IS_SPACE); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 9, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_IS_SPACE, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":10
 *     "IS_PUNCT": IS_PUNCT,
 *     "IS_SPACE": IS_SPACE,
 *     "IS_TITLE": IS_TITLE,             # <<<<<<<<<<<<<<
 *     "IS_UPPER": IS_UPPER,
 *     "LIKE_URL": LIKE_URL,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_IS_TITLE); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 10, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_IS_TITLE, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":11
 *     "IS_SPACE": IS_SPACE,
 *     "IS_TITLE": IS_TITLE,
 *     "IS_UPPER": IS_UPPER,             # <<<<<<<<<<<<<<
 *     "LIKE_URL": LIKE_URL,
 *     "LIKE_NUM": LIKE_NUM,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_IS_UPPER); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 11, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_IS_UPPER, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":12
 *     "IS_TITLE": IS_TITLE,
 *     "IS_UPPER": IS_UPPER,
 *     "LIKE_URL": LIKE_URL,             # <<<<<<<<<<<<<<
 *     "LIKE_NUM": LIKE_NUM,
 *     "LIKE_EMAIL": LIKE_EMAIL,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_LIKE_URL); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 12, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_LIKE_URL, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":13
 *     "IS_UPPER": IS_UPPER,
 *     "LIKE_URL": LIKE_URL,
 *     "LIKE_NUM": LIKE_NUM,             # <<<<<<<<<<<<<<
 *     "LIKE_EMAIL": LIKE_EMAIL,
 *     "IS_STOP": IS_STOP,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_LIKE_NUM); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 13, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_LIKE_NUM, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":14
 *     "LIKE_URL": LIKE_URL,
 *     "LIKE_NUM": LIKE_NUM,
 *     "LIKE_EMAIL": LIKE_EMAIL,             # <<<<<<<<<<<<<<
 *     "IS_STOP": IS_STOP,
 *     "IS_OOV_DEPRECATED": IS_OOV_DEPRECATED,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_LIKE_EMAIL); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 14, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_LIKE_EMAIL, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":15
 *     "LIKE_NUM": LIKE_NUM,
 *     "LIKE_EMAIL": LIKE_EMAIL,
 *     "IS_STOP": IS_STOP,             # <<<<<<<<<<<<<<
 *     "IS_OOV_DEPRECATED": IS_OOV_DEPRECATED,
 *     "IS_BRACKET": IS_BRACKET,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_IS_STOP); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 15, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_IS_STOP, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":16
 *     "LIKE_EMAIL": LIKE_EMAIL,
 *     "IS_STOP": IS_STOP,
 *     "IS_OOV_DEPRECATED": IS_OOV_DEPRECATED,             # <<<<<<<<<<<<<<
 *     "IS_BRACKET": IS_BRACKET,
 *     "IS_QUOTE": IS_QUOTE,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_IS_OOV_DEPRECATED); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 16, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_IS_OOV_DEPRECATED, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":17
 *     "IS_STOP": IS_STOP,
 *     "IS_OOV_DEPRECATED": IS_OOV_DEPRECATED,
 *     "IS_BRACKET": IS_BRACKET,             # <<<<<<<<<<<<<<
 *     "IS_QUOTE": IS_QUOTE,
 *     "IS_LEFT_PUNCT": IS_LEFT_PUNCT,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_IS_BRACKET); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 17, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_IS_BRACKET, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":18
 *     "IS_OOV_DEPRECATED": IS_OOV_DEPRECATED,
 *     "IS_BRACKET": IS_BRACKET,
 *     "IS_QUOTE": IS_QUOTE,             # <<<<<<<<<<<<<<
 *     "IS_LEFT_PUNCT": IS_LEFT_PUNCT,
 *     "IS_RIGHT_PUNCT": IS_RIGHT_PUNCT,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_IS_QUOTE); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 18, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_IS_QUOTE, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":19
 *     "IS_BRACKET": IS_BRACKET,
 *     "IS_QUOTE": IS_QUOTE,
 *     "IS_LEFT_PUNCT": IS_LEFT_PUNCT,             # <<<<<<<<<<<<<<
 *     "IS_RIGHT_PUNCT": IS_RIGHT_PUNCT,
 *     "IS_CURRENCY": IS_CURRENCY,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_IS_LEFT_PUNCT); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 19, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_IS_LEFT_PUNCT, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":20
 *     "IS_QUOTE": IS_QUOTE,
 *     "IS_LEFT_PUNCT": IS_LEFT_PUNCT,
 *     "IS_RIGHT_PUNCT": IS_RIGHT_PUNCT,             # <<<<<<<<<<<<<<
 *     "IS_CURRENCY": IS_CURRENCY,
 * 
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_IS_RIGHT_PUNCT); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 20, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_IS_RIGHT_PUNCT, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":21
 *     "IS_LEFT_PUNCT": IS_LEFT_PUNCT,
 *     "IS_RIGHT_PUNCT": IS_RIGHT_PUNCT,
 *     "IS_CURRENCY": IS_CURRENCY,             # <<<<<<<<<<<<<<
 * 
 *     "FLAG19": FLAG19,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_IS_CURRENCY); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 21, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_IS_CURRENCY, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":23
 *     "IS_CURRENCY": IS_CURRENCY,
 * 
 *     "FLAG19": FLAG19,             # <<<<<<<<<<<<<<
 *     "FLAG20": FLAG20,
 *     "FLAG21": FLAG21,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG19); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 23, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG19, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":24
 * 
 *     "FLAG19": FLAG19,
 *     "FLAG20": FLAG20,             # <<<<<<<<<<<<<<
 *     "FLAG21": FLAG21,
 *     "FLAG22": FLAG22,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG20); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 24, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG20, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":25
 *     "FLAG19": FLAG19,
 *     "FLAG20": FLAG20,
 *     "FLAG21": FLAG21,             # <<<<<<<<<<<<<<
 *     "FLAG22": FLAG22,
 *     "FLAG23": FLAG23,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG21); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 25, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG21, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":26
 *     "FLAG20": FLAG20,
 *     "FLAG21": FLAG21,
 *     "FLAG22": FLAG22,             # <<<<<<<<<<<<<<
 *     "FLAG23": FLAG23,
 *     "FLAG24": FLAG24,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG22); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 26, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG22, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":27
 *     "FLAG21": FLAG21,
 *     "FLAG22": FLAG22,
 *     "FLAG23": FLAG23,             # <<<<<<<<<<<<<<
 *     "FLAG24": FLAG24,
 *     "FLAG25": FLAG25,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG23); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 27, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG23, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":28
 *     "FLAG22": FLAG22,
 *     "FLAG23": FLAG23,
 *     "FLAG24": FLAG24,             # <<<<<<<<<<<<<<
 *     "FLAG25": FLAG25,
 *     "FLAG26": FLAG26,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG24); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 28, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG24, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":29
 *     "FLAG23": FLAG23,
 *     "FLAG24": FLAG24,
 *     "FLAG25": FLAG25,             # <<<<<<<<<<<<<<
 *     "FLAG26": FLAG26,
 *     "FLAG27": FLAG27,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG25); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 29, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG25, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":30
 *     "FLAG24": FLAG24,
 *     "FLAG25": FLAG25,
 *     "FLAG26": FLAG26,             # <<<<<<<<<<<<<<
 *     "FLAG27": FLAG27,
 *     "FLAG28": FLAG28,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG26); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 30, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG26, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":31
 *     "FLAG25": FLAG25,
 *     "FLAG26": FLAG26,
 *     "FLAG27": FLAG27,             # <<<<<<<<<<<<<<
 *     "FLAG28": FLAG28,
 *     "FLAG29": FLAG29,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG27); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 31, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG27, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":32
 *     "FLAG26": FLAG26,
 *     "FLAG27": FLAG27,
 *     "FLAG28": FLAG28,             # <<<<<<<<<<<<<<
 *     "FLAG29": FLAG29,
 *     "FLAG30": FLAG30,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG28); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 32, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG28, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":33
 *     "FLAG27": FLAG27,
 *     "FLAG28": FLAG28,
 *     "FLAG29": FLAG29,             # <<<<<<<<<<<<<<
 *     "FLAG30": FLAG30,
 *     "FLAG31": FLAG31,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG29); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 33, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG29, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":34
 *     "FLAG28": FLAG28,
 *     "FLAG29": FLAG29,
 *     "FLAG30": FLAG30,             # <<<<<<<<<<<<<<
 *     "FLAG31": FLAG31,
 *     "FLAG32": FLAG32,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG30); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 34, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG30, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":35
 *     "FLAG29": FLAG29,
 *     "FLAG30": FLAG30,
 *     "FLAG31": FLAG31,             # <<<<<<<<<<<<<<
 *     "FLAG32": FLAG32,
 *     "FLAG33": FLAG33,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG31); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 35, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG31, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":36
 *     "FLAG30": FLAG30,
 *     "FLAG31": FLAG31,
 *     "FLAG32": FLAG32,             # <<<<<<<<<<<<<<
 *     "FLAG33": FLAG33,
 *     "FLAG34": FLAG34,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG32); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 36, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG32, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":37
 *     "FLAG31": FLAG31,
 *     "FLAG32": FLAG32,
 *     "FLAG33": FLAG33,             # <<<<<<<<<<<<<<
 *     "FLAG34": FLAG34,
 *     "FLAG35": FLAG35,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG33); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 37, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG33, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":38
 *     "FLAG32": FLAG32,
 *     "FLAG33": FLAG33,
 *     "FLAG34": FLAG34,             # <<<<<<<<<<<<<<
 *     "FLAG35": FLAG35,
 *     "FLAG36": FLAG36,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG34); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 38, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG34, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":39
 *     "FLAG33": FLAG33,
 *     "FLAG34": FLAG34,
 *     "FLAG35": FLAG35,             # <<<<<<<<<<<<<<
 *     "FLAG36": FLAG36,
 *     "FLAG37": FLAG37,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG35); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 39, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG35, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":40
 *     "FLAG34": FLAG34,
 *     "FLAG35": FLAG35,
 *     "FLAG36": FLAG36,             # <<<<<<<<<<<<<<
 *     "FLAG37": FLAG37,
 *     "FLAG38": FLAG38,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG36); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 40, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG36, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":41
 *     "FLAG35": FLAG35,
 *     "FLAG36": FLAG36,
 *     "FLAG37": FLAG37,             # <<<<<<<<<<<<<<
 *     "FLAG38": FLAG38,
 *     "FLAG39": FLAG39,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG37); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 41, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG37, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":42
 *     "FLAG36": FLAG36,
 *     "FLAG37": FLAG37,
 *     "FLAG38": FLAG38,             # <<<<<<<<<<<<<<
 *     "FLAG39": FLAG39,
 *     "FLAG40": FLAG40,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG38); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 42, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG38, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":43
 *     "FLAG37": FLAG37,
 *     "FLAG38": FLAG38,
 *     "FLAG39": FLAG39,             # <<<<<<<<<<<<<<
 *     "FLAG40": FLAG40,
 *     "FLAG41": FLAG41,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG39); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 43, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG39, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":44
 *     "FLAG38": FLAG38,
 *     "FLAG39": FLAG39,
 *     "FLAG40": FLAG40,             # <<<<<<<<<<<<<<
 *     "FLAG41": FLAG41,
 *     "FLAG42": FLAG42,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG40); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 44, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG40, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":45
 *     "FLAG39": FLAG39,
 *     "FLAG40": FLAG40,
 *     "FLAG41": FLAG41,             # <<<<<<<<<<<<<<
 *     "FLAG42": FLAG42,
 *     "FLAG43": FLAG43,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG41); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 45, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG41, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":46
 *     "FLAG40": FLAG40,
 *     "FLAG41": FLAG41,
 *     "FLAG42": FLAG42,             # <<<<<<<<<<<<<<
 *     "FLAG43": FLAG43,
 *     "FLAG44": FLAG44,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG42); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 46, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG42, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":47
 *     "FLAG41": FLAG41,
 *     "FLAG42": FLAG42,
 *     "FLAG43": FLAG43,             # <<<<<<<<<<<<<<
 *     "FLAG44": FLAG44,
 *     "FLAG45": FLAG45,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG43); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 47, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG43, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":48
 *     "FLAG42": FLAG42,
 *     "FLAG43": FLAG43,
 *     "FLAG44": FLAG44,             # <<<<<<<<<<<<<<
 *     "FLAG45": FLAG45,
 *     "FLAG46": FLAG46,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG44); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 48, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG44, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":49
 *     "FLAG43": FLAG43,
 *     "FLAG44": FLAG44,
 *     "FLAG45": FLAG45,             # <<<<<<<<<<<<<<
 *     "FLAG46": FLAG46,
 *     "FLAG47": FLAG47,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG45); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 49, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG45, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":50
 *     "FLAG44": FLAG44,
 *     "FLAG45": FLAG45,
 *     "FLAG46": FLAG46,             # <<<<<<<<<<<<<<
 *     "FLAG47": FLAG47,
 *     "FLAG48": FLAG48,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG46); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 50, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG46, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":51
 *     "FLAG45": FLAG45,
 *     "FLAG46": FLAG46,
 *     "FLAG47": FLAG47,             # <<<<<<<<<<<<<<
 *     "FLAG48": FLAG48,
 *     "FLAG49": FLAG49,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG47); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 51, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG47, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":52
 *     "FLAG46": FLAG46,
 *     "FLAG47": FLAG47,
 *     "FLAG48": FLAG48,             # <<<<<<<<<<<<<<
 *     "FLAG49": FLAG49,
 *     "FLAG50": FLAG50,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG48); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 52, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG48, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":53
 *     "FLAG47": FLAG47,
 *     "FLAG48": FLAG48,
 *     "FLAG49": FLAG49,             # <<<<<<<<<<<<<<
 *     "FLAG50": FLAG50,
 *     "FLAG51": FLAG51,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG49); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 53, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG49, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":54
 *     "FLAG48": FLAG48,
 *     "FLAG49": FLAG49,
 *     "FLAG50": FLAG50,             # <<<<<<<<<<<<<<
 *     "FLAG51": FLAG51,
 *     "FLAG52": FLAG52,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG50); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 54, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG50, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":55
 *     "FLAG49": FLAG49,
 *     "FLAG50": FLAG50,
 *     "FLAG51": FLAG51,             # <<<<<<<<<<<<<<
 *     "FLAG52": FLAG52,
 *     "FLAG53": FLAG53,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG51); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 55, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG51, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":56
 *     "FLAG50": FLAG50,
 *     "FLAG51": FLAG51,
 *     "FLAG52": FLAG52,             # <<<<<<<<<<<<<<
 *     "FLAG53": FLAG53,
 *     "FLAG54": FLAG54,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG52); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 56, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG52, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":57
 *     "FLAG51": FLAG51,
 *     "FLAG52": FLAG52,
 *     "FLAG53": FLAG53,             # <<<<<<<<<<<<<<
 *     "FLAG54": FLAG54,
 *     "FLAG55": FLAG55,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG53); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 57, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG53, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":58
 *     "FLAG52": FLAG52,
 *     "FLAG53": FLAG53,
 *     "FLAG54": FLAG54,             # <<<<<<<<<<<<<<
 *     "FLAG55": FLAG55,
 *     "FLAG56": FLAG56,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG54); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 58, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG54, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":59
 *     "FLAG53": FLAG53,
 *     "FLAG54": FLAG54,
 *     "FLAG55": FLAG55,             # <<<<<<<<<<<<<<
 *     "FLAG56": FLAG56,
 *     "FLAG57": FLAG57,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG55); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 59, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG55, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":60
 *     "FLAG54": FLAG54,
 *     "FLAG55": FLAG55,
 *     "FLAG56": FLAG56,             # <<<<<<<<<<<<<<
 *     "FLAG57": FLAG57,
 *     "FLAG58": FLAG58,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG56); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 60, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG56, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":61
 *     "FLAG55": FLAG55,
 *     "FLAG56": FLAG56,
 *     "FLAG57": FLAG57,             # <<<<<<<<<<<<<<
 *     "FLAG58": FLAG58,
 *     "FLAG59": FLAG59,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG57); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 61, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG57, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":62
 *     "FLAG56": FLAG56,
 *     "FLAG57": FLAG57,
 *     "FLAG58": FLAG58,             # <<<<<<<<<<<<<<
 *     "FLAG59": FLAG59,
 *     "FLAG60": FLAG60,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG58); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 62, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG58, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":63
 *     "FLAG57": FLAG57,
 *     "FLAG58": FLAG58,
 *     "FLAG59": FLAG59,             # <<<<<<<<<<<<<<
 *     "FLAG60": FLAG60,
 *     "FLAG61": FLAG61,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG59); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 63, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG59, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":64
 *     "FLAG58": FLAG58,
 *     "FLAG59": FLAG59,
 *     "FLAG60": FLAG60,             # <<<<<<<<<<<<<<
 *     "FLAG61": FLAG61,
 *     "FLAG62": FLAG62,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG60); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 64, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG60, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":65
 *     "FLAG59": FLAG59,
 *     "FLAG60": FLAG60,
 *     "FLAG61": FLAG61,             # <<<<<<<<<<<<<<
 *     "FLAG62": FLAG62,
 *     "FLAG63": FLAG63,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG61); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 65, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG61, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":66
 *     "FLAG60": FLAG60,
 *     "FLAG61": FLAG61,
 *     "FLAG62": FLAG62,             # <<<<<<<<<<<<<<
 *     "FLAG63": FLAG63,
 * 
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG62); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 66, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG62, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":67
 *     "FLAG61": FLAG61,
 *     "FLAG62": FLAG62,
 *     "FLAG63": FLAG63,             # <<<<<<<<<<<<<<
 * 
 *     "ID": ID,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FLAG63); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 67, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FLAG63, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":69
 *     "FLAG63": FLAG63,
 * 
 *     "ID": ID,             # <<<<<<<<<<<<<<
 *     "ORTH": ORTH,
 *     "LOWER": LOWER,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_ID); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 69, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_ID, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":70
 * 
 *     "ID": ID,
 *     "ORTH": ORTH,             # <<<<<<<<<<<<<<
 *     "LOWER": LOWER,
 *     "NORM": NORM,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_ORTH); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 70, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_ORTH, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":71
 *     "ID": ID,
 *     "ORTH": ORTH,
 *     "LOWER": LOWER,             # <<<<<<<<<<<<<<
 *     "NORM": NORM,
 *     "SHAPE": SHAPE,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_LOWER); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 71, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_LOWER, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":72
 *     "ORTH": ORTH,
 *     "LOWER": LOWER,
 *     "NORM": NORM,             # <<<<<<<<<<<<<<
 *     "SHAPE": SHAPE,
 *     "PREFIX": PREFIX,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_NORM); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 72, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_NORM, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":73
 *     "LOWER": LOWER,
 *     "NORM": NORM,
 *     "SHAPE": SHAPE,             # <<<<<<<<<<<<<<
 *     "PREFIX": PREFIX,
 *     "SUFFIX": SUFFIX,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_SHAPE); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 73, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_SHAPE, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":74
 *     "NORM": NORM,
 *     "SHAPE": SHAPE,
 *     "PREFIX": PREFIX,             # <<<<<<<<<<<<<<
 *     "SUFFIX": SUFFIX,
 * 
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_PREFIX); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 74, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_PREFIX, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":75
 *     "SHAPE": SHAPE,
 *     "PREFIX": PREFIX,
 *     "SUFFIX": SUFFIX,             # <<<<<<<<<<<<<<
 * 
 *     "LENGTH": LENGTH,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_SUFFIX); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 75, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_SUFFIX, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":77
 *     "SUFFIX": SUFFIX,
 * 
 *     "LENGTH": LENGTH,             # <<<<<<<<<<<<<<
 *     "CLUSTER": CLUSTER,
 *     "LEMMA": LEMMA,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_LENGTH); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 77, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_LENGTH, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":78
 * 
 *     "LENGTH": LENGTH,
 *     "CLUSTER": CLUSTER,             # <<<<<<<<<<<<<<
 *     "LEMMA": LEMMA,
 *     "POS": POS,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_CLUSTER); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 78, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_CLUSTER, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":79
 *     "LENGTH": LENGTH,
 *     "CLUSTER": CLUSTER,
 *     "LEMMA": LEMMA,             # <<<<<<<<<<<<<<
 *     "POS": POS,
 *     "TAG": TAG,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_LEMMA); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 79, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_LEMMA, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":80
 *     "CLUSTER": CLUSTER,
 *     "LEMMA": LEMMA,
 *     "POS": POS,             # <<<<<<<<<<<<<<
 *     "TAG": TAG,
 *     "DEP": DEP,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_POS); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 80, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_POS, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":81
 *     "LEMMA": LEMMA,
 *     "POS": POS,
 *     "TAG": TAG,             # <<<<<<<<<<<<<<
 *     "DEP": DEP,
 *     "ENT_IOB": ENT_IOB,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_TAG); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 81, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_TAG, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":82
 *     "POS": POS,
 *     "TAG": TAG,
 *     "DEP": DEP,             # <<<<<<<<<<<<<<
 *     "ENT_IOB": ENT_IOB,
 *     "ENT_TYPE": ENT_TYPE,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEP); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 82, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEP, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":83
 *     "TAG": TAG,
 *     "DEP": DEP,
 *     "ENT_IOB": ENT_IOB,             # <<<<<<<<<<<<<<
 *     "ENT_TYPE": ENT_TYPE,
 *     "ENT_ID": ENT_ID,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_ENT_IOB); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 83, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_ENT_IOB, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":84
 *     "DEP": DEP,
 *     "ENT_IOB": ENT_IOB,
 *     "ENT_TYPE": ENT_TYPE,             # <<<<<<<<<<<<<<
 *     "ENT_ID": ENT_ID,
 *     "ENT_KB_ID": ENT_KB_ID,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_ENT_TYPE); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 84, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_ENT_TYPE, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":85
 *     "ENT_IOB": ENT_IOB,
 *     "ENT_TYPE": ENT_TYPE,
 *     "ENT_ID": ENT_ID,             # <<<<<<<<<<<<<<
 *     "ENT_KB_ID": ENT_KB_ID,
 *     "HEAD": HEAD,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_ENT_ID); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 85, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_ENT_ID, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":86
 *     "ENT_TYPE": ENT_TYPE,
 *     "ENT_ID": ENT_ID,
 *     "ENT_KB_ID": ENT_KB_ID,             # <<<<<<<<<<<<<<
 *     "HEAD": HEAD,
 *     "SENT_START": SENT_START,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_ENT_KB_ID); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 86, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_ENT_KB_ID, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":87
 *     "ENT_ID": ENT_ID,
 *     "ENT_KB_ID": ENT_KB_ID,
 *     "HEAD": HEAD,             # <<<<<<<<<<<<<<
 *     "SENT_START": SENT_START,
 *     "SPACY": SPACY,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_HEAD); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 87, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_HEAD, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":88
 *     "ENT_KB_ID": ENT_KB_ID,
 *     "HEAD": HEAD,
 *     "SENT_START": SENT_START,             # <<<<<<<<<<<<<<
 *     "SPACY": SPACY,
 *     "PROB": PROB,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_SENT_START); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 88, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_SENT_START, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":89
 *     "HEAD": HEAD,
 *     "SENT_START": SENT_START,
 *     "SPACY": SPACY,             # <<<<<<<<<<<<<<
 *     "PROB": PROB,
 *     "LANG": LANG,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_SPACY); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 89, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_SPACY, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":90
 *     "SENT_START": SENT_START,
 *     "SPACY": SPACY,
 *     "PROB": PROB,             # <<<<<<<<<<<<<<
 *     "LANG": LANG,
 *     "IDX": IDX,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_PROB); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 90, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_PROB, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":91
 *     "SPACY": SPACY,
 *     "PROB": PROB,
 *     "LANG": LANG,             # <<<<<<<<<<<<<<
 *     "IDX": IDX,
 * 
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_LANG); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 91, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_LANG, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":92
 *     "PROB": PROB,
 *     "LANG": LANG,
 *     "IDX": IDX,             # <<<<<<<<<<<<<<
 * 
 *     "ADJ": ADJ,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_IDX); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 92, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_IDX, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":94
 *     "IDX": IDX,
 * 
 *     "ADJ": ADJ,             # <<<<<<<<<<<<<<
 *     "ADP": ADP,
 *     "ADV": ADV,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_ADJ); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 94, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_ADJ, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":95
 * 
 *     "ADJ": ADJ,
 *     "ADP": ADP,             # <<<<<<<<<<<<<<
 *     "ADV": ADV,
 *     "AUX": AUX,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_ADP); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 95, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_ADP, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":96
 *     "ADJ": ADJ,
 *     "ADP": ADP,
 *     "ADV": ADV,             # <<<<<<<<<<<<<<
 *     "AUX": AUX,
 *     "CONJ": CONJ,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_ADV); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 96, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_ADV, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":97
 *     "ADP": ADP,
 *     "ADV": ADV,
 *     "AUX": AUX,             # <<<<<<<<<<<<<<
 *     "CONJ": CONJ,
 *     "CCONJ": CCONJ, # U20
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_AUX); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 97, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_AUX, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":98
 *     "ADV": ADV,
 *     "AUX": AUX,
 *     "CONJ": CONJ,             # <<<<<<<<<<<<<<
 *     "CCONJ": CCONJ, # U20
 *     "DET": DET,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_CONJ); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 98, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_CONJ, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":99
 *     "AUX": AUX,
 *     "CONJ": CONJ,
 *     "CCONJ": CCONJ, # U20             # <<<<<<<<<<<<<<
 *     "DET": DET,
 *     "INTJ": INTJ,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_CCONJ); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 99, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_CCONJ, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":100
 *     "CONJ": CONJ,
 *     "CCONJ": CCONJ, # U20
 *     "DET": DET,             # <<<<<<<<<<<<<<
 *     "INTJ": INTJ,
 *     "NOUN": NOUN,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DET); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 100, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DET, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":101
 *     "CCONJ": CCONJ, # U20
 *     "DET": DET,
 *     "INTJ": INTJ,             # <<<<<<<<<<<<<<
 *     "NOUN": NOUN,
 *     "NUM": NUM,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_INTJ); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 101, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_INTJ, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":102
 *     "DET": DET,
 *     "INTJ": INTJ,
 *     "NOUN": NOUN,             # <<<<<<<<<<<<<<
 *     "NUM": NUM,
 *     "PART": PART,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_NOUN); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 102, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_NOUN, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":103
 *     "INTJ": INTJ,
 *     "NOUN": NOUN,
 *     "NUM": NUM,             # <<<<<<<<<<<<<<
 *     "PART": PART,
 *     "PRON": PRON,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_NUM); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 103, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_NUM, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":104
 *     "NOUN": NOUN,
 *     "NUM": NUM,
 *     "PART": PART,             # <<<<<<<<<<<<<<
 *     "PRON": PRON,
 *     "PROPN": PROPN,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_PART); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 104, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_PART, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":105
 *     "NUM": NUM,
 *     "PART": PART,
 *     "PRON": PRON,             # <<<<<<<<<<<<<<
 *     "PROPN": PROPN,
 *     "PUNCT": PUNCT,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_PRON); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 105, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_PRON, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":106
 *     "PART": PART,
 *     "PRON": PRON,
 *     "PROPN": PROPN,             # <<<<<<<<<<<<<<
 *     "PUNCT": PUNCT,
 *     "SCONJ": SCONJ,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_PROPN); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 106, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_PROPN, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":107
 *     "PRON": PRON,
 *     "PROPN": PROPN,
 *     "PUNCT": PUNCT,             # <<<<<<<<<<<<<<
 *     "SCONJ": SCONJ,
 *     "SYM": SYM,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_PUNCT); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 107, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_PUNCT, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":108
 *     "PROPN": PROPN,
 *     "PUNCT": PUNCT,
 *     "SCONJ": SCONJ,             # <<<<<<<<<<<<<<
 *     "SYM": SYM,
 *     "VERB": VERB,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_SCONJ); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 108, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_SCONJ, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":109
 *     "PUNCT": PUNCT,
 *     "SCONJ": SCONJ,
 *     "SYM": SYM,             # <<<<<<<<<<<<<<
 *     "VERB": VERB,
 *     "X": X,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_SYM); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 109, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_SYM, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":110
 *     "SCONJ": SCONJ,
 *     "SYM": SYM,
 *     "VERB": VERB,             # <<<<<<<<<<<<<<
 *     "X": X,
 *     "EOL": EOL,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_VERB); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 110, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_VERB, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":111
 *     "SYM": SYM,
 *     "VERB": VERB,
 *     "X": X,             # <<<<<<<<<<<<<<
 *     "EOL": EOL,
 *     "SPACE": SPACE,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_X); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 111, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_X, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":112
 *     "VERB": VERB,
 *     "X": X,
 *     "EOL": EOL,             # <<<<<<<<<<<<<<
 *     "SPACE": SPACE,
 * 
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_EOL); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 112, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_EOL, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":113
 *     "X": X,
 *     "EOL": EOL,
 *     "SPACE": SPACE,             # <<<<<<<<<<<<<<
 * 
 *     "DEPRECATED001": DEPRECATED001,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_SPACE); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 113, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_SPACE, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":115
 *     "SPACE": SPACE,
 * 
 *     "DEPRECATED001": DEPRECATED001,             # <<<<<<<<<<<<<<
 *     "DEPRECATED002": DEPRECATED002,
 *     "DEPRECATED003": DEPRECATED003,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED001); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 115, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED001, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":116
 * 
 *     "DEPRECATED001": DEPRECATED001,
 *     "DEPRECATED002": DEPRECATED002,             # <<<<<<<<<<<<<<
 *     "DEPRECATED003": DEPRECATED003,
 *     "DEPRECATED004": DEPRECATED004,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED002); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 116, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED002, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":117
 *     "DEPRECATED001": DEPRECATED001,
 *     "DEPRECATED002": DEPRECATED002,
 *     "DEPRECATED003": DEPRECATED003,             # <<<<<<<<<<<<<<
 *     "DEPRECATED004": DEPRECATED004,
 *     "DEPRECATED005": DEPRECATED005,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED003); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 117, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED003, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":118
 *     "DEPRECATED002": DEPRECATED002,
 *     "DEPRECATED003": DEPRECATED003,
 *     "DEPRECATED004": DEPRECATED004,             # <<<<<<<<<<<<<<
 *     "DEPRECATED005": DEPRECATED005,
 *     "DEPRECATED006": DEPRECATED006,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED004); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 118, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED004, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":119
 *     "DEPRECATED003": DEPRECATED003,
 *     "DEPRECATED004": DEPRECATED004,
 *     "DEPRECATED005": DEPRECATED005,             # <<<<<<<<<<<<<<
 *     "DEPRECATED006": DEPRECATED006,
 *     "DEPRECATED007": DEPRECATED007,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED005); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 119, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED005, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":120
 *     "DEPRECATED004": DEPRECATED004,
 *     "DEPRECATED005": DEPRECATED005,
 *     "DEPRECATED006": DEPRECATED006,             # <<<<<<<<<<<<<<
 *     "DEPRECATED007": DEPRECATED007,
 *     "DEPRECATED008": DEPRECATED008,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED006); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 120, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED006, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":121
 *     "DEPRECATED005": DEPRECATED005,
 *     "DEPRECATED006": DEPRECATED006,
 *     "DEPRECATED007": DEPRECATED007,             # <<<<<<<<<<<<<<
 *     "DEPRECATED008": DEPRECATED008,
 *     "DEPRECATED009": DEPRECATED009,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED007); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 121, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED007, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":122
 *     "DEPRECATED006": DEPRECATED006,
 *     "DEPRECATED007": DEPRECATED007,
 *     "DEPRECATED008": DEPRECATED008,             # <<<<<<<<<<<<<<
 *     "DEPRECATED009": DEPRECATED009,
 *     "DEPRECATED010": DEPRECATED010,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED008); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 122, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED008, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":123
 *     "DEPRECATED007": DEPRECATED007,
 *     "DEPRECATED008": DEPRECATED008,
 *     "DEPRECATED009": DEPRECATED009,             # <<<<<<<<<<<<<<
 *     "DEPRECATED010": DEPRECATED010,
 *     "DEPRECATED011": DEPRECATED011,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED009); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 123, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED009, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":124
 *     "DEPRECATED008": DEPRECATED008,
 *     "DEPRECATED009": DEPRECATED009,
 *     "DEPRECATED010": DEPRECATED010,             # <<<<<<<<<<<<<<
 *     "DEPRECATED011": DEPRECATED011,
 *     "DEPRECATED012": DEPRECATED012,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED010); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 124, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED010, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":125
 *     "DEPRECATED009": DEPRECATED009,
 *     "DEPRECATED010": DEPRECATED010,
 *     "DEPRECATED011": DEPRECATED011,             # <<<<<<<<<<<<<<
 *     "DEPRECATED012": DEPRECATED012,
 *     "DEPRECATED013": DEPRECATED013,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED011); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 125, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED011, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":126
 *     "DEPRECATED010": DEPRECATED010,
 *     "DEPRECATED011": DEPRECATED011,
 *     "DEPRECATED012": DEPRECATED012,             # <<<<<<<<<<<<<<
 *     "DEPRECATED013": DEPRECATED013,
 *     "DEPRECATED014": DEPRECATED014,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED012); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 126, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED012, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":127
 *     "DEPRECATED011": DEPRECATED011,
 *     "DEPRECATED012": DEPRECATED012,
 *     "DEPRECATED013": DEPRECATED013,             # <<<<<<<<<<<<<<
 *     "DEPRECATED014": DEPRECATED014,
 *     "DEPRECATED015": DEPRECATED015,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED013); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 127, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED013, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":128
 *     "DEPRECATED012": DEPRECATED012,
 *     "DEPRECATED013": DEPRECATED013,
 *     "DEPRECATED014": DEPRECATED014,             # <<<<<<<<<<<<<<
 *     "DEPRECATED015": DEPRECATED015,
 *     "DEPRECATED016": DEPRECATED016,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED014); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 128, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED014, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":129
 *     "DEPRECATED013": DEPRECATED013,
 *     "DEPRECATED014": DEPRECATED014,
 *     "DEPRECATED015": DEPRECATED015,             # <<<<<<<<<<<<<<
 *     "DEPRECATED016": DEPRECATED016,
 *     "DEPRECATED017": DEPRECATED017,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED015); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 129, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED015, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":130
 *     "DEPRECATED014": DEPRECATED014,
 *     "DEPRECATED015": DEPRECATED015,
 *     "DEPRECATED016": DEPRECATED016,             # <<<<<<<<<<<<<<
 *     "DEPRECATED017": DEPRECATED017,
 *     "DEPRECATED018": DEPRECATED018,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED016); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 130, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED016, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":131
 *     "DEPRECATED015": DEPRECATED015,
 *     "DEPRECATED016": DEPRECATED016,
 *     "DEPRECATED017": DEPRECATED017,             # <<<<<<<<<<<<<<
 *     "DEPRECATED018": DEPRECATED018,
 *     "DEPRECATED019": DEPRECATED019,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED017); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 131, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED017, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":132
 *     "DEPRECATED016": DEPRECATED016,
 *     "DEPRECATED017": DEPRECATED017,
 *     "DEPRECATED018": DEPRECATED018,             # <<<<<<<<<<<<<<
 *     "DEPRECATED019": DEPRECATED019,
 *     "DEPRECATED020": DEPRECATED020,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED018); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 132, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED018, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":133
 *     "DEPRECATED017": DEPRECATED017,
 *     "DEPRECATED018": DEPRECATED018,
 *     "DEPRECATED019": DEPRECATED019,             # <<<<<<<<<<<<<<
 *     "DEPRECATED020": DEPRECATED020,
 *     "DEPRECATED021": DEPRECATED021,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED019); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 133, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED019, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":134
 *     "DEPRECATED018": DEPRECATED018,
 *     "DEPRECATED019": DEPRECATED019,
 *     "DEPRECATED020": DEPRECATED020,             # <<<<<<<<<<<<<<
 *     "DEPRECATED021": DEPRECATED021,
 *     "DEPRECATED022": DEPRECATED022,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED020); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 134, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED020, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":135
 *     "DEPRECATED019": DEPRECATED019,
 *     "DEPRECATED020": DEPRECATED020,
 *     "DEPRECATED021": DEPRECATED021,             # <<<<<<<<<<<<<<
 *     "DEPRECATED022": DEPRECATED022,
 *     "DEPRECATED023": DEPRECATED023,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED021); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 135, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED021, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":136
 *     "DEPRECATED020": DEPRECATED020,
 *     "DEPRECATED021": DEPRECATED021,
 *     "DEPRECATED022": DEPRECATED022,             # <<<<<<<<<<<<<<
 *     "DEPRECATED023": DEPRECATED023,
 *     "DEPRECATED024": DEPRECATED024,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED022); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 136, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED022, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":137
 *     "DEPRECATED021": DEPRECATED021,
 *     "DEPRECATED022": DEPRECATED022,
 *     "DEPRECATED023": DEPRECATED023,             # <<<<<<<<<<<<<<
 *     "DEPRECATED024": DEPRECATED024,
 *     "DEPRECATED025": DEPRECATED025,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED023); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 137, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED023, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":138
 *     "DEPRECATED022": DEPRECATED022,
 *     "DEPRECATED023": DEPRECATED023,
 *     "DEPRECATED024": DEPRECATED024,             # <<<<<<<<<<<<<<
 *     "DEPRECATED025": DEPRECATED025,
 *     "DEPRECATED026": DEPRECATED026,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED024); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 138, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED024, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":139
 *     "DEPRECATED023": DEPRECATED023,
 *     "DEPRECATED024": DEPRECATED024,
 *     "DEPRECATED025": DEPRECATED025,             # <<<<<<<<<<<<<<
 *     "DEPRECATED026": DEPRECATED026,
 *     "DEPRECATED027": DEPRECATED027,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED025); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 139, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED025, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":140
 *     "DEPRECATED024": DEPRECATED024,
 *     "DEPRECATED025": DEPRECATED025,
 *     "DEPRECATED026": DEPRECATED026,             # <<<<<<<<<<<<<<
 *     "DEPRECATED027": DEPRECATED027,
 *     "DEPRECATED028": DEPRECATED028,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED026); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 140, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED026, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":141
 *     "DEPRECATED025": DEPRECATED025,
 *     "DEPRECATED026": DEPRECATED026,
 *     "DEPRECATED027": DEPRECATED027,             # <<<<<<<<<<<<<<
 *     "DEPRECATED028": DEPRECATED028,
 *     "DEPRECATED029": DEPRECATED029,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED027); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 141, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED027, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":142
 *     "DEPRECATED026": DEPRECATED026,
 *     "DEPRECATED027": DEPRECATED027,
 *     "DEPRECATED028": DEPRECATED028,             # <<<<<<<<<<<<<<
 *     "DEPRECATED029": DEPRECATED029,
 *     "DEPRECATED030": DEPRECATED030,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED028); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 142, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED028, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":143
 *     "DEPRECATED027": DEPRECATED027,
 *     "DEPRECATED028": DEPRECATED028,
 *     "DEPRECATED029": DEPRECATED029,             # <<<<<<<<<<<<<<
 *     "DEPRECATED030": DEPRECATED030,
 *     "DEPRECATED031": DEPRECATED031,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED029); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 143, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED029, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":144
 *     "DEPRECATED028": DEPRECATED028,
 *     "DEPRECATED029": DEPRECATED029,
 *     "DEPRECATED030": DEPRECATED030,             # <<<<<<<<<<<<<<
 *     "DEPRECATED031": DEPRECATED031,
 *     "DEPRECATED032": DEPRECATED032,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED030); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 144, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED030, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":145
 *     "DEPRECATED029": DEPRECATED029,
 *     "DEPRECATED030": DEPRECATED030,
 *     "DEPRECATED031": DEPRECATED031,             # <<<<<<<<<<<<<<
 *     "DEPRECATED032": DEPRECATED032,
 *     "DEPRECATED033": DEPRECATED033,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED031); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 145, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED031, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":146
 *     "DEPRECATED030": DEPRECATED030,
 *     "DEPRECATED031": DEPRECATED031,
 *     "DEPRECATED032": DEPRECATED032,             # <<<<<<<<<<<<<<
 *     "DEPRECATED033": DEPRECATED033,
 *     "DEPRECATED034": DEPRECATED034,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED032); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 146, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED032, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":147
 *     "DEPRECATED031": DEPRECATED031,
 *     "DEPRECATED032": DEPRECATED032,
 *     "DEPRECATED033": DEPRECATED033,             # <<<<<<<<<<<<<<
 *     "DEPRECATED034": DEPRECATED034,
 *     "DEPRECATED035": DEPRECATED035,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED033); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 147, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED033, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":148
 *     "DEPRECATED032": DEPRECATED032,
 *     "DEPRECATED033": DEPRECATED033,
 *     "DEPRECATED034": DEPRECATED034,             # <<<<<<<<<<<<<<
 *     "DEPRECATED035": DEPRECATED035,
 *     "DEPRECATED036": DEPRECATED036,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED034); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 148, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED034, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":149
 *     "DEPRECATED033": DEPRECATED033,
 *     "DEPRECATED034": DEPRECATED034,
 *     "DEPRECATED035": DEPRECATED035,             # <<<<<<<<<<<<<<
 *     "DEPRECATED036": DEPRECATED036,
 *     "DEPRECATED037": DEPRECATED037,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED035); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 149, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED035, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":150
 *     "DEPRECATED034": DEPRECATED034,
 *     "DEPRECATED035": DEPRECATED035,
 *     "DEPRECATED036": DEPRECATED036,             # <<<<<<<<<<<<<<
 *     "DEPRECATED037": DEPRECATED037,
 *     "DEPRECATED038": DEPRECATED038,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED036); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 150, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED036, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":151
 *     "DEPRECATED035": DEPRECATED035,
 *     "DEPRECATED036": DEPRECATED036,
 *     "DEPRECATED037": DEPRECATED037,             # <<<<<<<<<<<<<<
 *     "DEPRECATED038": DEPRECATED038,
 *     "DEPRECATED039": DEPRECATED039,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED037); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 151, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED037, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":152
 *     "DEPRECATED036": DEPRECATED036,
 *     "DEPRECATED037": DEPRECATED037,
 *     "DEPRECATED038": DEPRECATED038,             # <<<<<<<<<<<<<<
 *     "DEPRECATED039": DEPRECATED039,
 *     "DEPRECATED040": DEPRECATED040,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED038); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 152, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED038, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":153
 *     "DEPRECATED037": DEPRECATED037,
 *     "DEPRECATED038": DEPRECATED038,
 *     "DEPRECATED039": DEPRECATED039,             # <<<<<<<<<<<<<<
 *     "DEPRECATED040": DEPRECATED040,
 *     "DEPRECATED041": DEPRECATED041,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED039); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 153, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED039, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":154
 *     "DEPRECATED038": DEPRECATED038,
 *     "DEPRECATED039": DEPRECATED039,
 *     "DEPRECATED040": DEPRECATED040,             # <<<<<<<<<<<<<<
 *     "DEPRECATED041": DEPRECATED041,
 *     "DEPRECATED042": DEPRECATED042,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED040); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 154, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED040, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":155
 *     "DEPRECATED039": DEPRECATED039,
 *     "DEPRECATED040": DEPRECATED040,
 *     "DEPRECATED041": DEPRECATED041,             # <<<<<<<<<<<<<<
 *     "DEPRECATED042": DEPRECATED042,
 *     "DEPRECATED043": DEPRECATED043,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED041); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 155, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED041, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":156
 *     "DEPRECATED040": DEPRECATED040,
 *     "DEPRECATED041": DEPRECATED041,
 *     "DEPRECATED042": DEPRECATED042,             # <<<<<<<<<<<<<<
 *     "DEPRECATED043": DEPRECATED043,
 *     "DEPRECATED044": DEPRECATED044,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED042); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 156, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED042, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":157
 *     "DEPRECATED041": DEPRECATED041,
 *     "DEPRECATED042": DEPRECATED042,
 *     "DEPRECATED043": DEPRECATED043,             # <<<<<<<<<<<<<<
 *     "DEPRECATED044": DEPRECATED044,
 *     "DEPRECATED045": DEPRECATED045,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED043); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 157, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED043, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":158
 *     "DEPRECATED042": DEPRECATED042,
 *     "DEPRECATED043": DEPRECATED043,
 *     "DEPRECATED044": DEPRECATED044,             # <<<<<<<<<<<<<<
 *     "DEPRECATED045": DEPRECATED045,
 *     "DEPRECATED046": DEPRECATED046,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED044); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 158, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED044, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":159
 *     "DEPRECATED043": DEPRECATED043,
 *     "DEPRECATED044": DEPRECATED044,
 *     "DEPRECATED045": DEPRECATED045,             # <<<<<<<<<<<<<<
 *     "DEPRECATED046": DEPRECATED046,
 *     "DEPRECATED047": DEPRECATED047,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED045); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 159, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED045, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":160
 *     "DEPRECATED044": DEPRECATED044,
 *     "DEPRECATED045": DEPRECATED045,
 *     "DEPRECATED046": DEPRECATED046,             # <<<<<<<<<<<<<<
 *     "DEPRECATED047": DEPRECATED047,
 *     "DEPRECATED048": DEPRECATED048,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED046); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 160, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED046, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":161
 *     "DEPRECATED045": DEPRECATED045,
 *     "DEPRECATED046": DEPRECATED046,
 *     "DEPRECATED047": DEPRECATED047,             # <<<<<<<<<<<<<<
 *     "DEPRECATED048": DEPRECATED048,
 *     "DEPRECATED049": DEPRECATED049,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED047); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 161, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED047, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":162
 *     "DEPRECATED046": DEPRECATED046,
 *     "DEPRECATED047": DEPRECATED047,
 *     "DEPRECATED048": DEPRECATED048,             # <<<<<<<<<<<<<<
 *     "DEPRECATED049": DEPRECATED049,
 *     "DEPRECATED050": DEPRECATED050,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED048); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 162, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED048, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":163
 *     "DEPRECATED047": DEPRECATED047,
 *     "DEPRECATED048": DEPRECATED048,
 *     "DEPRECATED049": DEPRECATED049,             # <<<<<<<<<<<<<<
 *     "DEPRECATED050": DEPRECATED050,
 *     "DEPRECATED051": DEPRECATED051,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED049); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 163, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED049, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":164
 *     "DEPRECATED048": DEPRECATED048,
 *     "DEPRECATED049": DEPRECATED049,
 *     "DEPRECATED050": DEPRECATED050,             # <<<<<<<<<<<<<<
 *     "DEPRECATED051": DEPRECATED051,
 *     "DEPRECATED052": DEPRECATED052,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED050); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 164, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED050, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":165
 *     "DEPRECATED049": DEPRECATED049,
 *     "DEPRECATED050": DEPRECATED050,
 *     "DEPRECATED051": DEPRECATED051,             # <<<<<<<<<<<<<<
 *     "DEPRECATED052": DEPRECATED052,
 *     "DEPRECATED053": DEPRECATED053,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED051); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 165, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED051, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":166
 *     "DEPRECATED050": DEPRECATED050,
 *     "DEPRECATED051": DEPRECATED051,
 *     "DEPRECATED052": DEPRECATED052,             # <<<<<<<<<<<<<<
 *     "DEPRECATED053": DEPRECATED053,
 *     "DEPRECATED054": DEPRECATED054,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED052); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 166, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED052, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":167
 *     "DEPRECATED051": DEPRECATED051,
 *     "DEPRECATED052": DEPRECATED052,
 *     "DEPRECATED053": DEPRECATED053,             # <<<<<<<<<<<<<<
 *     "DEPRECATED054": DEPRECATED054,
 *     "DEPRECATED055": DEPRECATED055,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED053); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 167, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED053, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":168
 *     "DEPRECATED052": DEPRECATED052,
 *     "DEPRECATED053": DEPRECATED053,
 *     "DEPRECATED054": DEPRECATED054,             # <<<<<<<<<<<<<<
 *     "DEPRECATED055": DEPRECATED055,
 *     "DEPRECATED056": DEPRECATED056,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED054); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 168, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED054, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":169
 *     "DEPRECATED053": DEPRECATED053,
 *     "DEPRECATED054": DEPRECATED054,
 *     "DEPRECATED055": DEPRECATED055,             # <<<<<<<<<<<<<<
 *     "DEPRECATED056": DEPRECATED056,
 *     "DEPRECATED057": DEPRECATED057,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED055); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 169, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED055, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":170
 *     "DEPRECATED054": DEPRECATED054,
 *     "DEPRECATED055": DEPRECATED055,
 *     "DEPRECATED056": DEPRECATED056,             # <<<<<<<<<<<<<<
 *     "DEPRECATED057": DEPRECATED057,
 *     "DEPRECATED058": DEPRECATED058,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED056); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 170, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED056, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":171
 *     "DEPRECATED055": DEPRECATED055,
 *     "DEPRECATED056": DEPRECATED056,
 *     "DEPRECATED057": DEPRECATED057,             # <<<<<<<<<<<<<<
 *     "DEPRECATED058": DEPRECATED058,
 *     "DEPRECATED059": DEPRECATED059,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED057); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 171, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED057, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":172
 *     "DEPRECATED056": DEPRECATED056,
 *     "DEPRECATED057": DEPRECATED057,
 *     "DEPRECATED058": DEPRECATED058,             # <<<<<<<<<<<<<<
 *     "DEPRECATED059": DEPRECATED059,
 *     "DEPRECATED060": DEPRECATED060,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED058); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 172, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED058, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":173
 *     "DEPRECATED057": DEPRECATED057,
 *     "DEPRECATED058": DEPRECATED058,
 *     "DEPRECATED059": DEPRECATED059,             # <<<<<<<<<<<<<<
 *     "DEPRECATED060": DEPRECATED060,
 *     "DEPRECATED061": DEPRECATED061,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED059); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 173, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED059, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":174
 *     "DEPRECATED058": DEPRECATED058,
 *     "DEPRECATED059": DEPRECATED059,
 *     "DEPRECATED060": DEPRECATED060,             # <<<<<<<<<<<<<<
 *     "DEPRECATED061": DEPRECATED061,
 *     "DEPRECATED062": DEPRECATED062,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED060); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 174, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED060, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":175
 *     "DEPRECATED059": DEPRECATED059,
 *     "DEPRECATED060": DEPRECATED060,
 *     "DEPRECATED061": DEPRECATED061,             # <<<<<<<<<<<<<<
 *     "DEPRECATED062": DEPRECATED062,
 *     "DEPRECATED063": DEPRECATED063,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED061); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 175, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED061, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":176
 *     "DEPRECATED060": DEPRECATED060,
 *     "DEPRECATED061": DEPRECATED061,
 *     "DEPRECATED062": DEPRECATED062,             # <<<<<<<<<<<<<<
 *     "DEPRECATED063": DEPRECATED063,
 *     "DEPRECATED064": DEPRECATED064,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED062); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 176, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED062, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":177
 *     "DEPRECATED061": DEPRECATED061,
 *     "DEPRECATED062": DEPRECATED062,
 *     "DEPRECATED063": DEPRECATED063,             # <<<<<<<<<<<<<<
 *     "DEPRECATED064": DEPRECATED064,
 *     "DEPRECATED065": DEPRECATED065,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED063); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 177, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED063, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":178
 *     "DEPRECATED062": DEPRECATED062,
 *     "DEPRECATED063": DEPRECATED063,
 *     "DEPRECATED064": DEPRECATED064,             # <<<<<<<<<<<<<<
 *     "DEPRECATED065": DEPRECATED065,
 *     "DEPRECATED066": DEPRECATED066,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED064); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 178, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED064, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":179
 *     "DEPRECATED063": DEPRECATED063,
 *     "DEPRECATED064": DEPRECATED064,
 *     "DEPRECATED065": DEPRECATED065,             # <<<<<<<<<<<<<<
 *     "DEPRECATED066": DEPRECATED066,
 *     "DEPRECATED067": DEPRECATED067,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED065); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 179, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED065, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":180
 *     "DEPRECATED064": DEPRECATED064,
 *     "DEPRECATED065": DEPRECATED065,
 *     "DEPRECATED066": DEPRECATED066,             # <<<<<<<<<<<<<<
 *     "DEPRECATED067": DEPRECATED067,
 *     "DEPRECATED068": DEPRECATED068,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED066); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 180, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED066, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":181
 *     "DEPRECATED065": DEPRECATED065,
 *     "DEPRECATED066": DEPRECATED066,
 *     "DEPRECATED067": DEPRECATED067,             # <<<<<<<<<<<<<<
 *     "DEPRECATED068": DEPRECATED068,
 *     "DEPRECATED069": DEPRECATED069,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED067); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 181, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED067, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":182
 *     "DEPRECATED066": DEPRECATED066,
 *     "DEPRECATED067": DEPRECATED067,
 *     "DEPRECATED068": DEPRECATED068,             # <<<<<<<<<<<<<<
 *     "DEPRECATED069": DEPRECATED069,
 *     "DEPRECATED070": DEPRECATED070,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED068); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 182, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED068, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":183
 *     "DEPRECATED067": DEPRECATED067,
 *     "DEPRECATED068": DEPRECATED068,
 *     "DEPRECATED069": DEPRECATED069,             # <<<<<<<<<<<<<<
 *     "DEPRECATED070": DEPRECATED070,
 *     "DEPRECATED071": DEPRECATED071,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED069); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 183, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED069, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":184
 *     "DEPRECATED068": DEPRECATED068,
 *     "DEPRECATED069": DEPRECATED069,
 *     "DEPRECATED070": DEPRECATED070,             # <<<<<<<<<<<<<<
 *     "DEPRECATED071": DEPRECATED071,
 *     "DEPRECATED072": DEPRECATED072,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED070); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 184, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED070, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":185
 *     "DEPRECATED069": DEPRECATED069,
 *     "DEPRECATED070": DEPRECATED070,
 *     "DEPRECATED071": DEPRECATED071,             # <<<<<<<<<<<<<<
 *     "DEPRECATED072": DEPRECATED072,
 *     "DEPRECATED073": DEPRECATED073,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED071); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 185, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED071, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":186
 *     "DEPRECATED070": DEPRECATED070,
 *     "DEPRECATED071": DEPRECATED071,
 *     "DEPRECATED072": DEPRECATED072,             # <<<<<<<<<<<<<<
 *     "DEPRECATED073": DEPRECATED073,
 *     "DEPRECATED074": DEPRECATED074,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED072); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 186, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED072, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":187
 *     "DEPRECATED071": DEPRECATED071,
 *     "DEPRECATED072": DEPRECATED072,
 *     "DEPRECATED073": DEPRECATED073,             # <<<<<<<<<<<<<<
 *     "DEPRECATED074": DEPRECATED074,
 *     "DEPRECATED075": DEPRECATED075,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED073); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 187, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED073, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":188
 *     "DEPRECATED072": DEPRECATED072,
 *     "DEPRECATED073": DEPRECATED073,
 *     "DEPRECATED074": DEPRECATED074,             # <<<<<<<<<<<<<<
 *     "DEPRECATED075": DEPRECATED075,
 *     "DEPRECATED076": DEPRECATED076,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED074); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 188, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED074, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":189
 *     "DEPRECATED073": DEPRECATED073,
 *     "DEPRECATED074": DEPRECATED074,
 *     "DEPRECATED075": DEPRECATED075,             # <<<<<<<<<<<<<<
 *     "DEPRECATED076": DEPRECATED076,
 *     "DEPRECATED077": DEPRECATED077,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED075); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 189, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED075, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":190
 *     "DEPRECATED074": DEPRECATED074,
 *     "DEPRECATED075": DEPRECATED075,
 *     "DEPRECATED076": DEPRECATED076,             # <<<<<<<<<<<<<<
 *     "DEPRECATED077": DEPRECATED077,
 *     "DEPRECATED078": DEPRECATED078,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED076); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 190, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED076, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":191
 *     "DEPRECATED075": DEPRECATED075,
 *     "DEPRECATED076": DEPRECATED076,
 *     "DEPRECATED077": DEPRECATED077,             # <<<<<<<<<<<<<<
 *     "DEPRECATED078": DEPRECATED078,
 *     "DEPRECATED079": DEPRECATED079,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED077); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 191, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED077, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":192
 *     "DEPRECATED076": DEPRECATED076,
 *     "DEPRECATED077": DEPRECATED077,
 *     "DEPRECATED078": DEPRECATED078,             # <<<<<<<<<<<<<<
 *     "DEPRECATED079": DEPRECATED079,
 *     "DEPRECATED080": DEPRECATED080,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED078); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 192, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED078, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":193
 *     "DEPRECATED077": DEPRECATED077,
 *     "DEPRECATED078": DEPRECATED078,
 *     "DEPRECATED079": DEPRECATED079,             # <<<<<<<<<<<<<<
 *     "DEPRECATED080": DEPRECATED080,
 *     "DEPRECATED081": DEPRECATED081,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED079); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 193, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED079, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":194
 *     "DEPRECATED078": DEPRECATED078,
 *     "DEPRECATED079": DEPRECATED079,
 *     "DEPRECATED080": DEPRECATED080,             # <<<<<<<<<<<<<<
 *     "DEPRECATED081": DEPRECATED081,
 *     "DEPRECATED082": DEPRECATED082,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED080); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 194, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED080, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":195
 *     "DEPRECATED079": DEPRECATED079,
 *     "DEPRECATED080": DEPRECATED080,
 *     "DEPRECATED081": DEPRECATED081,             # <<<<<<<<<<<<<<
 *     "DEPRECATED082": DEPRECATED082,
 *     "DEPRECATED083": DEPRECATED083,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED081); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 195, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED081, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":196
 *     "DEPRECATED080": DEPRECATED080,
 *     "DEPRECATED081": DEPRECATED081,
 *     "DEPRECATED082": DEPRECATED082,             # <<<<<<<<<<<<<<
 *     "DEPRECATED083": DEPRECATED083,
 *     "DEPRECATED084": DEPRECATED084,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED082); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 196, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED082, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":197
 *     "DEPRECATED081": DEPRECATED081,
 *     "DEPRECATED082": DEPRECATED082,
 *     "DEPRECATED083": DEPRECATED083,             # <<<<<<<<<<<<<<
 *     "DEPRECATED084": DEPRECATED084,
 *     "DEPRECATED085": DEPRECATED085,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED083); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 197, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED083, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":198
 *     "DEPRECATED082": DEPRECATED082,
 *     "DEPRECATED083": DEPRECATED083,
 *     "DEPRECATED084": DEPRECATED084,             # <<<<<<<<<<<<<<
 *     "DEPRECATED085": DEPRECATED085,
 *     "DEPRECATED086": DEPRECATED086,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED084); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 198, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED084, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":199
 *     "DEPRECATED083": DEPRECATED083,
 *     "DEPRECATED084": DEPRECATED084,
 *     "DEPRECATED085": DEPRECATED085,             # <<<<<<<<<<<<<<
 *     "DEPRECATED086": DEPRECATED086,
 *     "DEPRECATED087": DEPRECATED087,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED085); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 199, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED085, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":200
 *     "DEPRECATED084": DEPRECATED084,
 *     "DEPRECATED085": DEPRECATED085,
 *     "DEPRECATED086": DEPRECATED086,             # <<<<<<<<<<<<<<
 *     "DEPRECATED087": DEPRECATED087,
 *     "DEPRECATED088": DEPRECATED088,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED086); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 200, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED086, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":201
 *     "DEPRECATED085": DEPRECATED085,
 *     "DEPRECATED086": DEPRECATED086,
 *     "DEPRECATED087": DEPRECATED087,             # <<<<<<<<<<<<<<
 *     "DEPRECATED088": DEPRECATED088,
 *     "DEPRECATED089": DEPRECATED089,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED087); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 201, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED087, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":202
 *     "DEPRECATED086": DEPRECATED086,
 *     "DEPRECATED087": DEPRECATED087,
 *     "DEPRECATED088": DEPRECATED088,             # <<<<<<<<<<<<<<
 *     "DEPRECATED089": DEPRECATED089,
 *     "DEPRECATED090": DEPRECATED090,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED088); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 202, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED088, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":203
 *     "DEPRECATED087": DEPRECATED087,
 *     "DEPRECATED088": DEPRECATED088,
 *     "DEPRECATED089": DEPRECATED089,             # <<<<<<<<<<<<<<
 *     "DEPRECATED090": DEPRECATED090,
 *     "DEPRECATED091": DEPRECATED091,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED089); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 203, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED089, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":204
 *     "DEPRECATED088": DEPRECATED088,
 *     "DEPRECATED089": DEPRECATED089,
 *     "DEPRECATED090": DEPRECATED090,             # <<<<<<<<<<<<<<
 *     "DEPRECATED091": DEPRECATED091,
 *     "DEPRECATED092": DEPRECATED092,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED090); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 204, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED090, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":205
 *     "DEPRECATED089": DEPRECATED089,
 *     "DEPRECATED090": DEPRECATED090,
 *     "DEPRECATED091": DEPRECATED091,             # <<<<<<<<<<<<<<
 *     "DEPRECATED092": DEPRECATED092,
 *     "DEPRECATED093": DEPRECATED093,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED091); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 205, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED091, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":206
 *     "DEPRECATED090": DEPRECATED090,
 *     "DEPRECATED091": DEPRECATED091,
 *     "DEPRECATED092": DEPRECATED092,             # <<<<<<<<<<<<<<
 *     "DEPRECATED093": DEPRECATED093,
 *     "DEPRECATED094": DEPRECATED094,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED092); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 206, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED092, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":207
 *     "DEPRECATED091": DEPRECATED091,
 *     "DEPRECATED092": DEPRECATED092,
 *     "DEPRECATED093": DEPRECATED093,             # <<<<<<<<<<<<<<
 *     "DEPRECATED094": DEPRECATED094,
 *     "DEPRECATED095": DEPRECATED095,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED093); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 207, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED093, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":208
 *     "DEPRECATED092": DEPRECATED092,
 *     "DEPRECATED093": DEPRECATED093,
 *     "DEPRECATED094": DEPRECATED094,             # <<<<<<<<<<<<<<
 *     "DEPRECATED095": DEPRECATED095,
 *     "DEPRECATED096": DEPRECATED096,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED094); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 208, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED094, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":209
 *     "DEPRECATED093": DEPRECATED093,
 *     "DEPRECATED094": DEPRECATED094,
 *     "DEPRECATED095": DEPRECATED095,             # <<<<<<<<<<<<<<
 *     "DEPRECATED096": DEPRECATED096,
 *     "DEPRECATED097": DEPRECATED097,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED095); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 209, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED095, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":210
 *     "DEPRECATED094": DEPRECATED094,
 *     "DEPRECATED095": DEPRECATED095,
 *     "DEPRECATED096": DEPRECATED096,             # <<<<<<<<<<<<<<
 *     "DEPRECATED097": DEPRECATED097,
 *     "DEPRECATED098": DEPRECATED098,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED096); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 210, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED096, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":211
 *     "DEPRECATED095": DEPRECATED095,
 *     "DEPRECATED096": DEPRECATED096,
 *     "DEPRECATED097": DEPRECATED097,             # <<<<<<<<<<<<<<
 *     "DEPRECATED098": DEPRECATED098,
 *     "DEPRECATED099": DEPRECATED099,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED097); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 211, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED097, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":212
 *     "DEPRECATED096": DEPRECATED096,
 *     "DEPRECATED097": DEPRECATED097,
 *     "DEPRECATED098": DEPRECATED098,             # <<<<<<<<<<<<<<
 *     "DEPRECATED099": DEPRECATED099,
 *     "DEPRECATED100": DEPRECATED100,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED098); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 212, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED098, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":213
 *     "DEPRECATED097": DEPRECATED097,
 *     "DEPRECATED098": DEPRECATED098,
 *     "DEPRECATED099": DEPRECATED099,             # <<<<<<<<<<<<<<
 *     "DEPRECATED100": DEPRECATED100,
 *     "DEPRECATED101": DEPRECATED101,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED099); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 213, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED099, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":214
 *     "DEPRECATED098": DEPRECATED098,
 *     "DEPRECATED099": DEPRECATED099,
 *     "DEPRECATED100": DEPRECATED100,             # <<<<<<<<<<<<<<
 *     "DEPRECATED101": DEPRECATED101,
 *     "DEPRECATED102": DEPRECATED102,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED100); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 214, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED100, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":215
 *     "DEPRECATED099": DEPRECATED099,
 *     "DEPRECATED100": DEPRECATED100,
 *     "DEPRECATED101": DEPRECATED101,             # <<<<<<<<<<<<<<
 *     "DEPRECATED102": DEPRECATED102,
 *     "DEPRECATED103": DEPRECATED103,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED101); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 215, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED101, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":216
 *     "DEPRECATED100": DEPRECATED100,
 *     "DEPRECATED101": DEPRECATED101,
 *     "DEPRECATED102": DEPRECATED102,             # <<<<<<<<<<<<<<
 *     "DEPRECATED103": DEPRECATED103,
 *     "DEPRECATED104": DEPRECATED104,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED102); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 216, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED102, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":217
 *     "DEPRECATED101": DEPRECATED101,
 *     "DEPRECATED102": DEPRECATED102,
 *     "DEPRECATED103": DEPRECATED103,             # <<<<<<<<<<<<<<
 *     "DEPRECATED104": DEPRECATED104,
 *     "DEPRECATED105": DEPRECATED105,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED103); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 217, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED103, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":218
 *     "DEPRECATED102": DEPRECATED102,
 *     "DEPRECATED103": DEPRECATED103,
 *     "DEPRECATED104": DEPRECATED104,             # <<<<<<<<<<<<<<
 *     "DEPRECATED105": DEPRECATED105,
 *     "DEPRECATED106": DEPRECATED106,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED104); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 218, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED104, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":219
 *     "DEPRECATED103": DEPRECATED103,
 *     "DEPRECATED104": DEPRECATED104,
 *     "DEPRECATED105": DEPRECATED105,             # <<<<<<<<<<<<<<
 *     "DEPRECATED106": DEPRECATED106,
 *     "DEPRECATED107": DEPRECATED107,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED105); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 219, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED105, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":220
 *     "DEPRECATED104": DEPRECATED104,
 *     "DEPRECATED105": DEPRECATED105,
 *     "DEPRECATED106": DEPRECATED106,             # <<<<<<<<<<<<<<
 *     "DEPRECATED107": DEPRECATED107,
 *     "DEPRECATED108": DEPRECATED108,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED106); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 220, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED106, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":221
 *     "DEPRECATED105": DEPRECATED105,
 *     "DEPRECATED106": DEPRECATED106,
 *     "DEPRECATED107": DEPRECATED107,             # <<<<<<<<<<<<<<
 *     "DEPRECATED108": DEPRECATED108,
 *     "DEPRECATED109": DEPRECATED109,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED107); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 221, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED107, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":222
 *     "DEPRECATED106": DEPRECATED106,
 *     "DEPRECATED107": DEPRECATED107,
 *     "DEPRECATED108": DEPRECATED108,             # <<<<<<<<<<<<<<
 *     "DEPRECATED109": DEPRECATED109,
 *     "DEPRECATED110": DEPRECATED110,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED108); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 222, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED108, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":223
 *     "DEPRECATED107": DEPRECATED107,
 *     "DEPRECATED108": DEPRECATED108,
 *     "DEPRECATED109": DEPRECATED109,             # <<<<<<<<<<<<<<
 *     "DEPRECATED110": DEPRECATED110,
 *     "DEPRECATED111": DEPRECATED111,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED109); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 223, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED109, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":224
 *     "DEPRECATED108": DEPRECATED108,
 *     "DEPRECATED109": DEPRECATED109,
 *     "DEPRECATED110": DEPRECATED110,             # <<<<<<<<<<<<<<
 *     "DEPRECATED111": DEPRECATED111,
 *     "DEPRECATED112": DEPRECATED112,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED110); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 224, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED110, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":225
 *     "DEPRECATED109": DEPRECATED109,
 *     "DEPRECATED110": DEPRECATED110,
 *     "DEPRECATED111": DEPRECATED111,             # <<<<<<<<<<<<<<
 *     "DEPRECATED112": DEPRECATED112,
 *     "DEPRECATED113": DEPRECATED113,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED111); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 225, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED111, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":226
 *     "DEPRECATED110": DEPRECATED110,
 *     "DEPRECATED111": DEPRECATED111,
 *     "DEPRECATED112": DEPRECATED112,             # <<<<<<<<<<<<<<
 *     "DEPRECATED113": DEPRECATED113,
 *     "DEPRECATED114": DEPRECATED114,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED112); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 226, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED112, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":227
 *     "DEPRECATED111": DEPRECATED111,
 *     "DEPRECATED112": DEPRECATED112,
 *     "DEPRECATED113": DEPRECATED113,             # <<<<<<<<<<<<<<
 *     "DEPRECATED114": DEPRECATED114,
 *     "DEPRECATED115": DEPRECATED115,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED113); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 227, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED113, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":228
 *     "DEPRECATED112": DEPRECATED112,
 *     "DEPRECATED113": DEPRECATED113,
 *     "DEPRECATED114": DEPRECATED114,             # <<<<<<<<<<<<<<
 *     "DEPRECATED115": DEPRECATED115,
 *     "DEPRECATED116": DEPRECATED116,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED114); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 228, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED114, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":229
 *     "DEPRECATED113": DEPRECATED113,
 *     "DEPRECATED114": DEPRECATED114,
 *     "DEPRECATED115": DEPRECATED115,             # <<<<<<<<<<<<<<
 *     "DEPRECATED116": DEPRECATED116,
 *     "DEPRECATED117": DEPRECATED117,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED115); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 229, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED115, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":230
 *     "DEPRECATED114": DEPRECATED114,
 *     "DEPRECATED115": DEPRECATED115,
 *     "DEPRECATED116": DEPRECATED116,             # <<<<<<<<<<<<<<
 *     "DEPRECATED117": DEPRECATED117,
 *     "DEPRECATED118": DEPRECATED118,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED116); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 230, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED116, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":231
 *     "DEPRECATED115": DEPRECATED115,
 *     "DEPRECATED116": DEPRECATED116,
 *     "DEPRECATED117": DEPRECATED117,             # <<<<<<<<<<<<<<
 *     "DEPRECATED118": DEPRECATED118,
 *     "DEPRECATED119": DEPRECATED119,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED117); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 231, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED117, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":232
 *     "DEPRECATED116": DEPRECATED116,
 *     "DEPRECATED117": DEPRECATED117,
 *     "DEPRECATED118": DEPRECATED118,             # <<<<<<<<<<<<<<
 *     "DEPRECATED119": DEPRECATED119,
 *     "DEPRECATED120": DEPRECATED120,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED118); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 232, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED118, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":233
 *     "DEPRECATED117": DEPRECATED117,
 *     "DEPRECATED118": DEPRECATED118,
 *     "DEPRECATED119": DEPRECATED119,             # <<<<<<<<<<<<<<
 *     "DEPRECATED120": DEPRECATED120,
 *     "DEPRECATED121": DEPRECATED121,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED119); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 233, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED119, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":234
 *     "DEPRECATED118": DEPRECATED118,
 *     "DEPRECATED119": DEPRECATED119,
 *     "DEPRECATED120": DEPRECATED120,             # <<<<<<<<<<<<<<
 *     "DEPRECATED121": DEPRECATED121,
 *     "DEPRECATED122": DEPRECATED122,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED120); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 234, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED120, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":235
 *     "DEPRECATED119": DEPRECATED119,
 *     "DEPRECATED120": DEPRECATED120,
 *     "DEPRECATED121": DEPRECATED121,             # <<<<<<<<<<<<<<
 *     "DEPRECATED122": DEPRECATED122,
 *     "DEPRECATED123": DEPRECATED123,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED121); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 235, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED121, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":236
 *     "DEPRECATED120": DEPRECATED120,
 *     "DEPRECATED121": DEPRECATED121,
 *     "DEPRECATED122": DEPRECATED122,             # <<<<<<<<<<<<<<
 *     "DEPRECATED123": DEPRECATED123,
 *     "DEPRECATED124": DEPRECATED124,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED122); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 236, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED122, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":237
 *     "DEPRECATED121": DEPRECATED121,
 *     "DEPRECATED122": DEPRECATED122,
 *     "DEPRECATED123": DEPRECATED123,             # <<<<<<<<<<<<<<
 *     "DEPRECATED124": DEPRECATED124,
 *     "DEPRECATED125": DEPRECATED125,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED123); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 237, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED123, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":238
 *     "DEPRECATED122": DEPRECATED122,
 *     "DEPRECATED123": DEPRECATED123,
 *     "DEPRECATED124": DEPRECATED124,             # <<<<<<<<<<<<<<
 *     "DEPRECATED125": DEPRECATED125,
 *     "DEPRECATED126": DEPRECATED126,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED124); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 238, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED124, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":239
 *     "DEPRECATED123": DEPRECATED123,
 *     "DEPRECATED124": DEPRECATED124,
 *     "DEPRECATED125": DEPRECATED125,             # <<<<<<<<<<<<<<
 *     "DEPRECATED126": DEPRECATED126,
 *     "DEPRECATED127": DEPRECATED127,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED125); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 239, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED125, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":240
 *     "DEPRECATED124": DEPRECATED124,
 *     "DEPRECATED125": DEPRECATED125,
 *     "DEPRECATED126": DEPRECATED126,             # <<<<<<<<<<<<<<
 *     "DEPRECATED127": DEPRECATED127,
 *     "DEPRECATED128": DEPRECATED128,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED126); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 240, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED126, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":241
 *     "DEPRECATED125": DEPRECATED125,
 *     "DEPRECATED126": DEPRECATED126,
 *     "DEPRECATED127": DEPRECATED127,             # <<<<<<<<<<<<<<
 *     "DEPRECATED128": DEPRECATED128,
 *     "DEPRECATED129": DEPRECATED129,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED127); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 241, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED127, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":242
 *     "DEPRECATED126": DEPRECATED126,
 *     "DEPRECATED127": DEPRECATED127,
 *     "DEPRECATED128": DEPRECATED128,             # <<<<<<<<<<<<<<
 *     "DEPRECATED129": DEPRECATED129,
 *     "DEPRECATED130": DEPRECATED130,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED128); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 242, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED128, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":243
 *     "DEPRECATED127": DEPRECATED127,
 *     "DEPRECATED128": DEPRECATED128,
 *     "DEPRECATED129": DEPRECATED129,             # <<<<<<<<<<<<<<
 *     "DEPRECATED130": DEPRECATED130,
 *     "DEPRECATED131": DEPRECATED131,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED129); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 243, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED129, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":244
 *     "DEPRECATED128": DEPRECATED128,
 *     "DEPRECATED129": DEPRECATED129,
 *     "DEPRECATED130": DEPRECATED130,             # <<<<<<<<<<<<<<
 *     "DEPRECATED131": DEPRECATED131,
 *     "DEPRECATED132": DEPRECATED132,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED130); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 244, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED130, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":245
 *     "DEPRECATED129": DEPRECATED129,
 *     "DEPRECATED130": DEPRECATED130,
 *     "DEPRECATED131": DEPRECATED131,             # <<<<<<<<<<<<<<
 *     "DEPRECATED132": DEPRECATED132,
 *     "DEPRECATED133": DEPRECATED133,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED131); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 245, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED131, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":246
 *     "DEPRECATED130": DEPRECATED130,
 *     "DEPRECATED131": DEPRECATED131,
 *     "DEPRECATED132": DEPRECATED132,             # <<<<<<<<<<<<<<
 *     "DEPRECATED133": DEPRECATED133,
 *     "DEPRECATED134": DEPRECATED134,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED132); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 246, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED132, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":247
 *     "DEPRECATED131": DEPRECATED131,
 *     "DEPRECATED132": DEPRECATED132,
 *     "DEPRECATED133": DEPRECATED133,             # <<<<<<<<<<<<<<
 *     "DEPRECATED134": DEPRECATED134,
 *     "DEPRECATED135": DEPRECATED135,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED133); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 247, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED133, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":248
 *     "DEPRECATED132": DEPRECATED132,
 *     "DEPRECATED133": DEPRECATED133,
 *     "DEPRECATED134": DEPRECATED134,             # <<<<<<<<<<<<<<
 *     "DEPRECATED135": DEPRECATED135,
 *     "DEPRECATED136": DEPRECATED136,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED134); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 248, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED134, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":249
 *     "DEPRECATED133": DEPRECATED133,
 *     "DEPRECATED134": DEPRECATED134,
 *     "DEPRECATED135": DEPRECATED135,             # <<<<<<<<<<<<<<
 *     "DEPRECATED136": DEPRECATED136,
 *     "DEPRECATED137": DEPRECATED137,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED135); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 249, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED135, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":250
 *     "DEPRECATED134": DEPRECATED134,
 *     "DEPRECATED135": DEPRECATED135,
 *     "DEPRECATED136": DEPRECATED136,             # <<<<<<<<<<<<<<
 *     "DEPRECATED137": DEPRECATED137,
 *     "DEPRECATED138": DEPRECATED138,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED136); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 250, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED136, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":251
 *     "DEPRECATED135": DEPRECATED135,
 *     "DEPRECATED136": DEPRECATED136,
 *     "DEPRECATED137": DEPRECATED137,             # <<<<<<<<<<<<<<
 *     "DEPRECATED138": DEPRECATED138,
 *     "DEPRECATED139": DEPRECATED139,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED137); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 251, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED137, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":252
 *     "DEPRECATED136": DEPRECATED136,
 *     "DEPRECATED137": DEPRECATED137,
 *     "DEPRECATED138": DEPRECATED138,             # <<<<<<<<<<<<<<
 *     "DEPRECATED139": DEPRECATED139,
 *     "DEPRECATED140": DEPRECATED140,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED138); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 252, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED138, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":253
 *     "DEPRECATED137": DEPRECATED137,
 *     "DEPRECATED138": DEPRECATED138,
 *     "DEPRECATED139": DEPRECATED139,             # <<<<<<<<<<<<<<
 *     "DEPRECATED140": DEPRECATED140,
 *     "DEPRECATED141": DEPRECATED141,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED139); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 253, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED139, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":254
 *     "DEPRECATED138": DEPRECATED138,
 *     "DEPRECATED139": DEPRECATED139,
 *     "DEPRECATED140": DEPRECATED140,             # <<<<<<<<<<<<<<
 *     "DEPRECATED141": DEPRECATED141,
 *     "DEPRECATED142": DEPRECATED142,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED140); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 254, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED140, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":255
 *     "DEPRECATED139": DEPRECATED139,
 *     "DEPRECATED140": DEPRECATED140,
 *     "DEPRECATED141": DEPRECATED141,             # <<<<<<<<<<<<<<
 *     "DEPRECATED142": DEPRECATED142,
 *     "DEPRECATED143": DEPRECATED143,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED141); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 255, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED141, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":256
 *     "DEPRECATED140": DEPRECATED140,
 *     "DEPRECATED141": DEPRECATED141,
 *     "DEPRECATED142": DEPRECATED142,             # <<<<<<<<<<<<<<
 *     "DEPRECATED143": DEPRECATED143,
 *     "DEPRECATED144": DEPRECATED144,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED142); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 256, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED142, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":257
 *     "DEPRECATED141": DEPRECATED141,
 *     "DEPRECATED142": DEPRECATED142,
 *     "DEPRECATED143": DEPRECATED143,             # <<<<<<<<<<<<<<
 *     "DEPRECATED144": DEPRECATED144,
 *     "DEPRECATED145": DEPRECATED145,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED143); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 257, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED143, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":258
 *     "DEPRECATED142": DEPRECATED142,
 *     "DEPRECATED143": DEPRECATED143,
 *     "DEPRECATED144": DEPRECATED144,             # <<<<<<<<<<<<<<
 *     "DEPRECATED145": DEPRECATED145,
 *     "DEPRECATED146": DEPRECATED146,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED144); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 258, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED144, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":259
 *     "DEPRECATED143": DEPRECATED143,
 *     "DEPRECATED144": DEPRECATED144,
 *     "DEPRECATED145": DEPRECATED145,             # <<<<<<<<<<<<<<
 *     "DEPRECATED146": DEPRECATED146,
 *     "DEPRECATED147": DEPRECATED147,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED145); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 259, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED145, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":260
 *     "DEPRECATED144": DEPRECATED144,
 *     "DEPRECATED145": DEPRECATED145,
 *     "DEPRECATED146": DEPRECATED146,             # <<<<<<<<<<<<<<
 *     "DEPRECATED147": DEPRECATED147,
 *     "DEPRECATED148": DEPRECATED148,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED146); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 260, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED146, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":261
 *     "DEPRECATED145": DEPRECATED145,
 *     "DEPRECATED146": DEPRECATED146,
 *     "DEPRECATED147": DEPRECATED147,             # <<<<<<<<<<<<<<
 *     "DEPRECATED148": DEPRECATED148,
 *     "DEPRECATED149": DEPRECATED149,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED147); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 261, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED147, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":262
 *     "DEPRECATED146": DEPRECATED146,
 *     "DEPRECATED147": DEPRECATED147,
 *     "DEPRECATED148": DEPRECATED148,             # <<<<<<<<<<<<<<
 *     "DEPRECATED149": DEPRECATED149,
 *     "DEPRECATED150": DEPRECATED150,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED148); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 262, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED148, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":263
 *     "DEPRECATED147": DEPRECATED147,
 *     "DEPRECATED148": DEPRECATED148,
 *     "DEPRECATED149": DEPRECATED149,             # <<<<<<<<<<<<<<
 *     "DEPRECATED150": DEPRECATED150,
 *     "DEPRECATED151": DEPRECATED151,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED149); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 263, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED149, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":264
 *     "DEPRECATED148": DEPRECATED148,
 *     "DEPRECATED149": DEPRECATED149,
 *     "DEPRECATED150": DEPRECATED150,             # <<<<<<<<<<<<<<
 *     "DEPRECATED151": DEPRECATED151,
 *     "DEPRECATED152": DEPRECATED152,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED150); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 264, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED150, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":265
 *     "DEPRECATED149": DEPRECATED149,
 *     "DEPRECATED150": DEPRECATED150,
 *     "DEPRECATED151": DEPRECATED151,             # <<<<<<<<<<<<<<
 *     "DEPRECATED152": DEPRECATED152,
 *     "DEPRECATED153": DEPRECATED153,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED151); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 265, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED151, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":266
 *     "DEPRECATED150": DEPRECATED150,
 *     "DEPRECATED151": DEPRECATED151,
 *     "DEPRECATED152": DEPRECATED152,             # <<<<<<<<<<<<<<
 *     "DEPRECATED153": DEPRECATED153,
 *     "DEPRECATED154": DEPRECATED154,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED152); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 266, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED152, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":267
 *     "DEPRECATED151": DEPRECATED151,
 *     "DEPRECATED152": DEPRECATED152,
 *     "DEPRECATED153": DEPRECATED153,             # <<<<<<<<<<<<<<
 *     "DEPRECATED154": DEPRECATED154,
 *     "DEPRECATED155": DEPRECATED155,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED153); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 267, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED153, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":268
 *     "DEPRECATED152": DEPRECATED152,
 *     "DEPRECATED153": DEPRECATED153,
 *     "DEPRECATED154": DEPRECATED154,             # <<<<<<<<<<<<<<
 *     "DEPRECATED155": DEPRECATED155,
 *     "DEPRECATED156": DEPRECATED156,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED154); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 268, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED154, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":269
 *     "DEPRECATED153": DEPRECATED153,
 *     "DEPRECATED154": DEPRECATED154,
 *     "DEPRECATED155": DEPRECATED155,             # <<<<<<<<<<<<<<
 *     "DEPRECATED156": DEPRECATED156,
 *     "DEPRECATED157": DEPRECATED157,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED155); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 269, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED155, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":270
 *     "DEPRECATED154": DEPRECATED154,
 *     "DEPRECATED155": DEPRECATED155,
 *     "DEPRECATED156": DEPRECATED156,             # <<<<<<<<<<<<<<
 *     "DEPRECATED157": DEPRECATED157,
 *     "DEPRECATED158": DEPRECATED158,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED156); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 270, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED156, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":271
 *     "DEPRECATED155": DEPRECATED155,
 *     "DEPRECATED156": DEPRECATED156,
 *     "DEPRECATED157": DEPRECATED157,             # <<<<<<<<<<<<<<
 *     "DEPRECATED158": DEPRECATED158,
 *     "DEPRECATED159": DEPRECATED159,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED157); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 271, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED157, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":272
 *     "DEPRECATED156": DEPRECATED156,
 *     "DEPRECATED157": DEPRECATED157,
 *     "DEPRECATED158": DEPRECATED158,             # <<<<<<<<<<<<<<
 *     "DEPRECATED159": DEPRECATED159,
 *     "DEPRECATED160": DEPRECATED160,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED158); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 272, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED158, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":273
 *     "DEPRECATED157": DEPRECATED157,
 *     "DEPRECATED158": DEPRECATED158,
 *     "DEPRECATED159": DEPRECATED159,             # <<<<<<<<<<<<<<
 *     "DEPRECATED160": DEPRECATED160,
 *     "DEPRECATED161": DEPRECATED161,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED159); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 273, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED159, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":274
 *     "DEPRECATED158": DEPRECATED158,
 *     "DEPRECATED159": DEPRECATED159,
 *     "DEPRECATED160": DEPRECATED160,             # <<<<<<<<<<<<<<
 *     "DEPRECATED161": DEPRECATED161,
 *     "DEPRECATED162": DEPRECATED162,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED160); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 274, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED160, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":275
 *     "DEPRECATED159": DEPRECATED159,
 *     "DEPRECATED160": DEPRECATED160,
 *     "DEPRECATED161": DEPRECATED161,             # <<<<<<<<<<<<<<
 *     "DEPRECATED162": DEPRECATED162,
 *     "DEPRECATED163": DEPRECATED163,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED161); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 275, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED161, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":276
 *     "DEPRECATED160": DEPRECATED160,
 *     "DEPRECATED161": DEPRECATED161,
 *     "DEPRECATED162": DEPRECATED162,             # <<<<<<<<<<<<<<
 *     "DEPRECATED163": DEPRECATED163,
 *     "DEPRECATED164": DEPRECATED164,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED162); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 276, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED162, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":277
 *     "DEPRECATED161": DEPRECATED161,
 *     "DEPRECATED162": DEPRECATED162,
 *     "DEPRECATED163": DEPRECATED163,             # <<<<<<<<<<<<<<
 *     "DEPRECATED164": DEPRECATED164,
 *     "DEPRECATED165": DEPRECATED165,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED163); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 277, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED163, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":278
 *     "DEPRECATED162": DEPRECATED162,
 *     "DEPRECATED163": DEPRECATED163,
 *     "DEPRECATED164": DEPRECATED164,             # <<<<<<<<<<<<<<
 *     "DEPRECATED165": DEPRECATED165,
 *     "DEPRECATED166": DEPRECATED166,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED164); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 278, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED164, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":279
 *     "DEPRECATED163": DEPRECATED163,
 *     "DEPRECATED164": DEPRECATED164,
 *     "DEPRECATED165": DEPRECATED165,             # <<<<<<<<<<<<<<
 *     "DEPRECATED166": DEPRECATED166,
 *     "DEPRECATED167": DEPRECATED167,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED165); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 279, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED165, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":280
 *     "DEPRECATED164": DEPRECATED164,
 *     "DEPRECATED165": DEPRECATED165,
 *     "DEPRECATED166": DEPRECATED166,             # <<<<<<<<<<<<<<
 *     "DEPRECATED167": DEPRECATED167,
 *     "DEPRECATED168": DEPRECATED168,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED166); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 280, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED166, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":281
 *     "DEPRECATED165": DEPRECATED165,
 *     "DEPRECATED166": DEPRECATED166,
 *     "DEPRECATED167": DEPRECATED167,             # <<<<<<<<<<<<<<
 *     "DEPRECATED168": DEPRECATED168,
 *     "DEPRECATED169": DEPRECATED169,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED167); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 281, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED167, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":282
 *     "DEPRECATED166": DEPRECATED166,
 *     "DEPRECATED167": DEPRECATED167,
 *     "DEPRECATED168": DEPRECATED168,             # <<<<<<<<<<<<<<
 *     "DEPRECATED169": DEPRECATED169,
 *     "DEPRECATED170": DEPRECATED170,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED168); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 282, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED168, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":283
 *     "DEPRECATED167": DEPRECATED167,
 *     "DEPRECATED168": DEPRECATED168,
 *     "DEPRECATED169": DEPRECATED169,             # <<<<<<<<<<<<<<
 *     "DEPRECATED170": DEPRECATED170,
 *     "DEPRECATED171": DEPRECATED171,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED169); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 283, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED169, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":284
 *     "DEPRECATED168": DEPRECATED168,
 *     "DEPRECATED169": DEPRECATED169,
 *     "DEPRECATED170": DEPRECATED170,             # <<<<<<<<<<<<<<
 *     "DEPRECATED171": DEPRECATED171,
 *     "DEPRECATED172": DEPRECATED172,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED170); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 284, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED170, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":285
 *     "DEPRECATED169": DEPRECATED169,
 *     "DEPRECATED170": DEPRECATED170,
 *     "DEPRECATED171": DEPRECATED171,             # <<<<<<<<<<<<<<
 *     "DEPRECATED172": DEPRECATED172,
 *     "DEPRECATED173": DEPRECATED173,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED171); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 285, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED171, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":286
 *     "DEPRECATED170": DEPRECATED170,
 *     "DEPRECATED171": DEPRECATED171,
 *     "DEPRECATED172": DEPRECATED172,             # <<<<<<<<<<<<<<
 *     "DEPRECATED173": DEPRECATED173,
 *     "DEPRECATED174": DEPRECATED174,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED172); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 286, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED172, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":287
 *     "DEPRECATED171": DEPRECATED171,
 *     "DEPRECATED172": DEPRECATED172,
 *     "DEPRECATED173": DEPRECATED173,             # <<<<<<<<<<<<<<
 *     "DEPRECATED174": DEPRECATED174,
 *     "DEPRECATED175": DEPRECATED175,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED173); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 287, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED173, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":288
 *     "DEPRECATED172": DEPRECATED172,
 *     "DEPRECATED173": DEPRECATED173,
 *     "DEPRECATED174": DEPRECATED174,             # <<<<<<<<<<<<<<
 *     "DEPRECATED175": DEPRECATED175,
 *     "DEPRECATED176": DEPRECATED176,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED174); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 288, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED174, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":289
 *     "DEPRECATED173": DEPRECATED173,
 *     "DEPRECATED174": DEPRECATED174,
 *     "DEPRECATED175": DEPRECATED175,             # <<<<<<<<<<<<<<
 *     "DEPRECATED176": DEPRECATED176,
 *     "DEPRECATED177": DEPRECATED177,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED175); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 289, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED175, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":290
 *     "DEPRECATED174": DEPRECATED174,
 *     "DEPRECATED175": DEPRECATED175,
 *     "DEPRECATED176": DEPRECATED176,             # <<<<<<<<<<<<<<
 *     "DEPRECATED177": DEPRECATED177,
 *     "DEPRECATED178": DEPRECATED178,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED176); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 290, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED176, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":291
 *     "DEPRECATED175": DEPRECATED175,
 *     "DEPRECATED176": DEPRECATED176,
 *     "DEPRECATED177": DEPRECATED177,             # <<<<<<<<<<<<<<
 *     "DEPRECATED178": DEPRECATED178,
 *     "DEPRECATED179": DEPRECATED179,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED177); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 291, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED177, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":292
 *     "DEPRECATED176": DEPRECATED176,
 *     "DEPRECATED177": DEPRECATED177,
 *     "DEPRECATED178": DEPRECATED178,             # <<<<<<<<<<<<<<
 *     "DEPRECATED179": DEPRECATED179,
 *     "DEPRECATED180": DEPRECATED180,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED178); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 292, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED178, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":293
 *     "DEPRECATED177": DEPRECATED177,
 *     "DEPRECATED178": DEPRECATED178,
 *     "DEPRECATED179": DEPRECATED179,             # <<<<<<<<<<<<<<
 *     "DEPRECATED180": DEPRECATED180,
 *     "DEPRECATED181": DEPRECATED181,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED179); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 293, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED179, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":294
 *     "DEPRECATED178": DEPRECATED178,
 *     "DEPRECATED179": DEPRECATED179,
 *     "DEPRECATED180": DEPRECATED180,             # <<<<<<<<<<<<<<
 *     "DEPRECATED181": DEPRECATED181,
 *     "DEPRECATED182": DEPRECATED182,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED180); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 294, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED180, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":295
 *     "DEPRECATED179": DEPRECATED179,
 *     "DEPRECATED180": DEPRECATED180,
 *     "DEPRECATED181": DEPRECATED181,             # <<<<<<<<<<<<<<
 *     "DEPRECATED182": DEPRECATED182,
 *     "DEPRECATED183": DEPRECATED183,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED181); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 295, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED181, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":296
 *     "DEPRECATED180": DEPRECATED180,
 *     "DEPRECATED181": DEPRECATED181,
 *     "DEPRECATED182": DEPRECATED182,             # <<<<<<<<<<<<<<
 *     "DEPRECATED183": DEPRECATED183,
 *     "DEPRECATED184": DEPRECATED184,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED182); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 296, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED182, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":297
 *     "DEPRECATED181": DEPRECATED181,
 *     "DEPRECATED182": DEPRECATED182,
 *     "DEPRECATED183": DEPRECATED183,             # <<<<<<<<<<<<<<
 *     "DEPRECATED184": DEPRECATED184,
 *     "DEPRECATED185": DEPRECATED185,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED183); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 297, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED183, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":298
 *     "DEPRECATED182": DEPRECATED182,
 *     "DEPRECATED183": DEPRECATED183,
 *     "DEPRECATED184": DEPRECATED184,             # <<<<<<<<<<<<<<
 *     "DEPRECATED185": DEPRECATED185,
 *     "DEPRECATED186": DEPRECATED186,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED184); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 298, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED184, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":299
 *     "DEPRECATED183": DEPRECATED183,
 *     "DEPRECATED184": DEPRECATED184,
 *     "DEPRECATED185": DEPRECATED185,             # <<<<<<<<<<<<<<
 *     "DEPRECATED186": DEPRECATED186,
 *     "DEPRECATED187": DEPRECATED187,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED185); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 299, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED185, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":300
 *     "DEPRECATED184": DEPRECATED184,
 *     "DEPRECATED185": DEPRECATED185,
 *     "DEPRECATED186": DEPRECATED186,             # <<<<<<<<<<<<<<
 *     "DEPRECATED187": DEPRECATED187,
 *     "DEPRECATED188": DEPRECATED188,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED186); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 300, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED186, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":301
 *     "DEPRECATED185": DEPRECATED185,
 *     "DEPRECATED186": DEPRECATED186,
 *     "DEPRECATED187": DEPRECATED187,             # <<<<<<<<<<<<<<
 *     "DEPRECATED188": DEPRECATED188,
 *     "DEPRECATED189": DEPRECATED189,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED187); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 301, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED187, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":302
 *     "DEPRECATED186": DEPRECATED186,
 *     "DEPRECATED187": DEPRECATED187,
 *     "DEPRECATED188": DEPRECATED188,             # <<<<<<<<<<<<<<
 *     "DEPRECATED189": DEPRECATED189,
 *     "DEPRECATED190": DEPRECATED190,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED188); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 302, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED188, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":303
 *     "DEPRECATED187": DEPRECATED187,
 *     "DEPRECATED188": DEPRECATED188,
 *     "DEPRECATED189": DEPRECATED189,             # <<<<<<<<<<<<<<
 *     "DEPRECATED190": DEPRECATED190,
 *     "DEPRECATED191": DEPRECATED191,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED189); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 303, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED189, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":304
 *     "DEPRECATED188": DEPRECATED188,
 *     "DEPRECATED189": DEPRECATED189,
 *     "DEPRECATED190": DEPRECATED190,             # <<<<<<<<<<<<<<
 *     "DEPRECATED191": DEPRECATED191,
 *     "DEPRECATED192": DEPRECATED192,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED190); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 304, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED190, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":305
 *     "DEPRECATED189": DEPRECATED189,
 *     "DEPRECATED190": DEPRECATED190,
 *     "DEPRECATED191": DEPRECATED191,             # <<<<<<<<<<<<<<
 *     "DEPRECATED192": DEPRECATED192,
 *     "DEPRECATED193": DEPRECATED193,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED191); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 305, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED191, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":306
 *     "DEPRECATED190": DEPRECATED190,
 *     "DEPRECATED191": DEPRECATED191,
 *     "DEPRECATED192": DEPRECATED192,             # <<<<<<<<<<<<<<
 *     "DEPRECATED193": DEPRECATED193,
 *     "DEPRECATED194": DEPRECATED194,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED192); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 306, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED192, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":307
 *     "DEPRECATED191": DEPRECATED191,
 *     "DEPRECATED192": DEPRECATED192,
 *     "DEPRECATED193": DEPRECATED193,             # <<<<<<<<<<<<<<
 *     "DEPRECATED194": DEPRECATED194,
 *     "DEPRECATED195": DEPRECATED195,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED193); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 307, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED193, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":308
 *     "DEPRECATED192": DEPRECATED192,
 *     "DEPRECATED193": DEPRECATED193,
 *     "DEPRECATED194": DEPRECATED194,             # <<<<<<<<<<<<<<
 *     "DEPRECATED195": DEPRECATED195,
 *     "DEPRECATED196": DEPRECATED196,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED194); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 308, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED194, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":309
 *     "DEPRECATED193": DEPRECATED193,
 *     "DEPRECATED194": DEPRECATED194,
 *     "DEPRECATED195": DEPRECATED195,             # <<<<<<<<<<<<<<
 *     "DEPRECATED196": DEPRECATED196,
 *     "DEPRECATED197": DEPRECATED197,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED195); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 309, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED195, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":310
 *     "DEPRECATED194": DEPRECATED194,
 *     "DEPRECATED195": DEPRECATED195,
 *     "DEPRECATED196": DEPRECATED196,             # <<<<<<<<<<<<<<
 *     "DEPRECATED197": DEPRECATED197,
 *     "DEPRECATED198": DEPRECATED198,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED196); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 310, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED196, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":311
 *     "DEPRECATED195": DEPRECATED195,
 *     "DEPRECATED196": DEPRECATED196,
 *     "DEPRECATED197": DEPRECATED197,             # <<<<<<<<<<<<<<
 *     "DEPRECATED198": DEPRECATED198,
 *     "DEPRECATED199": DEPRECATED199,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED197); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 311, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED197, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":312
 *     "DEPRECATED196": DEPRECATED196,
 *     "DEPRECATED197": DEPRECATED197,
 *     "DEPRECATED198": DEPRECATED198,             # <<<<<<<<<<<<<<
 *     "DEPRECATED199": DEPRECATED199,
 *     "DEPRECATED200": DEPRECATED200,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED198); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 312, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED198, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":313
 *     "DEPRECATED197": DEPRECATED197,
 *     "DEPRECATED198": DEPRECATED198,
 *     "DEPRECATED199": DEPRECATED199,             # <<<<<<<<<<<<<<
 *     "DEPRECATED200": DEPRECATED200,
 *     "DEPRECATED201": DEPRECATED201,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED199); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 313, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED199, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":314
 *     "DEPRECATED198": DEPRECATED198,
 *     "DEPRECATED199": DEPRECATED199,
 *     "DEPRECATED200": DEPRECATED200,             # <<<<<<<<<<<<<<
 *     "DEPRECATED201": DEPRECATED201,
 *     "DEPRECATED202": DEPRECATED202,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED200); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 314, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED200, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":315
 *     "DEPRECATED199": DEPRECATED199,
 *     "DEPRECATED200": DEPRECATED200,
 *     "DEPRECATED201": DEPRECATED201,             # <<<<<<<<<<<<<<
 *     "DEPRECATED202": DEPRECATED202,
 *     "DEPRECATED203": DEPRECATED203,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED201); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 315, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED201, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":316
 *     "DEPRECATED200": DEPRECATED200,
 *     "DEPRECATED201": DEPRECATED201,
 *     "DEPRECATED202": DEPRECATED202,             # <<<<<<<<<<<<<<
 *     "DEPRECATED203": DEPRECATED203,
 *     "DEPRECATED204": DEPRECATED204,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED202); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 316, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED202, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":317
 *     "DEPRECATED201": DEPRECATED201,
 *     "DEPRECATED202": DEPRECATED202,
 *     "DEPRECATED203": DEPRECATED203,             # <<<<<<<<<<<<<<
 *     "DEPRECATED204": DEPRECATED204,
 *     "DEPRECATED205": DEPRECATED205,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED203); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 317, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED203, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":318
 *     "DEPRECATED202": DEPRECATED202,
 *     "DEPRECATED203": DEPRECATED203,
 *     "DEPRECATED204": DEPRECATED204,             # <<<<<<<<<<<<<<
 *     "DEPRECATED205": DEPRECATED205,
 *     "DEPRECATED206": DEPRECATED206,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED204); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 318, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED204, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":319
 *     "DEPRECATED203": DEPRECATED203,
 *     "DEPRECATED204": DEPRECATED204,
 *     "DEPRECATED205": DEPRECATED205,             # <<<<<<<<<<<<<<
 *     "DEPRECATED206": DEPRECATED206,
 *     "DEPRECATED207": DEPRECATED207,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED205); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 319, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED205, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":320
 *     "DEPRECATED204": DEPRECATED204,
 *     "DEPRECATED205": DEPRECATED205,
 *     "DEPRECATED206": DEPRECATED206,             # <<<<<<<<<<<<<<
 *     "DEPRECATED207": DEPRECATED207,
 *     "DEPRECATED208": DEPRECATED208,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED206); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 320, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED206, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":321
 *     "DEPRECATED205": DEPRECATED205,
 *     "DEPRECATED206": DEPRECATED206,
 *     "DEPRECATED207": DEPRECATED207,             # <<<<<<<<<<<<<<
 *     "DEPRECATED208": DEPRECATED208,
 *     "DEPRECATED209": DEPRECATED209,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED207); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 321, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED207, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":322
 *     "DEPRECATED206": DEPRECATED206,
 *     "DEPRECATED207": DEPRECATED207,
 *     "DEPRECATED208": DEPRECATED208,             # <<<<<<<<<<<<<<
 *     "DEPRECATED209": DEPRECATED209,
 *     "DEPRECATED210": DEPRECATED210,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED208); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 322, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED208, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":323
 *     "DEPRECATED207": DEPRECATED207,
 *     "DEPRECATED208": DEPRECATED208,
 *     "DEPRECATED209": DEPRECATED209,             # <<<<<<<<<<<<<<
 *     "DEPRECATED210": DEPRECATED210,
 *     "DEPRECATED211": DEPRECATED211,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED209); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 323, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED209, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":324
 *     "DEPRECATED208": DEPRECATED208,
 *     "DEPRECATED209": DEPRECATED209,
 *     "DEPRECATED210": DEPRECATED210,             # <<<<<<<<<<<<<<
 *     "DEPRECATED211": DEPRECATED211,
 *     "DEPRECATED212": DEPRECATED212,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED210); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 324, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED210, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":325
 *     "DEPRECATED209": DEPRECATED209,
 *     "DEPRECATED210": DEPRECATED210,
 *     "DEPRECATED211": DEPRECATED211,             # <<<<<<<<<<<<<<
 *     "DEPRECATED212": DEPRECATED212,
 *     "DEPRECATED213": DEPRECATED213,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED211); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 325, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED211, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":326
 *     "DEPRECATED210": DEPRECATED210,
 *     "DEPRECATED211": DEPRECATED211,
 *     "DEPRECATED212": DEPRECATED212,             # <<<<<<<<<<<<<<
 *     "DEPRECATED213": DEPRECATED213,
 *     "DEPRECATED214": DEPRECATED214,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED212); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 326, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED212, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":327
 *     "DEPRECATED211": DEPRECATED211,
 *     "DEPRECATED212": DEPRECATED212,
 *     "DEPRECATED213": DEPRECATED213,             # <<<<<<<<<<<<<<
 *     "DEPRECATED214": DEPRECATED214,
 *     "DEPRECATED215": DEPRECATED215,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED213); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 327, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED213, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":328
 *     "DEPRECATED212": DEPRECATED212,
 *     "DEPRECATED213": DEPRECATED213,
 *     "DEPRECATED214": DEPRECATED214,             # <<<<<<<<<<<<<<
 *     "DEPRECATED215": DEPRECATED215,
 *     "DEPRECATED216": DEPRECATED216,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED214); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 328, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED214, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":329
 *     "DEPRECATED213": DEPRECATED213,
 *     "DEPRECATED214": DEPRECATED214,
 *     "DEPRECATED215": DEPRECATED215,             # <<<<<<<<<<<<<<
 *     "DEPRECATED216": DEPRECATED216,
 *     "DEPRECATED217": DEPRECATED217,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED215); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 329, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED215, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":330
 *     "DEPRECATED214": DEPRECATED214,
 *     "DEPRECATED215": DEPRECATED215,
 *     "DEPRECATED216": DEPRECATED216,             # <<<<<<<<<<<<<<
 *     "DEPRECATED217": DEPRECATED217,
 *     "DEPRECATED218": DEPRECATED218,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED216); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 330, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED216, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":331
 *     "DEPRECATED215": DEPRECATED215,
 *     "DEPRECATED216": DEPRECATED216,
 *     "DEPRECATED217": DEPRECATED217,             # <<<<<<<<<<<<<<
 *     "DEPRECATED218": DEPRECATED218,
 *     "DEPRECATED219": DEPRECATED219,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED217); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 331, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED217, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":332
 *     "DEPRECATED216": DEPRECATED216,
 *     "DEPRECATED217": DEPRECATED217,
 *     "DEPRECATED218": DEPRECATED218,             # <<<<<<<<<<<<<<
 *     "DEPRECATED219": DEPRECATED219,
 *     "DEPRECATED220": DEPRECATED220,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED218); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 332, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED218, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":333
 *     "DEPRECATED217": DEPRECATED217,
 *     "DEPRECATED218": DEPRECATED218,
 *     "DEPRECATED219": DEPRECATED219,             # <<<<<<<<<<<<<<
 *     "DEPRECATED220": DEPRECATED220,
 *     "DEPRECATED221": DEPRECATED221,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED219); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 333, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED219, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":334
 *     "DEPRECATED218": DEPRECATED218,
 *     "DEPRECATED219": DEPRECATED219,
 *     "DEPRECATED220": DEPRECATED220,             # <<<<<<<<<<<<<<
 *     "DEPRECATED221": DEPRECATED221,
 *     "DEPRECATED222": DEPRECATED222,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED220); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 334, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED220, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":335
 *     "DEPRECATED219": DEPRECATED219,
 *     "DEPRECATED220": DEPRECATED220,
 *     "DEPRECATED221": DEPRECATED221,             # <<<<<<<<<<<<<<
 *     "DEPRECATED222": DEPRECATED222,
 *     "DEPRECATED223": DEPRECATED223,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED221); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 335, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED221, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":336
 *     "DEPRECATED220": DEPRECATED220,
 *     "DEPRECATED221": DEPRECATED221,
 *     "DEPRECATED222": DEPRECATED222,             # <<<<<<<<<<<<<<
 *     "DEPRECATED223": DEPRECATED223,
 *     "DEPRECATED224": DEPRECATED224,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED222); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 336, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED222, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":337
 *     "DEPRECATED221": DEPRECATED221,
 *     "DEPRECATED222": DEPRECATED222,
 *     "DEPRECATED223": DEPRECATED223,             # <<<<<<<<<<<<<<
 *     "DEPRECATED224": DEPRECATED224,
 *     "DEPRECATED225": DEPRECATED225,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED223); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 337, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED223, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":338
 *     "DEPRECATED222": DEPRECATED222,
 *     "DEPRECATED223": DEPRECATED223,
 *     "DEPRECATED224": DEPRECATED224,             # <<<<<<<<<<<<<<
 *     "DEPRECATED225": DEPRECATED225,
 *     "DEPRECATED226": DEPRECATED226,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED224); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 338, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED224, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":339
 *     "DEPRECATED223": DEPRECATED223,
 *     "DEPRECATED224": DEPRECATED224,
 *     "DEPRECATED225": DEPRECATED225,             # <<<<<<<<<<<<<<
 *     "DEPRECATED226": DEPRECATED226,
 *     "DEPRECATED227": DEPRECATED227,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED225); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 339, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED225, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":340
 *     "DEPRECATED224": DEPRECATED224,
 *     "DEPRECATED225": DEPRECATED225,
 *     "DEPRECATED226": DEPRECATED226,             # <<<<<<<<<<<<<<
 *     "DEPRECATED227": DEPRECATED227,
 *     "DEPRECATED228": DEPRECATED228,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED226); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 340, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED226, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":341
 *     "DEPRECATED225": DEPRECATED225,
 *     "DEPRECATED226": DEPRECATED226,
 *     "DEPRECATED227": DEPRECATED227,             # <<<<<<<<<<<<<<
 *     "DEPRECATED228": DEPRECATED228,
 *     "DEPRECATED229": DEPRECATED229,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED227); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 341, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED227, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":342
 *     "DEPRECATED226": DEPRECATED226,
 *     "DEPRECATED227": DEPRECATED227,
 *     "DEPRECATED228": DEPRECATED228,             # <<<<<<<<<<<<<<
 *     "DEPRECATED229": DEPRECATED229,
 *     "DEPRECATED230": DEPRECATED230,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED228); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 342, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED228, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":343
 *     "DEPRECATED227": DEPRECATED227,
 *     "DEPRECATED228": DEPRECATED228,
 *     "DEPRECATED229": DEPRECATED229,             # <<<<<<<<<<<<<<
 *     "DEPRECATED230": DEPRECATED230,
 *     "DEPRECATED231": DEPRECATED231,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED229); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 343, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED229, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":344
 *     "DEPRECATED228": DEPRECATED228,
 *     "DEPRECATED229": DEPRECATED229,
 *     "DEPRECATED230": DEPRECATED230,             # <<<<<<<<<<<<<<
 *     "DEPRECATED231": DEPRECATED231,
 *     "DEPRECATED232": DEPRECATED232,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED230); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 344, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED230, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":345
 *     "DEPRECATED229": DEPRECATED229,
 *     "DEPRECATED230": DEPRECATED230,
 *     "DEPRECATED231": DEPRECATED231,             # <<<<<<<<<<<<<<
 *     "DEPRECATED232": DEPRECATED232,
 *     "DEPRECATED233": DEPRECATED233,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED231); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 345, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED231, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":346
 *     "DEPRECATED230": DEPRECATED230,
 *     "DEPRECATED231": DEPRECATED231,
 *     "DEPRECATED232": DEPRECATED232,             # <<<<<<<<<<<<<<
 *     "DEPRECATED233": DEPRECATED233,
 *     "DEPRECATED234": DEPRECATED234,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED232); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 346, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED232, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":347
 *     "DEPRECATED231": DEPRECATED231,
 *     "DEPRECATED232": DEPRECATED232,
 *     "DEPRECATED233": DEPRECATED233,             # <<<<<<<<<<<<<<
 *     "DEPRECATED234": DEPRECATED234,
 *     "DEPRECATED235": DEPRECATED235,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED233); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 347, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED233, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":348
 *     "DEPRECATED232": DEPRECATED232,
 *     "DEPRECATED233": DEPRECATED233,
 *     "DEPRECATED234": DEPRECATED234,             # <<<<<<<<<<<<<<
 *     "DEPRECATED235": DEPRECATED235,
 *     "DEPRECATED236": DEPRECATED236,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED234); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 348, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED234, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":349
 *     "DEPRECATED233": DEPRECATED233,
 *     "DEPRECATED234": DEPRECATED234,
 *     "DEPRECATED235": DEPRECATED235,             # <<<<<<<<<<<<<<
 *     "DEPRECATED236": DEPRECATED236,
 *     "DEPRECATED237": DEPRECATED237,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED235); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 349, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED235, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":350
 *     "DEPRECATED234": DEPRECATED234,
 *     "DEPRECATED235": DEPRECATED235,
 *     "DEPRECATED236": DEPRECATED236,             # <<<<<<<<<<<<<<
 *     "DEPRECATED237": DEPRECATED237,
 *     "DEPRECATED238": DEPRECATED238,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED236); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 350, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED236, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":351
 *     "DEPRECATED235": DEPRECATED235,
 *     "DEPRECATED236": DEPRECATED236,
 *     "DEPRECATED237": DEPRECATED237,             # <<<<<<<<<<<<<<
 *     "DEPRECATED238": DEPRECATED238,
 *     "DEPRECATED239": DEPRECATED239,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED237); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 351, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED237, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":352
 *     "DEPRECATED236": DEPRECATED236,
 *     "DEPRECATED237": DEPRECATED237,
 *     "DEPRECATED238": DEPRECATED238,             # <<<<<<<<<<<<<<
 *     "DEPRECATED239": DEPRECATED239,
 *     "DEPRECATED240": DEPRECATED240,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED238); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 352, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED238, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":353
 *     "DEPRECATED237": DEPRECATED237,
 *     "DEPRECATED238": DEPRECATED238,
 *     "DEPRECATED239": DEPRECATED239,             # <<<<<<<<<<<<<<
 *     "DEPRECATED240": DEPRECATED240,
 *     "DEPRECATED241": DEPRECATED241,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED239); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 353, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED239, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":354
 *     "DEPRECATED238": DEPRECATED238,
 *     "DEPRECATED239": DEPRECATED239,
 *     "DEPRECATED240": DEPRECATED240,             # <<<<<<<<<<<<<<
 *     "DEPRECATED241": DEPRECATED241,
 *     "DEPRECATED242": DEPRECATED242,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED240); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 354, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED240, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":355
 *     "DEPRECATED239": DEPRECATED239,
 *     "DEPRECATED240": DEPRECATED240,
 *     "DEPRECATED241": DEPRECATED241,             # <<<<<<<<<<<<<<
 *     "DEPRECATED242": DEPRECATED242,
 *     "DEPRECATED243": DEPRECATED243,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED241); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 355, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED241, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":356
 *     "DEPRECATED240": DEPRECATED240,
 *     "DEPRECATED241": DEPRECATED241,
 *     "DEPRECATED242": DEPRECATED242,             # <<<<<<<<<<<<<<
 *     "DEPRECATED243": DEPRECATED243,
 *     "DEPRECATED244": DEPRECATED244,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED242); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 356, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED242, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":357
 *     "DEPRECATED241": DEPRECATED241,
 *     "DEPRECATED242": DEPRECATED242,
 *     "DEPRECATED243": DEPRECATED243,             # <<<<<<<<<<<<<<
 *     "DEPRECATED244": DEPRECATED244,
 *     "DEPRECATED245": DEPRECATED245,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED243); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 357, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED243, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":358
 *     "DEPRECATED242": DEPRECATED242,
 *     "DEPRECATED243": DEPRECATED243,
 *     "DEPRECATED244": DEPRECATED244,             # <<<<<<<<<<<<<<
 *     "DEPRECATED245": DEPRECATED245,
 *     "DEPRECATED246": DEPRECATED246,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED244); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 358, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED244, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":359
 *     "DEPRECATED243": DEPRECATED243,
 *     "DEPRECATED244": DEPRECATED244,
 *     "DEPRECATED245": DEPRECATED245,             # <<<<<<<<<<<<<<
 *     "DEPRECATED246": DEPRECATED246,
 *     "DEPRECATED247": DEPRECATED247,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED245); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 359, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED245, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":360
 *     "DEPRECATED244": DEPRECATED244,
 *     "DEPRECATED245": DEPRECATED245,
 *     "DEPRECATED246": DEPRECATED246,             # <<<<<<<<<<<<<<
 *     "DEPRECATED247": DEPRECATED247,
 *     "DEPRECATED248": DEPRECATED248,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED246); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 360, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED246, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":361
 *     "DEPRECATED245": DEPRECATED245,
 *     "DEPRECATED246": DEPRECATED246,
 *     "DEPRECATED247": DEPRECATED247,             # <<<<<<<<<<<<<<
 *     "DEPRECATED248": DEPRECATED248,
 *     "DEPRECATED249": DEPRECATED249,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED247); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 361, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED247, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":362
 *     "DEPRECATED246": DEPRECATED246,
 *     "DEPRECATED247": DEPRECATED247,
 *     "DEPRECATED248": DEPRECATED248,             # <<<<<<<<<<<<<<
 *     "DEPRECATED249": DEPRECATED249,
 *     "DEPRECATED250": DEPRECATED250,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED248); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 362, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED248, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":363
 *     "DEPRECATED247": DEPRECATED247,
 *     "DEPRECATED248": DEPRECATED248,
 *     "DEPRECATED249": DEPRECATED249,             # <<<<<<<<<<<<<<
 *     "DEPRECATED250": DEPRECATED250,
 *     "DEPRECATED251": DEPRECATED251,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED249); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 363, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED249, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":364
 *     "DEPRECATED248": DEPRECATED248,
 *     "DEPRECATED249": DEPRECATED249,
 *     "DEPRECATED250": DEPRECATED250,             # <<<<<<<<<<<<<<
 *     "DEPRECATED251": DEPRECATED251,
 *     "DEPRECATED252": DEPRECATED252,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED250); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 364, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED250, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":365
 *     "DEPRECATED249": DEPRECATED249,
 *     "DEPRECATED250": DEPRECATED250,
 *     "DEPRECATED251": DEPRECATED251,             # <<<<<<<<<<<<<<
 *     "DEPRECATED252": DEPRECATED252,
 *     "DEPRECATED253": DEPRECATED253,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED251); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 365, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED251, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":366
 *     "DEPRECATED250": DEPRECATED250,
 *     "DEPRECATED251": DEPRECATED251,
 *     "DEPRECATED252": DEPRECATED252,             # <<<<<<<<<<<<<<
 *     "DEPRECATED253": DEPRECATED253,
 *     "DEPRECATED254": DEPRECATED254,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED252); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 366, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED252, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":367
 *     "DEPRECATED251": DEPRECATED251,
 *     "DEPRECATED252": DEPRECATED252,
 *     "DEPRECATED253": DEPRECATED253,             # <<<<<<<<<<<<<<
 *     "DEPRECATED254": DEPRECATED254,
 *     "DEPRECATED255": DEPRECATED255,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED253); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 367, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED253, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":368
 *     "DEPRECATED252": DEPRECATED252,
 *     "DEPRECATED253": DEPRECATED253,
 *     "DEPRECATED254": DEPRECATED254,             # <<<<<<<<<<<<<<
 *     "DEPRECATED255": DEPRECATED255,
 *     "DEPRECATED256": DEPRECATED256,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED254); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 368, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED254, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":369
 *     "DEPRECATED253": DEPRECATED253,
 *     "DEPRECATED254": DEPRECATED254,
 *     "DEPRECATED255": DEPRECATED255,             # <<<<<<<<<<<<<<
 *     "DEPRECATED256": DEPRECATED256,
 *     "DEPRECATED257": DEPRECATED257,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED255); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 369, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED255, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":370
 *     "DEPRECATED254": DEPRECATED254,
 *     "DEPRECATED255": DEPRECATED255,
 *     "DEPRECATED256": DEPRECATED256,             # <<<<<<<<<<<<<<
 *     "DEPRECATED257": DEPRECATED257,
 *     "DEPRECATED258": DEPRECATED258,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED256); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 370, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED256, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":371
 *     "DEPRECATED255": DEPRECATED255,
 *     "DEPRECATED256": DEPRECATED256,
 *     "DEPRECATED257": DEPRECATED257,             # <<<<<<<<<<<<<<
 *     "DEPRECATED258": DEPRECATED258,
 *     "DEPRECATED259": DEPRECATED259,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED257); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 371, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED257, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":372
 *     "DEPRECATED256": DEPRECATED256,
 *     "DEPRECATED257": DEPRECATED257,
 *     "DEPRECATED258": DEPRECATED258,             # <<<<<<<<<<<<<<
 *     "DEPRECATED259": DEPRECATED259,
 *     "DEPRECATED260": DEPRECATED260,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED258); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 372, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED258, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":373
 *     "DEPRECATED257": DEPRECATED257,
 *     "DEPRECATED258": DEPRECATED258,
 *     "DEPRECATED259": DEPRECATED259,             # <<<<<<<<<<<<<<
 *     "DEPRECATED260": DEPRECATED260,
 *     "DEPRECATED261": DEPRECATED261,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED259); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 373, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED259, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":374
 *     "DEPRECATED258": DEPRECATED258,
 *     "DEPRECATED259": DEPRECATED259,
 *     "DEPRECATED260": DEPRECATED260,             # <<<<<<<<<<<<<<
 *     "DEPRECATED261": DEPRECATED261,
 *     "DEPRECATED262": DEPRECATED262,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED260); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 374, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED260, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":375
 *     "DEPRECATED259": DEPRECATED259,
 *     "DEPRECATED260": DEPRECATED260,
 *     "DEPRECATED261": DEPRECATED261,             # <<<<<<<<<<<<<<
 *     "DEPRECATED262": DEPRECATED262,
 *     "DEPRECATED263": DEPRECATED263,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED261); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 375, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED261, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":376
 *     "DEPRECATED260": DEPRECATED260,
 *     "DEPRECATED261": DEPRECATED261,
 *     "DEPRECATED262": DEPRECATED262,             # <<<<<<<<<<<<<<
 *     "DEPRECATED263": DEPRECATED263,
 *     "DEPRECATED264": DEPRECATED264,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED262); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 376, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED262, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":377
 *     "DEPRECATED261": DEPRECATED261,
 *     "DEPRECATED262": DEPRECATED262,
 *     "DEPRECATED263": DEPRECATED263,             # <<<<<<<<<<<<<<
 *     "DEPRECATED264": DEPRECATED264,
 *     "DEPRECATED265": DEPRECATED265,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED263); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 377, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED263, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":378
 *     "DEPRECATED262": DEPRECATED262,
 *     "DEPRECATED263": DEPRECATED263,
 *     "DEPRECATED264": DEPRECATED264,             # <<<<<<<<<<<<<<
 *     "DEPRECATED265": DEPRECATED265,
 *     "DEPRECATED266": DEPRECATED266,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED264); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 378, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED264, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":379
 *     "DEPRECATED263": DEPRECATED263,
 *     "DEPRECATED264": DEPRECATED264,
 *     "DEPRECATED265": DEPRECATED265,             # <<<<<<<<<<<<<<
 *     "DEPRECATED266": DEPRECATED266,
 *     "DEPRECATED267": DEPRECATED267,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED265); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 379, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED265, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":380
 *     "DEPRECATED264": DEPRECATED264,
 *     "DEPRECATED265": DEPRECATED265,
 *     "DEPRECATED266": DEPRECATED266,             # <<<<<<<<<<<<<<
 *     "DEPRECATED267": DEPRECATED267,
 *     "DEPRECATED268": DEPRECATED268,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED266); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 380, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED266, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":381
 *     "DEPRECATED265": DEPRECATED265,
 *     "DEPRECATED266": DEPRECATED266,
 *     "DEPRECATED267": DEPRECATED267,             # <<<<<<<<<<<<<<
 *     "DEPRECATED268": DEPRECATED268,
 *     "DEPRECATED269": DEPRECATED269,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED267); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 381, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED267, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":382
 *     "DEPRECATED266": DEPRECATED266,
 *     "DEPRECATED267": DEPRECATED267,
 *     "DEPRECATED268": DEPRECATED268,             # <<<<<<<<<<<<<<
 *     "DEPRECATED269": DEPRECATED269,
 *     "DEPRECATED270": DEPRECATED270,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED268); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 382, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED268, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":383
 *     "DEPRECATED267": DEPRECATED267,
 *     "DEPRECATED268": DEPRECATED268,
 *     "DEPRECATED269": DEPRECATED269,             # <<<<<<<<<<<<<<
 *     "DEPRECATED270": DEPRECATED270,
 *     "DEPRECATED271": DEPRECATED271,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED269); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 383, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED269, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":384
 *     "DEPRECATED268": DEPRECATED268,
 *     "DEPRECATED269": DEPRECATED269,
 *     "DEPRECATED270": DEPRECATED270,             # <<<<<<<<<<<<<<
 *     "DEPRECATED271": DEPRECATED271,
 *     "DEPRECATED272": DEPRECATED272,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED270); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 384, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED270, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":385
 *     "DEPRECATED269": DEPRECATED269,
 *     "DEPRECATED270": DEPRECATED270,
 *     "DEPRECATED271": DEPRECATED271,             # <<<<<<<<<<<<<<
 *     "DEPRECATED272": DEPRECATED272,
 *     "DEPRECATED273": DEPRECATED273,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED271); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 385, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED271, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":386
 *     "DEPRECATED270": DEPRECATED270,
 *     "DEPRECATED271": DEPRECATED271,
 *     "DEPRECATED272": DEPRECATED272,             # <<<<<<<<<<<<<<
 *     "DEPRECATED273": DEPRECATED273,
 *     "DEPRECATED274": DEPRECATED274,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED272); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 386, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED272, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":387
 *     "DEPRECATED271": DEPRECATED271,
 *     "DEPRECATED272": DEPRECATED272,
 *     "DEPRECATED273": DEPRECATED273,             # <<<<<<<<<<<<<<
 *     "DEPRECATED274": DEPRECATED274,
 *     "DEPRECATED275": DEPRECATED275,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED273); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 387, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED273, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":388
 *     "DEPRECATED272": DEPRECATED272,
 *     "DEPRECATED273": DEPRECATED273,
 *     "DEPRECATED274": DEPRECATED274,             # <<<<<<<<<<<<<<
 *     "DEPRECATED275": DEPRECATED275,
 *     "DEPRECATED276": DEPRECATED276,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED274); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 388, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED274, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":389
 *     "DEPRECATED273": DEPRECATED273,
 *     "DEPRECATED274": DEPRECATED274,
 *     "DEPRECATED275": DEPRECATED275,             # <<<<<<<<<<<<<<
 *     "DEPRECATED276": DEPRECATED276,
 * 
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED275); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 389, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED275, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":390
 *     "DEPRECATED274": DEPRECATED274,
 *     "DEPRECATED275": DEPRECATED275,
 *     "DEPRECATED276": DEPRECATED276,             # <<<<<<<<<<<<<<
 * 
 *     "PERSON": PERSON,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DEPRECATED276); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 390, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DEPRECATED276, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":392
 *     "DEPRECATED276": DEPRECATED276,
 * 
 *     "PERSON": PERSON,             # <<<<<<<<<<<<<<
 *     "NORP": NORP,
 *     "FACILITY": FACILITY,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_PERSON); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 392, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_PERSON, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":393
 * 
 *     "PERSON": PERSON,
 *     "NORP": NORP,             # <<<<<<<<<<<<<<
 *     "FACILITY": FACILITY,
 *     "ORG": ORG,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_NORP); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 393, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_NORP, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":394
 *     "PERSON": PERSON,
 *     "NORP": NORP,
 *     "FACILITY": FACILITY,             # <<<<<<<<<<<<<<
 *     "ORG": ORG,
 *     "GPE": GPE,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_FACILITY); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 394, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_FACILITY, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":395
 *     "NORP": NORP,
 *     "FACILITY": FACILITY,
 *     "ORG": ORG,             # <<<<<<<<<<<<<<
 *     "GPE": GPE,
 *     "LOC": LOC,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_ORG); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 395, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_ORG, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":396
 *     "FACILITY": FACILITY,
 *     "ORG": ORG,
 *     "GPE": GPE,             # <<<<<<<<<<<<<<
 *     "LOC": LOC,
 *     "PRODUCT": PRODUCT,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_GPE); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 396, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_GPE, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":397
 *     "ORG": ORG,
 *     "GPE": GPE,
 *     "LOC": LOC,             # <<<<<<<<<<<<<<
 *     "PRODUCT": PRODUCT,
 *     "EVENT": EVENT,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_LOC); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 397, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_LOC, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":398
 *     "GPE": GPE,
 *     "LOC": LOC,
 *     "PRODUCT": PRODUCT,             # <<<<<<<<<<<<<<
 *     "EVENT": EVENT,
 *     "WORK_OF_ART": WORK_OF_ART,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_PRODUCT); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 398, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_PRODUCT, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":399
 *     "LOC": LOC,
 *     "PRODUCT": PRODUCT,
 *     "EVENT": EVENT,             # <<<<<<<<<<<<<<
 *     "WORK_OF_ART": WORK_OF_ART,
 *     "LANGUAGE": LANGUAGE,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_EVENT); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 399, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_EVENT, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":400
 *     "PRODUCT": PRODUCT,
 *     "EVENT": EVENT,
 *     "WORK_OF_ART": WORK_OF_ART,             # <<<<<<<<<<<<<<
 *     "LANGUAGE": LANGUAGE,
 * 
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_WORK_OF_ART); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 400, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_WORK_OF_ART, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":401
 *     "EVENT": EVENT,
 *     "WORK_OF_ART": WORK_OF_ART,
 *     "LANGUAGE": LANGUAGE,             # <<<<<<<<<<<<<<
 * 
 *     "DATE": DATE,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_LANGUAGE); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 401, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_LANGUAGE, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":403
 *     "LANGUAGE": LANGUAGE,
 * 
 *     "DATE": DATE,             # <<<<<<<<<<<<<<
 *     "TIME": TIME,
 *     "PERCENT": PERCENT,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_DATE); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 403, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_DATE, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":404
 * 
 *     "DATE": DATE,
 *     "TIME": TIME,             # <<<<<<<<<<<<<<
 *     "PERCENT": PERCENT,
 *     "MONEY": MONEY,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_TIME); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 404, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_TIME, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":405
 *     "DATE": DATE,
 *     "TIME": TIME,
 *     "PERCENT": PERCENT,             # <<<<<<<<<<<<<<
 *     "MONEY": MONEY,
 *     "QUANTITY": QUANTITY,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_PERCENT); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 405, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_PERCENT, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":406
 *     "TIME": TIME,
 *     "PERCENT": PERCENT,
 *     "MONEY": MONEY,             # <<<<<<<<<<<<<<
 *     "QUANTITY": QUANTITY,
 *     "ORDINAL": ORDINAL,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_MONEY); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 406, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_MONEY, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":407
 *     "PERCENT": PERCENT,
 *     "MONEY": MONEY,
 *     "QUANTITY": QUANTITY,             # <<<<<<<<<<<<<<
 *     "ORDINAL": ORDINAL,
 *     "CARDINAL": CARDINAL,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_QUANTITY); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 407, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_QUANTITY, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":408
 *     "MONEY": MONEY,
 *     "QUANTITY": QUANTITY,
 *     "ORDINAL": ORDINAL,             # <<<<<<<<<<<<<<
 *     "CARDINAL": CARDINAL,
 * 
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_ORDINAL); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 408, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_ORDINAL, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":409
 *     "QUANTITY": QUANTITY,
 *     "ORDINAL": ORDINAL,
 *     "CARDINAL": CARDINAL,             # <<<<<<<<<<<<<<
 * 
 *     "acomp": acomp,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_CARDINAL); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 409, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_CARDINAL, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":411
 *     "CARDINAL": CARDINAL,
 * 
 *     "acomp": acomp,             # <<<<<<<<<<<<<<
 *     "advcl": advcl,
 *     "advmod": advmod,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_acomp); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 411, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_acomp, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":412
 * 
 *     "acomp": acomp,
 *     "advcl": advcl,             # <<<<<<<<<<<<<<
 *     "advmod": advmod,
 *     "agent": agent,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_advcl); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 412, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_advcl, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":413
 *     "acomp": acomp,
 *     "advcl": advcl,
 *     "advmod": advmod,             # <<<<<<<<<<<<<<
 *     "agent": agent,
 *     "amod": amod,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_advmod); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 413, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_advmod, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":414
 *     "advcl": advcl,
 *     "advmod": advmod,
 *     "agent": agent,             # <<<<<<<<<<<<<<
 *     "amod": amod,
 *     "appos": appos,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_agent); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 414, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_agent, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":415
 *     "advmod": advmod,
 *     "agent": agent,
 *     "amod": amod,             # <<<<<<<<<<<<<<
 *     "appos": appos,
 *     "attr": attr,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_amod); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 415, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_amod, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":416
 *     "agent": agent,
 *     "amod": amod,
 *     "appos": appos,             # <<<<<<<<<<<<<<
 *     "attr": attr,
 *     "aux": aux,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_appos); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 416, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_appos, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":417
 *     "amod": amod,
 *     "appos": appos,
 *     "attr": attr,             # <<<<<<<<<<<<<<
 *     "aux": aux,
 *     "auxpass": auxpass,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_attr); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 417, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_attr, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":418
 *     "appos": appos,
 *     "attr": attr,
 *     "aux": aux,             # <<<<<<<<<<<<<<
 *     "auxpass": auxpass,
 *     "cc": cc,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_aux); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 418, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_aux, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":419
 *     "attr": attr,
 *     "aux": aux,
 *     "auxpass": auxpass,             # <<<<<<<<<<<<<<
 *     "cc": cc,
 *     "ccomp": ccomp,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_auxpass); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 419, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_auxpass, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":420
 *     "aux": aux,
 *     "auxpass": auxpass,
 *     "cc": cc,             # <<<<<<<<<<<<<<
 *     "ccomp": ccomp,
 *     "complm": complm,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_cc); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 420, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_cc, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":421
 *     "auxpass": auxpass,
 *     "cc": cc,
 *     "ccomp": ccomp,             # <<<<<<<<<<<<<<
 *     "complm": complm,
 *     "conj": conj,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_ccomp); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 421, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_ccomp, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":422
 *     "cc": cc,
 *     "ccomp": ccomp,
 *     "complm": complm,             # <<<<<<<<<<<<<<
 *     "conj": conj,
 *     "cop": cop, # U20
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_complm); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 422, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_complm, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":423
 *     "ccomp": ccomp,
 *     "complm": complm,
 *     "conj": conj,             # <<<<<<<<<<<<<<
 *     "cop": cop, # U20
 *     "csubj": csubj,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_conj); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 423, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_conj, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":424
 *     "complm": complm,
 *     "conj": conj,
 *     "cop": cop, # U20             # <<<<<<<<<<<<<<
 *     "csubj": csubj,
 *     "csubjpass": csubjpass,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_cop); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 424, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_cop, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":425
 *     "conj": conj,
 *     "cop": cop, # U20
 *     "csubj": csubj,             # <<<<<<<<<<<<<<
 *     "csubjpass": csubjpass,
 *     "dep": dep,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_csubj); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 425, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_csubj, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":426
 *     "cop": cop, # U20
 *     "csubj": csubj,
 *     "csubjpass": csubjpass,             # <<<<<<<<<<<<<<
 *     "dep": dep,
 *     "det": det,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_csubjpass); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 426, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_csubjpass, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":427
 *     "csubj": csubj,
 *     "csubjpass": csubjpass,
 *     "dep": dep,             # <<<<<<<<<<<<<<
 *     "det": det,
 *     "dobj": dobj,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_dep); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 427, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_dep, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":428
 *     "csubjpass": csubjpass,
 *     "dep": dep,
 *     "det": det,             # <<<<<<<<<<<<<<
 *     "dobj": dobj,
 *     "expl": expl,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_det); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 428, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_det, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":429
 *     "dep": dep,
 *     "det": det,
 *     "dobj": dobj,             # <<<<<<<<<<<<<<
 *     "expl": expl,
 *     "hmod": hmod,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_dobj); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 429, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_dobj, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":430
 *     "det": det,
 *     "dobj": dobj,
 *     "expl": expl,             # <<<<<<<<<<<<<<
 *     "hmod": hmod,
 *     "hyph": hyph,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_expl); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 430, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_expl, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":431
 *     "dobj": dobj,
 *     "expl": expl,
 *     "hmod": hmod,             # <<<<<<<<<<<<<<
 *     "hyph": hyph,
 *     "infmod": infmod,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_hmod); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 431, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_hmod, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":432
 *     "expl": expl,
 *     "hmod": hmod,
 *     "hyph": hyph,             # <<<<<<<<<<<<<<
 *     "infmod": infmod,
 *     "intj": intj,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_hyph); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 432, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_hyph, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":433
 *     "hmod": hmod,
 *     "hyph": hyph,
 *     "infmod": infmod,             # <<<<<<<<<<<<<<
 *     "intj": intj,
 *     "iobj": iobj,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_infmod); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 433, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_infmod, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":434
 *     "hyph": hyph,
 *     "infmod": infmod,
 *     "intj": intj,             # <<<<<<<<<<<<<<
 *     "iobj": iobj,
 *     "mark": mark,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_intj); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 434, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_intj, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":435
 *     "infmod": infmod,
 *     "intj": intj,
 *     "iobj": iobj,             # <<<<<<<<<<<<<<
 *     "mark": mark,
 *     "meta": meta,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_iobj); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 435, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_iobj, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":436
 *     "intj": intj,
 *     "iobj": iobj,
 *     "mark": mark,             # <<<<<<<<<<<<<<
 *     "meta": meta,
 *     "neg": neg,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_mark); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 436, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_mark, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":437
 *     "iobj": iobj,
 *     "mark": mark,
 *     "meta": meta,             # <<<<<<<<<<<<<<
 *     "neg": neg,
 *     "nmod": nmod,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_meta); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 437, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_meta, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":438
 *     "mark": mark,
 *     "meta": meta,
 *     "neg": neg,             # <<<<<<<<<<<<<<
 *     "nmod": nmod,
 *     "nn": nn,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_neg); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 438, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_neg, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":439
 *     "meta": meta,
 *     "neg": neg,
 *     "nmod": nmod,             # <<<<<<<<<<<<<<
 *     "nn": nn,
 *     "npadvmod": npadvmod,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_nmod); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 439, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_nmod, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":440
 *     "neg": neg,
 *     "nmod": nmod,
 *     "nn": nn,             # <<<<<<<<<<<<<<
 *     "npadvmod": npadvmod,
 *     "nsubj": nsubj,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_nn); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 440, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_nn, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":441
 *     "nmod": nmod,
 *     "nn": nn,
 *     "npadvmod": npadvmod,             # <<<<<<<<<<<<<<
 *     "nsubj": nsubj,
 *     "nsubjpass": nsubjpass,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_npadvmod); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 441, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_npadvmod, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":442
 *     "nn": nn,
 *     "npadvmod": npadvmod,
 *     "nsubj": nsubj,             # <<<<<<<<<<<<<<
 *     "nsubjpass": nsubjpass,
 *     "num": num,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_nsubj); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 442, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_nsubj, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":443
 *     "npadvmod": npadvmod,
 *     "nsubj": nsubj,
 *     "nsubjpass": nsubjpass,             # <<<<<<<<<<<<<<
 *     "num": num,
 *     "number": number,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_nsubjpass); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 443, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_nsubjpass, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":444
 *     "nsubj": nsubj,
 *     "nsubjpass": nsubjpass,
 *     "num": num,             # <<<<<<<<<<<<<<
 *     "number": number,
 *     "oprd": oprd,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_num); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 444, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_num, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":445
 *     "nsubjpass": nsubjpass,
 *     "num": num,
 *     "number": number,             # <<<<<<<<<<<<<<
 *     "oprd": oprd,
 *     "obj": obj, # U20
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_number); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 445, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_number, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":446
 *     "num": num,
 *     "number": number,
 *     "oprd": oprd,             # <<<<<<<<<<<<<<
 *     "obj": obj, # U20
 *     "obl": obl, # U20
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_oprd); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 446, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_oprd, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":447
 *     "number": number,
 *     "oprd": oprd,
 *     "obj": obj, # U20             # <<<<<<<<<<<<<<
 *     "obl": obl, # U20
 *     "parataxis": parataxis,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_obj); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 447, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_obj, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":448
 *     "oprd": oprd,
 *     "obj": obj, # U20
 *     "obl": obl, # U20             # <<<<<<<<<<<<<<
 *     "parataxis": parataxis,
 *     "partmod": partmod,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_obl); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 448, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_obl, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":449
 *     "obj": obj, # U20
 *     "obl": obl, # U20
 *     "parataxis": parataxis,             # <<<<<<<<<<<<<<
 *     "partmod": partmod,
 *     "pcomp": pcomp,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_parataxis); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 449, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_parataxis, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":450
 *     "obl": obl, # U20
 *     "parataxis": parataxis,
 *     "partmod": partmod,             # <<<<<<<<<<<<<<
 *     "pcomp": pcomp,
 *     "pobj": pobj,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_partmod); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 450, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_partmod, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":451
 *     "parataxis": parataxis,
 *     "partmod": partmod,
 *     "pcomp": pcomp,             # <<<<<<<<<<<<<<
 *     "pobj": pobj,
 *     "poss": poss,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_pcomp); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 451, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_pcomp, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":452
 *     "partmod": partmod,
 *     "pcomp": pcomp,
 *     "pobj": pobj,             # <<<<<<<<<<<<<<
 *     "poss": poss,
 *     "possessive": possessive,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_pobj); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 452, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_pobj, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":453
 *     "pcomp": pcomp,
 *     "pobj": pobj,
 *     "poss": poss,             # <<<<<<<<<<<<<<
 *     "possessive": possessive,
 *     "preconj": preconj,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_poss); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 453, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_poss, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":454
 *     "pobj": pobj,
 *     "poss": poss,
 *     "possessive": possessive,             # <<<<<<<<<<<<<<
 *     "preconj": preconj,
 *     "prep": prep,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_possessive); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 454, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_possessive, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":455
 *     "poss": poss,
 *     "possessive": possessive,
 *     "preconj": preconj,             # <<<<<<<<<<<<<<
 *     "prep": prep,
 *     "prt": prt,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_preconj); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 455, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_preconj, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":456
 *     "possessive": possessive,
 *     "preconj": preconj,
 *     "prep": prep,             # <<<<<<<<<<<<<<
 *     "prt": prt,
 *     "punct": punct,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_prep); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 456, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_prep, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":457
 *     "preconj": preconj,
 *     "prep": prep,
 *     "prt": prt,             # <<<<<<<<<<<<<<
 *     "punct": punct,
 *     "quantmod": quantmod,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_prt); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 457, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_prt, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":458
 *     "prep": prep,
 *     "prt": prt,
 *     "punct": punct,             # <<<<<<<<<<<<<<
 *     "quantmod": quantmod,
 *     "rcmod": rcmod,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_punct); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 458, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_punct, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":459
 *     "prt": prt,
 *     "punct": punct,
 *     "quantmod": quantmod,             # <<<<<<<<<<<<<<
 *     "rcmod": rcmod,
 *     "relcl": relcl,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_quantmod); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 459, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_quantmod, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":460
 *     "punct": punct,
 *     "quantmod": quantmod,
 *     "rcmod": rcmod,             # <<<<<<<<<<<<<<
 *     "relcl": relcl,
 *     "root": root,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_rcmod); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 460, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_rcmod, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":461
 *     "quantmod": quantmod,
 *     "rcmod": rcmod,
 *     "relcl": relcl,             # <<<<<<<<<<<<<<
 *     "root": root,
 *     "xcomp": xcomp,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_relcl); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 461, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_relcl, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":462
 *     "rcmod": rcmod,
 *     "relcl": relcl,
 *     "root": root,             # <<<<<<<<<<<<<<
 *     "xcomp": xcomp,
 * 
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_root); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 462, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_root, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":463
 *     "relcl": relcl,
 *     "root": root,
 *     "xcomp": xcomp,             # <<<<<<<<<<<<<<
 * 
 *     "acl": acl,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_xcomp); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 463, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_xcomp, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":465
 *     "xcomp": xcomp,
 * 
 *     "acl": acl,             # <<<<<<<<<<<<<<
 *     "LAW": LAW,
 *     "MORPH": MORPH,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_acl); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 465, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_acl, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":466
 * 
 *     "acl": acl,
 *     "LAW": LAW,             # <<<<<<<<<<<<<<
 *     "MORPH": MORPH,
 *     "_": _,
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_LAW); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 466, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_LAW, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":467
 *     "acl": acl,
 *     "LAW": LAW,
 *     "MORPH": MORPH,             # <<<<<<<<<<<<<<
 *     "_": _,
 * }
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols_MORPH); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 467, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_MORPH, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/symbols.pyx":468
 *     "LAW": LAW,
 *     "MORPH": MORPH,
 *     "_": _,             # <<<<<<<<<<<<<<
 * }
 * 
 */
  __pyx_t_2 = __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(__pyx_e_5spacy_7symbols__); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 468, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s__2, __pyx_t_2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_IDS, __pyx_t_1) < 0) __PYX_ERR(0, 2, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/symbols.pyx":472
 * 
 * 
 * def sort_nums(x):             # <<<<<<<<<<<<<<
 *     return x[1]
 * 
 */
  __pyx_t_1 = PyCFunction_NewEx(&__pyx_mdef_5spacy_7symbols_1sort_nums, NULL, __pyx_n_s_spacy_symbols); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 472, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_sort_nums, __pyx_t_1) < 0) __PYX_ERR(0, 472, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/symbols.pyx":476
 * 
 * 
 * NAMES = [it[0] for it in sorted(IDS.items(), key=sort_nums)]             # <<<<<<<<<<<<<<
 * # Unfortunate hack here, to work around problem with long cpdef enum
 * # (which is generating an enormous amount of C++ in Cython 0.24+)
 */
  __pyx_t_1 = PyList_New(0); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 476, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_IDS); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 476, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_3 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_items); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 476, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_2 = __Pyx_PyObject_CallNoArg(__pyx_t_3); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 476, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_3 = PyTuple_New(1); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 476, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __Pyx_GIVEREF(__pyx_t_2);
  PyTuple_SET_ITEM(__pyx_t_3, 0, __pyx_t_2);
  __pyx_t_2 = 0;
  __pyx_t_2 = __Pyx_PyDict_NewPresized(1); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 476, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_GetModuleGlobalName(__pyx_t_4, __pyx_n_s_sort_nums); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 476, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_4);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_key, __pyx_t_4) < 0) __PYX_ERR(0, 476, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
  __pyx_t_4 = __Pyx_PyObject_Call(__pyx_builtin_sorted, __pyx_t_3, __pyx_t_2); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 476, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_4);
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  if (likely(PyList_CheckExact(__pyx_t_4)) || PyTuple_CheckExact(__pyx_t_4)) {
    __pyx_t_2 = __pyx_t_4; __Pyx_INCREF(__pyx_t_2); __pyx_t_5 = 0;
    __pyx_t_6 = NULL;
  } else {
    __pyx_t_5 = -1; __pyx_t_2 = PyObject_GetIter(__pyx_t_4); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 476, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __pyx_t_6 = Py_TYPE(__pyx_t_2)->tp_iternext; if (unlikely(!__pyx_t_6)) __PYX_ERR(0, 476, __pyx_L1_error)
  }
  __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
  for (;;) {
    if (likely(!__pyx_t_6)) {
      if (likely(PyList_CheckExact(__pyx_t_2))) {
        if (__pyx_t_5 >= PyList_GET_SIZE(__pyx_t_2)) break;
        #if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS
        __pyx_t_4 = PyList_GET_ITEM(__pyx_t_2, __pyx_t_5); __Pyx_INCREF(__pyx_t_4); __pyx_t_5++; if (unlikely(0 < 0)) __PYX_ERR(0, 476, __pyx_L1_error)
        #else
        __pyx_t_4 = PySequence_ITEM(__pyx_t_2, __pyx_t_5); __pyx_t_5++; if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 476, __pyx_L1_error)
        __Pyx_GOTREF(__pyx_t_4);
        #endif
      } else {
        if (__pyx_t_5 >= PyTuple_GET_SIZE(__pyx_t_2)) break;
        #if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS
        __pyx_t_4 = PyTuple_GET_ITEM(__pyx_t_2, __pyx_t_5); __Pyx_INCREF(__pyx_t_4); __pyx_t_5++; if (unlikely(0 < 0)) __PYX_ERR(0, 476, __pyx_L1_error)
        #else
        __pyx_t_4 = PySequence_ITEM(__pyx_t_2, __pyx_t_5); __pyx_t_5++; if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 476, __pyx_L1_error)
        __Pyx_GOTREF(__pyx_t_4);
        #endif
      }
    } else {
      __pyx_t_4 = __pyx_t_6(__pyx_t_2);
      if (unlikely(!__pyx_t_4)) {
        PyObject* exc_type = PyErr_Occurred();
        if (exc_type) {
          if (likely(__Pyx_PyErr_GivenExceptionMatches(exc_type, PyExc_StopIteration))) PyErr_Clear();
          else __PYX_ERR(0, 476, __pyx_L1_error)
        }
        break;
      }
      __Pyx_GOTREF(__pyx_t_4);
    }
    if (PyDict_SetItem(__pyx_d, __pyx_n_s_it, __pyx_t_4) < 0) __PYX_ERR(0, 476, __pyx_L1_error)
    __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
    __Pyx_GetModuleGlobalName(__pyx_t_4, __pyx_n_s_it); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 476, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_4);
    __pyx_t_3 = __Pyx_GetItemInt(__pyx_t_4, 0, long, 1, __Pyx_PyInt_From_long, 0, 0, 1); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 476, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_3);
    __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
    if (unlikely(__Pyx_ListComp_Append(__pyx_t_1, (PyObject*)__pyx_t_3))) __PYX_ERR(0, 476, __pyx_L1_error)
    __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  }
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_NAMES, __pyx_t_1) < 0) __PYX_ERR(0, 476, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/symbols.pyx":480
 * # (which is generating an enormous amount of C++ in Cython 0.24+)
 * # We keep the enum cdef, and just make sure the names are available to Python
 * locals().update(IDS)             # <<<<<<<<<<<<<<
 */
  __pyx_t_1 = __Pyx_Globals(); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 480, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_t_2 = __Pyx_PyObject_GetAttrStr(__pyx_t_1, __pyx_n_s_update); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 480, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_IDS); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 480, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_t_3 = __Pyx_PyObject_CallOneArg(__pyx_t_2, __pyx_t_1); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 480, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;

  /* "spacy/symbols.pyx":1
 * # cython: optimize.unpack_method_calls=False             # <<<<<<<<<<<<<<
 * IDS = {
 *     "": NIL,
 */
  __pyx_t_3 = __Pyx_PyDict_NewPresized(0); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 1, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_test, __pyx_t_3) < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;

  /*--- Wrapped vars code ---*/

  goto __pyx_L0;
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_XDECREF(__pyx_t_2);
  __Pyx_XDECREF(__pyx_t_3);
  __Pyx_XDECREF(__pyx_t_4);
  if (__pyx_m) {
    if (__pyx_d) {
      __Pyx_AddTraceback("init spacy.symbols", __pyx_clineno, __pyx_lineno, __pyx_filename);
    }
    Py_CLEAR(__pyx_m);
  } else if (!PyErr_Occurred()) {
    PyErr_SetString(PyExc_ImportError, "init spacy.symbols");
  }
  __pyx_L0:;
  __Pyx_RefNannyFinishContext();
  #if CYTHON_PEP489_MULTI_PHASE_INIT
  return (__pyx_m != NULL) ? 0 : -1;
  #elif PY_MAJOR_VERSION >= 3
  return __pyx_m;
  #else
  return;
  #endif
}

/* --- Runtime support code --- */
/* Refnanny */
#if CYTHON_REFNANNY
static __Pyx_RefNannyAPIStruct *__Pyx_RefNannyImportAPI(const char *modname) {
    PyObject *m = NULL, *p = NULL;
    void *r = NULL;
    m = PyImport_ImportModule(modname);
    if (!m) goto end;
    p = PyObject_GetAttrString(m, "RefNannyAPI");
    if (!p) goto end;
    r = PyLong_AsVoidPtr(p);
end:
    Py_XDECREF(p);
    Py_XDECREF(m);
    return (__Pyx_RefNannyAPIStruct *)r;
}
#endif

/* PyObjectGetAttrStr */
#if CYTHON_USE_TYPE_SLOTS
static CYTHON_INLINE PyObject* __Pyx_PyObject_GetAttrStr(PyObject* obj, PyObject* attr_name) {
    PyTypeObject* tp = Py_TYPE(obj);
    if (likely(tp->tp_getattro))
        return tp->tp_getattro(obj, attr_name);
#if PY_MAJOR_VERSION < 3
    if (likely(tp->tp_getattr))
        return tp->tp_getattr(obj, PyString_AS_STRING(attr_name));
#endif
    return PyObject_GetAttr(obj, attr_name);
}
#endif

/* GetBuiltinName */
static PyObject *__Pyx_GetBuiltinName(PyObject *name) {
    PyObject* result = __Pyx_PyObject_GetAttrStr(__pyx_b, name);
    if (unlikely(!result)) {
        PyErr_Format(PyExc_NameError,
#if PY_MAJOR_VERSION >= 3
            "name '%U' is not defined", name);
#else
            "name '%.200s' is not defined", PyString_AS_STRING(name));
#endif
    }
    return result;
}

/* GetItemInt */
static PyObject *__Pyx_GetItemInt_Generic(PyObject *o, PyObject* j) {
    PyObject *r;
    if (!j) return NULL;
    r = PyObject_GetItem(o, j);
    Py_DECREF(j);
    return r;
}
static CYTHON_INLINE PyObject *__Pyx_GetItemInt_List_Fast(PyObject *o, Py_ssize_t i,
                                                              CYTHON_NCP_UNUSED int wraparound,
                                                              CYTHON_NCP_UNUSED int boundscheck) {
#if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS
    Py_ssize_t wrapped_i = i;
    if (wraparound & unlikely(i < 0)) {
        wrapped_i += PyList_GET_SIZE(o);
    }
    if ((!boundscheck) || likely(__Pyx_is_valid_index(wrapped_i, PyList_GET_SIZE(o)))) {
        PyObject *r = PyList_GET_ITEM(o, wrapped_i);
        Py_INCREF(r);
        return r;
    }
    return __Pyx_GetItemInt_Generic(o, PyInt_FromSsize_t(i));
#else
    return PySequence_GetItem(o, i);
#endif
}
static CYTHON_INLINE PyObject *__Pyx_GetItemInt_Tuple_Fast(PyObject *o, Py_ssize_t i,
                                                              CYTHON_NCP_UNUSED int wraparound,
                                                              CYTHON_NCP_UNUSED int boundscheck) {
#if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS
    Py_ssize_t wrapped_i = i;
    if (wraparound & unlikely(i < 0)) {
        wrapped_i += PyTuple_GET_SIZE(o);
    }
    if ((!boundscheck) || likely(__Pyx_is_valid_index(wrapped_i, PyTuple_GET_SIZE(o)))) {
        PyObject *r = PyTuple_GET_ITEM(o, wrapped_i);
        Py_INCREF(r);
        return r;
    }
    return __Pyx_GetItemInt_Generic(o, PyInt_FromSsize_t(i));
#else
    return PySequence_GetItem(o, i);
#endif
}
static CYTHON_INLINE PyObject *__Pyx_GetItemInt_Fast(PyObject *o, Py_ssize_t i, int is_list,
                                                     CYTHON_NCP_UNUSED int wraparound,
                                                     CYTHON_NCP_UNUSED int boundscheck) {
#if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS && CYTHON_USE_TYPE_SLOTS
    if (is_list || PyList_CheckExact(o)) {
        Py_ssize_t n = ((!wraparound) | likely(i >= 0)) ? i : i + PyList_GET_SIZE(o);
        if ((!boundscheck) || (likely(__Pyx_is_valid_index(n, PyList_GET_SIZE(o))))) {
            PyObject *r = PyList_GET_ITEM(o, n);
            Py_INCREF(r);
            return r;
        }
    }
    else if (PyTuple_CheckExact(o)) {
        Py_ssize_t n = ((!wraparound) | likely(i >= 0)) ? i : i + PyTuple_GET_SIZE(o);
        if ((!boundscheck) || likely(__Pyx_is_valid_index(n, PyTuple_GET_SIZE(o)))) {
            PyObject *r = PyTuple_GET_ITEM(o, n);
            Py_INCREF(r);
            return r;
        }
    } else {
        PySequenceMethods *m = Py_TYPE(o)->tp_as_sequence;
        if (likely(m && m->sq_item)) {
            if (wraparound && unlikely(i < 0) && likely(m->sq_length)) {
                Py_ssize_t l = m->sq_length(o);
                if (likely(l >= 0)) {
                    i += l;
                } else {
                    if (!PyErr_ExceptionMatches(PyExc_OverflowError))
                        return NULL;
                    PyErr_Clear();
                }
            }
            return m->sq_item(o, i);
        }
    }
#else
    if (is_list || PySequence_Check(o)) {
        return PySequence_GetItem(o, i);
    }
#endif
    return __Pyx_GetItemInt_Generic(o, PyInt_FromSsize_t(i));
}

/* PyDictVersioning */
#if CYTHON_USE_DICT_VERSIONS && CYTHON_USE_TYPE_SLOTS
static CYTHON_INLINE PY_UINT64_T __Pyx_get_tp_dict_version(PyObject *obj) {
    PyObject *dict = Py_TYPE(obj)->tp_dict;
    return likely(dict) ? __PYX_GET_DICT_VERSION(dict) : 0;
}
static CYTHON_INLINE PY_UINT64_T __Pyx_get_object_dict_version(PyObject *obj) {
    PyObject **dictptr = NULL;
    Py_ssize_t offset = Py_TYPE(obj)->tp_dictoffset;
    if (offset) {
#if CYTHON_COMPILING_IN_CPYTHON
        dictptr = (likely(offset > 0)) ? (PyObject **) ((char *)obj + offset) : _PyObject_GetDictPtr(obj);
#else
        dictptr = _PyObject_GetDictPtr(obj);
#endif
    }
    return (dictptr && *dictptr) ? __PYX_GET_DICT_VERSION(*dictptr) : 0;
}
static CYTHON_INLINE int __Pyx_object_dict_version_matches(PyObject* obj, PY_UINT64_T tp_dict_version, PY_UINT64_T obj_dict_version) {
    PyObject *dict = Py_TYPE(obj)->tp_dict;
    if (unlikely(!dict) || unlikely(tp_dict_version != __PYX_GET_DICT_VERSION(dict)))
        return 0;
    return obj_dict_version == __Pyx_get_object_dict_version(obj);
}
#endif

/* GetModuleGlobalName */
#if CYTHON_USE_DICT_VERSIONS
static PyObject *__Pyx__GetModuleGlobalName(PyObject *name, PY_UINT64_T *dict_version, PyObject **dict_cached_value)
#else
static CYTHON_INLINE PyObject *__Pyx__GetModuleGlobalName(PyObject *name)
#endif
{
    PyObject *result;
#if !CYTHON_AVOID_BORROWED_REFS
#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX >= 0x030500A1
    result = _PyDict_GetItem_KnownHash(__pyx_d, name, ((PyASCIIObject *) name)->hash);
    __PYX_UPDATE_DICT_CACHE(__pyx_d, result, *dict_cached_value, *dict_version)
    if (likely(result)) {
        return __Pyx_NewRef(result);
    } else if (unlikely(PyErr_Occurred())) {
        return NULL;
    }
#else
    result = PyDict_GetItem(__pyx_d, name);
    __PYX_UPDATE_DICT_CACHE(__pyx_d, result, *dict_cached_value, *dict_version)
    if (likely(result)) {
        return __Pyx_NewRef(result);
    }
#endif
#else
    result = PyObject_GetItem(__pyx_d, name);
    __PYX_UPDATE_DICT_CACHE(__pyx_d, result, *dict_cached_value, *dict_version)
    if (likely(result)) {
        return __Pyx_NewRef(result);
    }
    PyErr_Clear();
#endif
    return __Pyx_GetBuiltinName(name);
}

/* PyFunctionFastCall */
#if CYTHON_FAST_PYCALL
static PyObject* __Pyx_PyFunction_FastCallNoKw(PyCodeObject *co, PyObject **args, Py_ssize_t na,
                                               PyObject *globals) {
    PyFrameObject *f;
    PyThreadState *tstate = __Pyx_PyThreadState_Current;
    PyObject **fastlocals;
    Py_ssize_t i;
    PyObject *result;
    assert(globals != NULL);
    /* XXX Perhaps we should create a specialized
       PyFrame_New() that doesn't take locals, but does
       take builtins without sanity checking them.
       */
    assert(tstate != NULL);
    f = PyFrame_New(tstate, co, globals, NULL);
    if (f == NULL) {
        return NULL;
    }
    fastlocals = __Pyx_PyFrame_GetLocalsplus(f);
    for (i = 0; i < na; i++) {
        Py_INCREF(*args);
        fastlocals[i] = *args++;
    }
    result = PyEval_EvalFrameEx(f,0);
    ++tstate->recursion_depth;
    Py_DECREF(f);
    --tstate->recursion_depth;
    return result;
}
#if 1 || PY_VERSION_HEX < 0x030600B1
static PyObject *__Pyx_PyFunction_FastCallDict(PyObject *func, PyObject **args, Py_ssize_t nargs, PyObject *kwargs) {
    PyCodeObject *co = (PyCodeObject *)PyFunction_GET_CODE(func);
    PyObject *globals = PyFunction_GET_GLOBALS(func);
    PyObject *argdefs = PyFunction_GET_DEFAULTS(func);
    PyObject *closure;
#if PY_MAJOR_VERSION >= 3
    PyObject *kwdefs;
#endif
    PyObject *kwtuple, **k;
    PyObject **d;
    Py_ssize_t nd;
    Py_ssize_t nk;
    PyObject *result;
    assert(kwargs == NULL || PyDict_Check(kwargs));
    nk = kwargs ? PyDict_Size(kwargs) : 0;
    if (Py_EnterRecursiveCall((char*)" while calling a Python object")) {
        return NULL;
    }
    if (
#if PY_MAJOR_VERSION >= 3
            co->co_kwonlyargcount == 0 &&
#endif
            likely(kwargs == NULL || nk == 0) &&
            co->co_flags == (CO_OPTIMIZED | CO_NEWLOCALS | CO_NOFREE)) {
        if (argdefs == NULL && co->co_argcount == nargs) {
            result = __Pyx_PyFunction_FastCallNoKw(co, args, nargs, globals);
            goto done;
        }
        else if (nargs == 0 && argdefs != NULL
                 && co->co_argcount == Py_SIZE(argdefs)) {
            /* function called with no arguments, but all parameters have
               a default value: use default values as arguments .*/
            args = &PyTuple_GET_ITEM(argdefs, 0);
            result =__Pyx_PyFunction_FastCallNoKw(co, args, Py_SIZE(argdefs), globals);
            goto done;
        }
    }
    if (kwargs != NULL) {
        Py_ssize_t pos, i;
        kwtuple = PyTuple_New(2 * nk);
        if (kwtuple == NULL) {
            result = NULL;
            goto done;
        }
        k = &PyTuple_GET_ITEM(kwtuple, 0);
        pos = i = 0;
        while (PyDict_Next(kwargs, &pos, &k[i], &k[i+1])) {
            Py_INCREF(k[i]);
            Py_INCREF(k[i+1]);
            i += 2;
        }
        nk = i / 2;
    }
    else {
        kwtuple = NULL;
        k = NULL;
    }
    closure = PyFunction_GET_CLOSURE(func);
#if PY_MAJOR_VERSION >= 3
    kwdefs = PyFunction_GET_KW_DEFAULTS(func);
#endif
    if (argdefs != NULL) {
        d = &PyTuple_GET_ITEM(argdefs, 0);
        nd = Py_SIZE(argdefs);
    }
    else {
        d = NULL;
        nd = 0;
    }
#if PY_MAJOR_VERSION >= 3
    result = PyEval_EvalCodeEx((PyObject*)co, globals, (PyObject *)NULL,
                               args, (int)nargs,
                               k, (int)nk,
                               d, (int)nd, kwdefs, closure);
#else
    result = PyEval_EvalCodeEx(co, globals, (PyObject *)NULL,
                               args, (int)nargs,
                               k, (int)nk,
                               d, (int)nd, closure);
#endif
    Py_XDECREF(kwtuple);
done:
    Py_LeaveRecursiveCall();
    return result;
}
#endif
#endif

/* PyObjectCall */
#if CYTHON_COMPILING_IN_CPYTHON
static CYTHON_INLINE PyObject* __Pyx_PyObject_Call(PyObject *func, PyObject *arg, PyObject *kw) {
    PyObject *result;
    ternaryfunc call = Py_TYPE(func)->tp_call;
    if (unlikely(!call))
        return PyObject_Call(func, arg, kw);
    if (unlikely(Py_EnterRecursiveCall((char*)" while calling a Python object")))
        return NULL;
    result = (*call)(func, arg, kw);
    Py_LeaveRecursiveCall();
    if (unlikely(!result) && unlikely(!PyErr_Occurred())) {
        PyErr_SetString(
            PyExc_SystemError,
            "NULL result without error in PyObject_Call");
    }
    return result;
}
#endif

/* PyObjectCallMethO */
#if CYTHON_COMPILING_IN_CPYTHON
static CYTHON_INLINE PyObject* __Pyx_PyObject_CallMethO(PyObject *func, PyObject *arg) {
    PyObject *self, *result;
    PyCFunction cfunc;
    cfunc = PyCFunction_GET_FUNCTION(func);
    self = PyCFunction_GET_SELF(func);
    if (unlikely(Py_EnterRecursiveCall((char*)" while calling a Python object")))
        return NULL;
    result = cfunc(self, arg);
    Py_LeaveRecursiveCall();
    if (unlikely(!result) && unlikely(!PyErr_Occurred())) {
        PyErr_SetString(
            PyExc_SystemError,
            "NULL result without error in PyObject_Call");
    }
    return result;
}
#endif

/* PyObjectCallNoArg */
#if CYTHON_COMPILING_IN_CPYTHON
static CYTHON_INLINE PyObject* __Pyx_PyObject_CallNoArg(PyObject *func) {
#if CYTHON_FAST_PYCALL
    if (PyFunction_Check(func)) {
        return __Pyx_PyFunction_FastCall(func, NULL, 0);
    }
#endif
#if defined(__Pyx_CyFunction_USED) && defined(NDEBUG)
    if (likely(PyCFunction_Check(func) || __Pyx_CyFunction_Check(func)))
#else
    if (likely(PyCFunction_Check(func)))
#endif
    {
        if (likely(PyCFunction_GET_FLAGS(func) & METH_NOARGS)) {
            return __Pyx_PyObject_CallMethO(func, NULL);
        }
    }
    return __Pyx_PyObject_Call(func, __pyx_empty_tuple, NULL);
}
#endif

/* PyCFunctionFastCall */
#if CYTHON_FAST_PYCCALL
static CYTHON_INLINE PyObject * __Pyx_PyCFunction_FastCall(PyObject *func_obj, PyObject **args, Py_ssize_t nargs) {
    PyCFunctionObject *func = (PyCFunctionObject*)func_obj;
    PyCFunction meth = PyCFunction_GET_FUNCTION(func);
    PyObject *self = PyCFunction_GET_SELF(func);
    int flags = PyCFunction_GET_FLAGS(func);
    assert(PyCFunction_Check(func));
    assert(METH_FASTCALL == (flags & ~(METH_CLASS | METH_STATIC | METH_COEXIST | METH_KEYWORDS | METH_STACKLESS)));
    assert(nargs >= 0);
    assert(nargs == 0 || args != NULL);
    /* _PyCFunction_FastCallDict() must not be called with an exception set,
       because it may clear it (directly or indirectly) and so the
       caller loses its exception */
    assert(!PyErr_Occurred());
    if ((PY_VERSION_HEX < 0x030700A0) || unlikely(flags & METH_KEYWORDS)) {
        return (*((__Pyx_PyCFunctionFastWithKeywords)(void*)meth)) (self, args, nargs, NULL);
    } else {
        return (*((__Pyx_PyCFunctionFast)(void*)meth)) (self, args, nargs);
    }
}
#endif

/* PyObjectCallOneArg */
#if CYTHON_COMPILING_IN_CPYTHON
static PyObject* __Pyx__PyObject_CallOneArg(PyObject *func, PyObject *arg) {
    PyObject *result;
    PyObject *args = PyTuple_New(1);
    if (unlikely(!args)) return NULL;
    Py_INCREF(arg);
    PyTuple_SET_ITEM(args, 0, arg);
    result = __Pyx_PyObject_Call(func, args, NULL);
    Py_DECREF(args);
    return result;
}
static CYTHON_INLINE PyObject* __Pyx_PyObject_CallOneArg(PyObject *func, PyObject *arg) {
#if CYTHON_FAST_PYCALL
    if (PyFunction_Check(func)) {
        return __Pyx_PyFunction_FastCall(func, &arg, 1);
    }
#endif
    if (likely(PyCFunction_Check(func))) {
        if (likely(PyCFunction_GET_FLAGS(func) & METH_O)) {
            return __Pyx_PyObject_CallMethO(func, arg);
#if CYTHON_FAST_PYCCALL
        } else if (__Pyx_PyFastCFunction_Check(func)) {
            return __Pyx_PyCFunction_FastCall(func, &arg, 1);
#endif
        }
    }
    return __Pyx__PyObject_CallOneArg(func, arg);
}
#else
static CYTHON_INLINE PyObject* __Pyx_PyObject_CallOneArg(PyObject *func, PyObject *arg) {
    PyObject *result;
    PyObject *args = PyTuple_Pack(1, arg);
    if (unlikely(!args)) return NULL;
    result = __Pyx_PyObject_Call(func, args, NULL);
    Py_DECREF(args);
    return result;
}
#endif

/* PyErrFetchRestore */
#if CYTHON_FAST_THREAD_STATE
static CYTHON_INLINE void __Pyx_ErrRestoreInState(PyThreadState *tstate, PyObject *type, PyObject *value, PyObject *tb) {
    PyObject *tmp_type, *tmp_value, *tmp_tb;
    tmp_type = tstate->curexc_type;
    tmp_value = tstate->curexc_value;
    tmp_tb = tstate->curexc_traceback;
    tstate->curexc_type = type;
    tstate->curexc_value = value;
    tstate->curexc_traceback = tb;
    Py_XDECREF(tmp_type);
    Py_XDECREF(tmp_value);
    Py_XDECREF(tmp_tb);
}
static CYTHON_INLINE void __Pyx_ErrFetchInState(PyThreadState *tstate, PyObject **type, PyObject **value, PyObject **tb) {
    *type = tstate->curexc_type;
    *value = tstate->curexc_value;
    *tb = tstate->curexc_traceback;
    tstate->curexc_type = 0;
    tstate->curexc_value = 0;
    tstate->curexc_traceback = 0;
}
#endif

/* CLineInTraceback */
#ifndef CYTHON_CLINE_IN_TRACEBACK
static int __Pyx_CLineForTraceback(CYTHON_UNUSED PyThreadState *tstate, int c_line) {
    PyObject *use_cline;
    PyObject *ptype, *pvalue, *ptraceback;
#if CYTHON_COMPILING_IN_CPYTHON
    PyObject **cython_runtime_dict;
#endif
    if (unlikely(!__pyx_cython_runtime)) {
        return c_line;
    }
    __Pyx_ErrFetchInState(tstate, &ptype, &pvalue, &ptraceback);
#if CYTHON_COMPILING_IN_CPYTHON
    cython_runtime_dict = _PyObject_GetDictPtr(__pyx_cython_runtime);
    if (likely(cython_runtime_dict)) {
        __PYX_PY_DICT_LOOKUP_IF_MODIFIED(
            use_cline, *cython_runtime_dict,
            __Pyx_PyDict_GetItemStr(*cython_runtime_dict, __pyx_n_s_cline_in_traceback))
    } else
#endif
    {
      PyObject *use_cline_obj = __Pyx_PyObject_GetAttrStr(__pyx_cython_runtime, __pyx_n_s_cline_in_traceback);
      if (use_cline_obj) {
        use_cline = PyObject_Not(use_cline_obj) ? Py_False : Py_True;
        Py_DECREF(use_cline_obj);
      } else {
        PyErr_Clear();
        use_cline = NULL;
      }
    }
    if (!use_cline) {
        c_line = 0;
        (void) PyObject_SetAttr(__pyx_cython_runtime, __pyx_n_s_cline_in_traceback, Py_False);
    }
    else if (use_cline == Py_False || (use_cline != Py_True && PyObject_Not(use_cline) != 0)) {
        c_line = 0;
    }
    __Pyx_ErrRestoreInState(tstate, ptype, pvalue, ptraceback);
    return c_line;
}
#endif

/* CodeObjectCache */
static int __pyx_bisect_code_objects(__Pyx_CodeObjectCacheEntry* entries, int count, int code_line) {
    int start = 0, mid = 0, end = count - 1;
    if (end >= 0 && code_line > entries[end].code_line) {
        return count;
    }
    while (start < end) {
        mid = start + (end - start) / 2;
        if (code_line < entries[mid].code_line) {
            end = mid;
        } else if (code_line > entries[mid].code_line) {
             start = mid + 1;
        } else {
            return mid;
        }
    }
    if (code_line <= entries[mid].code_line) {
        return mid;
    } else {
        return mid + 1;
    }
}
static PyCodeObject *__pyx_find_code_object(int code_line) {
    PyCodeObject* code_object;
    int pos;
    if (unlikely(!code_line) || unlikely(!__pyx_code_cache.entries)) {
        return NULL;
    }
    pos = __pyx_bisect_code_objects(__pyx_code_cache.entries, __pyx_code_cache.count, code_line);
    if (unlikely(pos >= __pyx_code_cache.count) || unlikely(__pyx_code_cache.entries[pos].code_line != code_line)) {
        return NULL;
    }
    code_object = __pyx_code_cache.entries[pos].code_object;
    Py_INCREF(code_object);
    return code_object;
}
static void __pyx_insert_code_object(int code_line, PyCodeObject* code_object) {
    int pos, i;
    __Pyx_CodeObjectCacheEntry* entries = __pyx_code_cache.entries;
    if (unlikely(!code_line)) {
        return;
    }
    if (unlikely(!entries)) {
        entries = (__Pyx_CodeObjectCacheEntry*)PyMem_Malloc(64*sizeof(__Pyx_CodeObjectCacheEntry));
        if (likely(entries)) {
            __pyx_code_cache.entries = entries;
            __pyx_code_cache.max_count = 64;
            __pyx_code_cache.count = 1;
            entries[0].code_line = code_line;
            entries[0].code_object = code_object;
            Py_INCREF(code_object);
        }
        return;
    }
    pos = __pyx_bisect_code_objects(__pyx_code_cache.entries, __pyx_code_cache.count, code_line);
    if ((pos < __pyx_code_cache.count) && unlikely(__pyx_code_cache.entries[pos].code_line == code_line)) {
        PyCodeObject* tmp = entries[pos].code_object;
        entries[pos].code_object = code_object;
        Py_DECREF(tmp);
        return;
    }
    if (__pyx_code_cache.count == __pyx_code_cache.max_count) {
        int new_max = __pyx_code_cache.max_count + 64;
        entries = (__Pyx_CodeObjectCacheEntry*)PyMem_Realloc(
            __pyx_code_cache.entries, ((size_t)new_max) * sizeof(__Pyx_CodeObjectCacheEntry));
        if (unlikely(!entries)) {
            return;
        }
        __pyx_code_cache.entries = entries;
        __pyx_code_cache.max_count = new_max;
    }
    for (i=__pyx_code_cache.count; i>pos; i--) {
        entries[i] = entries[i-1];
    }
    entries[pos].code_line = code_line;
    entries[pos].code_object = code_object;
    __pyx_code_cache.count++;
    Py_INCREF(code_object);
}

/* AddTraceback */
#include "compile.h"
#include "frameobject.h"
#include "traceback.h"
#if PY_VERSION_HEX >= 0x030b00a6
  #ifndef Py_BUILD_CORE
    #define Py_BUILD_CORE 1
  #endif
  #include "internal/pycore_frame.h"
#endif
static PyCodeObject* __Pyx_CreateCodeObjectForTraceback(
            const char *funcname, int c_line,
            int py_line, const char *filename) {
    PyCodeObject *py_code = NULL;
    PyObject *py_funcname = NULL;
    #if PY_MAJOR_VERSION < 3
    PyObject *py_srcfile = NULL;
    py_srcfile = PyString_FromString(filename);
    if (!py_srcfile) goto bad;
    #endif
    if (c_line) {
        #if PY_MAJOR_VERSION < 3
        py_funcname = PyString_FromFormat( "%s (%s:%d)", funcname, __pyx_cfilenm, c_line);
        if (!py_funcname) goto bad;
        #else
        py_funcname = PyUnicode_FromFormat( "%s (%s:%d)", funcname, __pyx_cfilenm, c_line);
        if (!py_funcname) goto bad;
        funcname = PyUnicode_AsUTF8(py_funcname);
        if (!funcname) goto bad;
        #endif
    }
    else {
        #if PY_MAJOR_VERSION < 3
        py_funcname = PyString_FromString(funcname);
        if (!py_funcname) goto bad;
        #endif
    }
    #if PY_MAJOR_VERSION < 3
    py_code = __Pyx_PyCode_New(
        0,
        0,
        0,
        0,
        0,
        __pyx_empty_bytes, /*PyObject *code,*/
        __pyx_empty_tuple, /*PyObject *consts,*/
        __pyx_empty_tuple, /*PyObject *names,*/
        __pyx_empty_tuple, /*PyObject *varnames,*/
        __pyx_empty_tuple, /*PyObject *freevars,*/
        __pyx_empty_tuple, /*PyObject *cellvars,*/
        py_srcfile,   /*PyObject *filename,*/
        py_funcname,  /*PyObject *name,*/
        py_line,
        __pyx_empty_bytes  /*PyObject *lnotab*/
    );
    Py_DECREF(py_srcfile);
    #else
    py_code = PyCode_NewEmpty(filename, funcname, py_line);
    #endif
    Py_XDECREF(py_funcname);  // XDECREF since it's only set on Py3 if cline
    return py_code;
bad:
    Py_XDECREF(py_funcname);
    #if PY_MAJOR_VERSION < 3
    Py_XDECREF(py_srcfile);
    #endif
    return NULL;
}
static void __Pyx_AddTraceback(const char *funcname, int c_line,
                               int py_line, const char *filename) {
    PyCodeObject *py_code = 0;
    PyFrameObject *py_frame = 0;
    PyThreadState *tstate = __Pyx_PyThreadState_Current;
    PyObject *ptype, *pvalue, *ptraceback;
    if (c_line) {
        c_line = __Pyx_CLineForTraceback(tstate, c_line);
    }
    py_code = __pyx_find_code_object(c_line ? -c_line : py_line);
    if (!py_code) {
        __Pyx_ErrFetchInState(tstate, &ptype, &pvalue, &ptraceback);
        py_code = __Pyx_CreateCodeObjectForTraceback(
            funcname, c_line, py_line, filename);
        if (!py_code) {
            /* If the code object creation fails, then we should clear the
               fetched exception references and propagate the new exception */
            Py_XDECREF(ptype);
            Py_XDECREF(pvalue);
            Py_XDECREF(ptraceback);
            goto bad;
        }
        __Pyx_ErrRestoreInState(tstate, ptype, pvalue, ptraceback);
        __pyx_insert_code_object(c_line ? -c_line : py_line, py_code);
    }
    py_frame = PyFrame_New(
        tstate,            /*PyThreadState *tstate,*/
        py_code,           /*PyCodeObject *code,*/
        __pyx_d,    /*PyObject *globals,*/
        0                  /*PyObject *locals*/
    );
    if (!py_frame) goto bad;
    __Pyx_PyFrame_SetLineNumber(py_frame, py_line);
    PyTraceBack_Here(py_frame);
bad:
    Py_XDECREF(py_code);
    Py_XDECREF(py_frame);
}

/* CIntToPy */
static CYTHON_INLINE PyObject* __Pyx_PyInt_From_enum____pyx_t_5spacy_7symbols_symbol_t(enum __pyx_t_5spacy_7symbols_symbol_t value) {
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wconversion"
#endif
    const enum __pyx_t_5spacy_7symbols_symbol_t neg_one = (enum __pyx_t_5spacy_7symbols_symbol_t) -1, const_zero = (enum __pyx_t_5spacy_7symbols_symbol_t) 0;
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic pop
#endif
    const int is_unsigned = neg_one > const_zero;
    if (is_unsigned) {
        if (sizeof(enum __pyx_t_5spacy_7symbols_symbol_t) < sizeof(long)) {
            return PyInt_FromLong((long) value);
        } else if (sizeof(enum __pyx_t_5spacy_7symbols_symbol_t) <= sizeof(unsigned long)) {
            return PyLong_FromUnsignedLong((unsigned long) value);
#ifdef HAVE_LONG_LONG
        } else if (sizeof(enum __pyx_t_5spacy_7symbols_symbol_t) <= sizeof(unsigned PY_LONG_LONG)) {
            return PyLong_FromUnsignedLongLong((unsigned PY_LONG_LONG) value);
#endif
        }
    } else {
        if (sizeof(enum __pyx_t_5spacy_7symbols_symbol_t) <= sizeof(long)) {
            return PyInt_FromLong((long) value);
#ifdef HAVE_LONG_LONG
        } else if (sizeof(enum __pyx_t_5spacy_7symbols_symbol_t) <= sizeof(PY_LONG_LONG)) {
            return PyLong_FromLongLong((PY_LONG_LONG) value);
#endif
        }
    }
    {
        int one = 1; int little = (int)*(unsigned char *)&one;
        unsigned char *bytes = (unsigned char *)&value;
        return _PyLong_FromByteArray(bytes, sizeof(enum __pyx_t_5spacy_7symbols_symbol_t),
                                     little, !is_unsigned);
    }
}

/* CIntToPy */
static CYTHON_INLINE PyObject* __Pyx_PyInt_From_long(long value) {
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wconversion"
#endif
    const long neg_one = (long) -1, const_zero = (long) 0;
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic pop
#endif
    const int is_unsigned = neg_one > const_zero;
    if (is_unsigned) {
        if (sizeof(long) < sizeof(long)) {
            return PyInt_FromLong((long) value);
        } else if (sizeof(long) <= sizeof(unsigned long)) {
            return PyLong_FromUnsignedLong((unsigned long) value);
#ifdef HAVE_LONG_LONG
        } else if (sizeof(long) <= sizeof(unsigned PY_LONG_LONG)) {
            return PyLong_FromUnsignedLongLong((unsigned PY_LONG_LONG) value);
#endif
        }
    } else {
        if (sizeof(long) <= sizeof(long)) {
            return PyInt_FromLong((long) value);
#ifdef HAVE_LONG_LONG
        } else if (sizeof(long) <= sizeof(PY_LONG_LONG)) {
            return PyLong_FromLongLong((PY_LONG_LONG) value);
#endif
        }
    }
    {
        int one = 1; int little = (int)*(unsigned char *)&one;
        unsigned char *bytes = (unsigned char *)&value;
        return _PyLong_FromByteArray(bytes, sizeof(long),
                                     little, !is_unsigned);
    }
}

/* GetAttr */
static CYTHON_INLINE PyObject *__Pyx_GetAttr(PyObject *o, PyObject *n) {
#if CYTHON_USE_TYPE_SLOTS
#if PY_MAJOR_VERSION >= 3
    if (likely(PyUnicode_Check(n)))
#else
    if (likely(PyString_Check(n)))
#endif
        return __Pyx_PyObject_GetAttrStr(o, n);
#endif
    return PyObject_GetAttr(o, n);
}

/* Globals */
static PyObject* __Pyx_Globals(void) {
    Py_ssize_t i;
    PyObject *names;
    PyObject *globals = __pyx_d;
    Py_INCREF(globals);
    names = PyObject_Dir(__pyx_m);
    if (!names)
        goto bad;
    for (i = PyList_GET_SIZE(names)-1; i >= 0; i--) {
#if CYTHON_COMPILING_IN_PYPY
        PyObject* name = PySequence_ITEM(names, i);
        if (!name)
            goto bad;
#else
        PyObject* name = PyList_GET_ITEM(names, i);
#endif
        if (!PyDict_Contains(globals, name)) {
            PyObject* value = __Pyx_GetAttr(__pyx_m, name);
            if (!value) {
#if CYTHON_COMPILING_IN_PYPY
                Py_DECREF(name);
#endif
                goto bad;
            }
            if (PyDict_SetItem(globals, name, value) < 0) {
#if CYTHON_COMPILING_IN_PYPY
                Py_DECREF(name);
#endif
                Py_DECREF(value);
                goto bad;
            }
        }
#if CYTHON_COMPILING_IN_PYPY
        Py_DECREF(name);
#endif
    }
    Py_DECREF(names);
    return globals;
bad:
    Py_XDECREF(names);
    Py_XDECREF(globals);
    return NULL;
}

/* CIntFromPyVerify */
#define __PYX_VERIFY_RETURN_INT(target_type, func_type, func_value)\
    __PYX__VERIFY_RETURN_INT(target_type, func_type, func_value, 0)
#define __PYX_VERIFY_RETURN_INT_EXC(target_type, func_type, func_value)\
    __PYX__VERIFY_RETURN_INT(target_type, func_type, func_value, 1)
#define __PYX__VERIFY_RETURN_INT(target_type, func_type, func_value, exc)\
    {\
        func_type value = func_value;\
        if (sizeof(target_type) < sizeof(func_type)) {\
            if (unlikely(value != (func_type) (target_type) value)) {\
                func_type zero = 0;\
                if (exc && unlikely(value == (func_type)-1 && PyErr_Occurred()))\
                    return (target_type) -1;\
                if (is_unsigned && unlikely(value < zero))\
                    goto raise_neg_overflow;\
                else\
                    goto raise_overflow;\
            }\
        }\
        return (target_type) value;\
    }

/* CIntFromPy */
static CYTHON_INLINE long __Pyx_PyInt_As_long(PyObject *x) {
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wconversion"
#endif
    const long neg_one = (long) -1, const_zero = (long) 0;
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic pop
#endif
    const int is_unsigned = neg_one > const_zero;
#if PY_MAJOR_VERSION < 3
    if (likely(PyInt_Check(x))) {
        if (sizeof(long) < sizeof(long)) {
            __PYX_VERIFY_RETURN_INT(long, long, PyInt_AS_LONG(x))
        } else {
            long val = PyInt_AS_LONG(x);
            if (is_unsigned && unlikely(val < 0)) {
                goto raise_neg_overflow;
            }
            return (long) val;
        }
    } else
#endif
    if (likely(PyLong_Check(x))) {
        if (is_unsigned) {
#if CYTHON_USE_PYLONG_INTERNALS
            const digit* digits = ((PyLongObject*)x)->ob_digit;
            switch (Py_SIZE(x)) {
                case  0: return (long) 0;
                case  1: __PYX_VERIFY_RETURN_INT(long, digit, digits[0])
                case 2:
                    if (8 * sizeof(long) > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) >= 2 * PyLong_SHIFT) {
                            return (long) (((((long)digits[1]) << PyLong_SHIFT) | (long)digits[0]));
                        }
                    }
                    break;
                case 3:
                    if (8 * sizeof(long) > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) >= 3 * PyLong_SHIFT) {
                            return (long) (((((((long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0]));
                        }
                    }
                    break;
                case 4:
                    if (8 * sizeof(long) > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) >= 4 * PyLong_SHIFT) {
                            return (long) (((((((((long)digits[3]) << PyLong_SHIFT) | (long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0]));
                        }
                    }
                    break;
            }
#endif
#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX < 0x030C00A7
            if (unlikely(Py_SIZE(x) < 0)) {
                goto raise_neg_overflow;
            }
#else
            {
                int result = PyObject_RichCompareBool(x, Py_False, Py_LT);
                if (unlikely(result < 0))
                    return (long) -1;
                if (unlikely(result == 1))
                    goto raise_neg_overflow;
            }
#endif
            if (sizeof(long) <= sizeof(unsigned long)) {
                __PYX_VERIFY_RETURN_INT_EXC(long, unsigned long, PyLong_AsUnsignedLong(x))
#ifdef HAVE_LONG_LONG
            } else if (sizeof(long) <= sizeof(unsigned PY_LONG_LONG)) {
                __PYX_VERIFY_RETURN_INT_EXC(long, unsigned PY_LONG_LONG, PyLong_AsUnsignedLongLong(x))
#endif
            }
        } else {
#if CYTHON_USE_PYLONG_INTERNALS
            const digit* digits = ((PyLongObject*)x)->ob_digit;
            switch (Py_SIZE(x)) {
                case  0: return (long) 0;
                case -1: __PYX_VERIFY_RETURN_INT(long, sdigit, (sdigit) (-(sdigit)digits[0]))
                case  1: __PYX_VERIFY_RETURN_INT(long,  digit, +digits[0])
                case -2:
                    if (8 * sizeof(long) - 1 > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, long, -(long) (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) - 1 > 2 * PyLong_SHIFT) {
                            return (long) (((long)-1)*(((((long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
                case 2:
                    if (8 * sizeof(long) > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) - 1 > 2 * PyLong_SHIFT) {
                            return (long) ((((((long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
                case -3:
                    if (8 * sizeof(long) - 1 > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, long, -(long) (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) - 1 > 3 * PyLong_SHIFT) {
                            return (long) (((long)-1)*(((((((long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
                case 3:
                    if (8 * sizeof(long) > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) - 1 > 3 * PyLong_SHIFT) {
                            return (long) ((((((((long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
                case -4:
                    if (8 * sizeof(long) - 1 > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, long, -(long) (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) - 1 > 4 * PyLong_SHIFT) {
                            return (long) (((long)-1)*(((((((((long)digits[3]) << PyLong_SHIFT) | (long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
                case 4:
                    if (8 * sizeof(long) > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) - 1 > 4 * PyLong_SHIFT) {
                            return (long) ((((((((((long)digits[3]) << PyLong_SHIFT) | (long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
            }
#endif
            if (sizeof(long) <= sizeof(long)) {
                __PYX_VERIFY_RETURN_INT_EXC(long, long, PyLong_AsLong(x))
#ifdef HAVE_LONG_LONG
            } else if (sizeof(long) <= sizeof(PY_LONG_LONG)) {
                __PYX_VERIFY_RETURN_INT_EXC(long, PY_LONG_LONG, PyLong_AsLongLong(x))
#endif
            }
        }
        {
#if CYTHON_COMPILING_IN_PYPY && !defined(_PyLong_AsByteArray)
            PyErr_SetString(PyExc_RuntimeError,
                            "_PyLong_AsByteArray() not available in PyPy, cannot convert large numbers");
#else
            long val;
            PyObject *v = __Pyx_PyNumber_IntOrLong(x);
 #if PY_MAJOR_VERSION < 3
            if (likely(v) && !PyLong_Check(v)) {
                PyObject *tmp = v;
                v = PyNumber_Long(tmp);
                Py_DECREF(tmp);
            }
 #endif
            if (likely(v)) {
                int one = 1; int is_little = (int)*(unsigned char *)&one;
                unsigned char *bytes = (unsigned char *)&val;
                int ret = _PyLong_AsByteArray((PyLongObject *)v,
                                              bytes, sizeof(val),
                                              is_little, !is_unsigned);
                Py_DECREF(v);
                if (likely(!ret))
                    return val;
            }
#endif
            return (long) -1;
        }
    } else {
        long val;
        PyObject *tmp = __Pyx_PyNumber_IntOrLong(x);
        if (!tmp) return (long) -1;
        val = __Pyx_PyInt_As_long(tmp);
        Py_DECREF(tmp);
        return val;
    }
raise_overflow:
    PyErr_SetString(PyExc_OverflowError,
        "value too large to convert to long");
    return (long) -1;
raise_neg_overflow:
    PyErr_SetString(PyExc_OverflowError,
        "can't convert negative value to long");
    return (long) -1;
}

/* CIntFromPy */
static CYTHON_INLINE int __Pyx_PyInt_As_int(PyObject *x) {
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wconversion"
#endif
    const int neg_one = (int) -1, const_zero = (int) 0;
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic pop
#endif
    const int is_unsigned = neg_one > const_zero;
#if PY_MAJOR_VERSION < 3
    if (likely(PyInt_Check(x))) {
        if (sizeof(int) < sizeof(long)) {
            __PYX_VERIFY_RETURN_INT(int, long, PyInt_AS_LONG(x))
        } else {
            long val = PyInt_AS_LONG(x);
            if (is_unsigned && unlikely(val < 0)) {
                goto raise_neg_overflow;
            }
            return (int) val;
        }
    } else
#endif
    if (likely(PyLong_Check(x))) {
        if (is_unsigned) {
#if CYTHON_USE_PYLONG_INTERNALS
            const digit* digits = ((PyLongObject*)x)->ob_digit;
            switch (Py_SIZE(x)) {
                case  0: return (int) 0;
                case  1: __PYX_VERIFY_RETURN_INT(int, digit, digits[0])
                case 2:
                    if (8 * sizeof(int) > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) >= 2 * PyLong_SHIFT) {
                            return (int) (((((int)digits[1]) << PyLong_SHIFT) | (int)digits[0]));
                        }
                    }
                    break;
                case 3:
                    if (8 * sizeof(int) > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) >= 3 * PyLong_SHIFT) {
                            return (int) (((((((int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0]));
                        }
                    }
                    break;
                case 4:
                    if (8 * sizeof(int) > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) >= 4 * PyLong_SHIFT) {
                            return (int) (((((((((int)digits[3]) << PyLong_SHIFT) | (int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0]));
                        }
                    }
                    break;
            }
#endif
#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX < 0x030C00A7
            if (unlikely(Py_SIZE(x) < 0)) {
                goto raise_neg_overflow;
            }
#else
            {
                int result = PyObject_RichCompareBool(x, Py_False, Py_LT);
                if (unlikely(result < 0))
                    return (int) -1;
                if (unlikely(result == 1))
                    goto raise_neg_overflow;
            }
#endif
            if (sizeof(int) <= sizeof(unsigned long)) {
                __PYX_VERIFY_RETURN_INT_EXC(int, unsigned long, PyLong_AsUnsignedLong(x))
#ifdef HAVE_LONG_LONG
            } else if (sizeof(int) <= sizeof(unsigned PY_LONG_LONG)) {
                __PYX_VERIFY_RETURN_INT_EXC(int, unsigned PY_LONG_LONG, PyLong_AsUnsignedLongLong(x))
#endif
            }
        } else {
#if CYTHON_USE_PYLONG_INTERNALS
            const digit* digits = ((PyLongObject*)x)->ob_digit;
            switch (Py_SIZE(x)) {
                case  0: return (int) 0;
                case -1: __PYX_VERIFY_RETURN_INT(int, sdigit, (sdigit) (-(sdigit)digits[0]))
                case  1: __PYX_VERIFY_RETURN_INT(int,  digit, +digits[0])
                case -2:
                    if (8 * sizeof(int) - 1 > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, long, -(long) (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) - 1 > 2 * PyLong_SHIFT) {
                            return (int) (((int)-1)*(((((int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
                case 2:
                    if (8 * sizeof(int) > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) - 1 > 2 * PyLong_SHIFT) {
                            return (int) ((((((int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
                case -3:
                    if (8 * sizeof(int) - 1 > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, long, -(long) (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) - 1 > 3 * PyLong_SHIFT) {
                            return (int) (((int)-1)*(((((((int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
                case 3:
                    if (8 * sizeof(int) > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) - 1 > 3 * PyLong_SHIFT) {
                            return (int) ((((((((int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
                case -4:
                    if (8 * sizeof(int) - 1 > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, long, -(long) (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) - 1 > 4 * PyLong_SHIFT) {
                            return (int) (((int)-1)*(((((((((int)digits[3]) << PyLong_SHIFT) | (int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
                case 4:
                    if (8 * sizeof(int) > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) - 1 > 4 * PyLong_SHIFT) {
                            return (int) ((((((((((int)digits[3]) << PyLong_SHIFT) | (int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
            }
#endif
            if (sizeof(int) <= sizeof(long)) {
                __PYX_VERIFY_RETURN_INT_EXC(int, long, PyLong_AsLong(x))
#ifdef HAVE_LONG_LONG
            } else if (sizeof(int) <= sizeof(PY_LONG_LONG)) {
                __PYX_VERIFY_RETURN_INT_EXC(int, PY_LONG_LONG, PyLong_AsLongLong(x))
#endif
            }
        }
        {
#if CYTHON_COMPILING_IN_PYPY && !defined(_PyLong_AsByteArray)
            PyErr_SetString(PyExc_RuntimeError,
                            "_PyLong_AsByteArray() not available in PyPy, cannot convert large numbers");
#else
            int val;
            PyObject *v = __Pyx_PyNumber_IntOrLong(x);
 #if PY_MAJOR_VERSION < 3
            if (likely(v) && !PyLong_Check(v)) {
                PyObject *tmp = v;
                v = PyNumber_Long(tmp);
                Py_DECREF(tmp);
            }
 #endif
            if (likely(v)) {
                int one = 1; int is_little = (int)*(unsigned char *)&one;
                unsigned char *bytes = (unsigned char *)&val;
                int ret = _PyLong_AsByteArray((PyLongObject *)v,
                                              bytes, sizeof(val),
                                              is_little, !is_unsigned);
                Py_DECREF(v);
                if (likely(!ret))
                    return val;
            }
#endif
            return (int) -1;
        }
    } else {
        int val;
        PyObject *tmp = __Pyx_PyNumber_IntOrLong(x);
        if (!tmp) return (int) -1;
        val = __Pyx_PyInt_As_int(tmp);
        Py_DECREF(tmp);
        return val;
    }
raise_overflow:
    PyErr_SetString(PyExc_OverflowError,
        "value too large to convert to int");
    return (int) -1;
raise_neg_overflow:
    PyErr_SetString(PyExc_OverflowError,
        "can't convert negative value to int");
    return (int) -1;
}

/* FastTypeChecks */
#if CYTHON_COMPILING_IN_CPYTHON
static int __Pyx_InBases(PyTypeObject *a, PyTypeObject *b) {
    while (a) {
        a = a->tp_base;
        if (a == b)
            return 1;
    }
    return b == &PyBaseObject_Type;
}
static CYTHON_INLINE int __Pyx_IsSubtype(PyTypeObject *a, PyTypeObject *b) {
    PyObject *mro;
    if (a == b) return 1;
    mro = a->tp_mro;
    if (likely(mro)) {
        Py_ssize_t i, n;
        n = PyTuple_GET_SIZE(mro);
        for (i = 0; i < n; i++) {
            if (PyTuple_GET_ITEM(mro, i) == (PyObject *)b)
                return 1;
        }
        return 0;
    }
    return __Pyx_InBases(a, b);
}
#if PY_MAJOR_VERSION == 2
static int __Pyx_inner_PyErr_GivenExceptionMatches2(PyObject *err, PyObject* exc_type1, PyObject* exc_type2) {
    PyObject *exception, *value, *tb;
    int res;
    __Pyx_PyThreadState_declare
    __Pyx_PyThreadState_assign
    __Pyx_ErrFetch(&exception, &value, &tb);
    res = exc_type1 ? PyObject_IsSubclass(err, exc_type1) : 0;
    if (unlikely(res == -1)) {
        PyErr_WriteUnraisable(err);
        res = 0;
    }
    if (!res) {
        res = PyObject_IsSubclass(err, exc_type2);
        if (unlikely(res == -1)) {
            PyErr_WriteUnraisable(err);
            res = 0;
        }
    }
    __Pyx_ErrRestore(exception, value, tb);
    return res;
}
#else
static CYTHON_INLINE int __Pyx_inner_PyErr_GivenExceptionMatches2(PyObject *err, PyObject* exc_type1, PyObject *exc_type2) {
    int res = exc_type1 ? __Pyx_IsSubtype((PyTypeObject*)err, (PyTypeObject*)exc_type1) : 0;
    if (!res) {
        res = __Pyx_IsSubtype((PyTypeObject*)err, (PyTypeObject*)exc_type2);
    }
    return res;
}
#endif
static int __Pyx_PyErr_GivenExceptionMatchesTuple(PyObject *exc_type, PyObject *tuple) {
    Py_ssize_t i, n;
    assert(PyExceptionClass_Check(exc_type));
    n = PyTuple_GET_SIZE(tuple);
#if PY_MAJOR_VERSION >= 3
    for (i=0; i<n; i++) {
        if (exc_type == PyTuple_GET_ITEM(tuple, i)) return 1;
    }
#endif
    for (i=0; i<n; i++) {
        PyObject *t = PyTuple_GET_ITEM(tuple, i);
        #if PY_MAJOR_VERSION < 3
        if (likely(exc_type == t)) return 1;
        #endif
        if (likely(PyExceptionClass_Check(t))) {
            if (__Pyx_inner_PyErr_GivenExceptionMatches2(exc_type, NULL, t)) return 1;
        } else {
        }
    }
    return 0;
}
static CYTHON_INLINE int __Pyx_PyErr_GivenExceptionMatches(PyObject *err, PyObject* exc_type) {
    if (likely(err == exc_type)) return 1;
    if (likely(PyExceptionClass_Check(err))) {
        if (likely(PyExceptionClass_Check(exc_type))) {
            return __Pyx_inner_PyErr_GivenExceptionMatches2(err, NULL, exc_type);
        } else if (likely(PyTuple_Check(exc_type))) {
            return __Pyx_PyErr_GivenExceptionMatchesTuple(err, exc_type);
        } else {
        }
    }
    return PyErr_GivenExceptionMatches(err, exc_type);
}
static CYTHON_INLINE int __Pyx_PyErr_GivenExceptionMatches2(PyObject *err, PyObject *exc_type1, PyObject *exc_type2) {
    assert(PyExceptionClass_Check(exc_type1));
    assert(PyExceptionClass_Check(exc_type2));
    if (likely(err == exc_type1 || err == exc_type2)) return 1;
    if (likely(PyExceptionClass_Check(err))) {
        return __Pyx_inner_PyErr_GivenExceptionMatches2(err, exc_type1, exc_type2);
    }
    return (PyErr_GivenExceptionMatches(err, exc_type1) || PyErr_GivenExceptionMatches(err, exc_type2));
}
#endif

/* CheckBinaryVersion */
static int __Pyx_check_binary_version(void) {
    char ctversion[5];
    int same=1, i, found_dot;
    const char* rt_from_call = Py_GetVersion();
    PyOS_snprintf(ctversion, 5, "%d.%d", PY_MAJOR_VERSION, PY_MINOR_VERSION);
    found_dot = 0;
    for (i = 0; i < 4; i++) {
        if (!ctversion[i]) {
            same = (rt_from_call[i] < '0' || rt_from_call[i] > '9');
            break;
        }
        if (rt_from_call[i] != ctversion[i]) {
            same = 0;
            break;
        }
    }
    if (!same) {
        char rtversion[5] = {'\0'};
        char message[200];
        for (i=0; i<4; ++i) {
            if (rt_from_call[i] == '.') {
                if (found_dot) break;
                found_dot = 1;
            } else if (rt_from_call[i] < '0' || rt_from_call[i] > '9') {
                break;
            }
            rtversion[i] = rt_from_call[i];
        }
        PyOS_snprintf(message, sizeof(message),
                      "compiletime version %s of module '%.100s' "
                      "does not match runtime version %s",
                      ctversion, __Pyx_MODULE_NAME, rtversion);
        return PyErr_WarnEx(NULL, message, 1);
    }
    return 0;
}

/* InitStrings */
static int __Pyx_InitStrings(__Pyx_StringTabEntry *t) {
    while (t->p) {
        #if PY_MAJOR_VERSION < 3
        if (t->is_unicode) {
            *t->p = PyUnicode_DecodeUTF8(t->s, t->n - 1, NULL);
        } else if (t->intern) {
            *t->p = PyString_InternFromString(t->s);
        } else {
            *t->p = PyString_FromStringAndSize(t->s, t->n - 1);
        }
        #else
        if (t->is_unicode | t->is_str) {
            if (t->intern) {
                *t->p = PyUnicode_InternFromString(t->s);
            } else if (t->encoding) {
                *t->p = PyUnicode_Decode(t->s, t->n - 1, t->encoding, NULL);
            } else {
                *t->p = PyUnicode_FromStringAndSize(t->s, t->n - 1);
            }
        } else {
            *t->p = PyBytes_FromStringAndSize(t->s, t->n - 1);
        }
        #endif
        if (!*t->p)
            return -1;
        if (PyObject_Hash(*t->p) == -1)
            return -1;
        ++t;
    }
    return 0;
}

static CYTHON_INLINE PyObject* __Pyx_PyUnicode_FromString(const char* c_str) {
    return __Pyx_PyUnicode_FromStringAndSize(c_str, (Py_ssize_t)strlen(c_str));
}
static CYTHON_INLINE const char* __Pyx_PyObject_AsString(PyObject* o) {
    Py_ssize_t ignore;
    return __Pyx_PyObject_AsStringAndSize(o, &ignore);
}
#if __PYX_DEFAULT_STRING_ENCODING_IS_ASCII || __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT
#if !CYTHON_PEP393_ENABLED
static const char* __Pyx_PyUnicode_AsStringAndSize(PyObject* o, Py_ssize_t *length) {
    char* defenc_c;
    PyObject* defenc = _PyUnicode_AsDefaultEncodedString(o, NULL);
    if (!defenc) return NULL;
    defenc_c = PyBytes_AS_STRING(defenc);
#if __PYX_DEFAULT_STRING_ENCODING_IS_ASCII
    {
        char* end = defenc_c + PyBytes_GET_SIZE(defenc);
        char* c;
        for (c = defenc_c; c < end; c++) {
            if ((unsigned char) (*c) >= 128) {
                PyUnicode_AsASCIIString(o);
                return NULL;
            }
        }
    }
#endif
    *length = PyBytes_GET_SIZE(defenc);
    return defenc_c;
}
#else
static CYTHON_INLINE const char* __Pyx_PyUnicode_AsStringAndSize(PyObject* o, Py_ssize_t *length) {
    if (unlikely(__Pyx_PyUnicode_READY(o) == -1)) return NULL;
#if __PYX_DEFAULT_STRING_ENCODING_IS_ASCII
    if (likely(PyUnicode_IS_ASCII(o))) {
        *length = PyUnicode_GET_LENGTH(o);
        return PyUnicode_AsUTF8(o);
    } else {
        PyUnicode_AsASCIIString(o);
        return NULL;
    }
#else
    return PyUnicode_AsUTF8AndSize(o, length);
#endif
}
#endif
#endif
static CYTHON_INLINE const char* __Pyx_PyObject_AsStringAndSize(PyObject* o, Py_ssize_t *length) {
#if __PYX_DEFAULT_STRING_ENCODING_IS_ASCII || __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT
    if (
#if PY_MAJOR_VERSION < 3 && __PYX_DEFAULT_STRING_ENCODING_IS_ASCII
            __Pyx_sys_getdefaultencoding_not_ascii &&
#endif
            PyUnicode_Check(o)) {
        return __Pyx_PyUnicode_AsStringAndSize(o, length);
    } else
#endif
#if (!CYTHON_COMPILING_IN_PYPY) || (defined(PyByteArray_AS_STRING) && defined(PyByteArray_GET_SIZE))
    if (PyByteArray_Check(o)) {
        *length = PyByteArray_GET_SIZE(o);
        return PyByteArray_AS_STRING(o);
    } else
#endif
    {
        char* result;
        int r = PyBytes_AsStringAndSize(o, &result, length);
        if (unlikely(r < 0)) {
            return NULL;
        } else {
            return result;
        }
    }
}
static CYTHON_INLINE int __Pyx_PyObject_IsTrue(PyObject* x) {
   int is_true = x == Py_True;
   if (is_true | (x == Py_False) | (x == Py_None)) return is_true;
   else return PyObject_IsTrue(x);
}
static CYTHON_INLINE int __Pyx_PyObject_IsTrueAndDecref(PyObject* x) {
    int retval;
    if (unlikely(!x)) return -1;
    retval = __Pyx_PyObject_IsTrue(x);
    Py_DECREF(x);
    return retval;
}
static PyObject* __Pyx_PyNumber_IntOrLongWrongResultType(PyObject* result, const char* type_name) {
#if PY_MAJOR_VERSION >= 3
    if (PyLong_Check(result)) {
        if (PyErr_WarnFormat(PyExc_DeprecationWarning, 1,
                "__int__ returned non-int (type %.200s).  "
                "The ability to return an instance of a strict subclass of int "
                "is deprecated, and may be removed in a future version of Python.",
                Py_TYPE(result)->tp_name)) {
            Py_DECREF(result);
            return NULL;
        }
        return result;
    }
#endif
    PyErr_Format(PyExc_TypeError,
                 "__%.4s__ returned non-%.4s (type %.200s)",
                 type_name, type_name, Py_TYPE(result)->tp_name);
    Py_DECREF(result);
    return NULL;
}
static CYTHON_INLINE PyObject* __Pyx_PyNumber_IntOrLong(PyObject* x) {
#if CYTHON_USE_TYPE_SLOTS
  PyNumberMethods *m;
#endif
  const char *name = NULL;
  PyObject *res = NULL;
#if PY_MAJOR_VERSION < 3
  if (likely(PyInt_Check(x) || PyLong_Check(x)))
#else
  if (likely(PyLong_Check(x)))
#endif
    return __Pyx_NewRef(x);
#if CYTHON_USE_TYPE_SLOTS
  m = Py_TYPE(x)->tp_as_number;
  #if PY_MAJOR_VERSION < 3
  if (m && m->nb_int) {
    name = "int";
    res = m->nb_int(x);
  }
  else if (m && m->nb_long) {
    name = "long";
    res = m->nb_long(x);
  }
  #else
  if (likely(m && m->nb_int)) {
    name = "int";
    res = m->nb_int(x);
  }
  #endif
#else
  if (!PyBytes_CheckExact(x) && !PyUnicode_CheckExact(x)) {
    res = PyNumber_Int(x);
  }
#endif
  if (likely(res)) {
#if PY_MAJOR_VERSION < 3
    if (unlikely(!PyInt_Check(res) && !PyLong_Check(res))) {
#else
    if (unlikely(!PyLong_CheckExact(res))) {
#endif
        return __Pyx_PyNumber_IntOrLongWrongResultType(res, name);
    }
  }
  else if (!PyErr_Occurred()) {
    PyErr_SetString(PyExc_TypeError,
                    "an integer is required");
  }
  return res;
}
static CYTHON_INLINE Py_ssize_t __Pyx_PyIndex_AsSsize_t(PyObject* b) {
  Py_ssize_t ival;
  PyObject *x;
#if PY_MAJOR_VERSION < 3
  if (likely(PyInt_CheckExact(b))) {
    if (sizeof(Py_ssize_t) >= sizeof(long))
        return PyInt_AS_LONG(b);
    else
        return PyInt_AsSsize_t(b);
  }
#endif
  if (likely(PyLong_CheckExact(b))) {
    #if CYTHON_USE_PYLONG_INTERNALS
    const digit* digits = ((PyLongObject*)b)->ob_digit;
    const Py_ssize_t size = Py_SIZE(b);
    if (likely(__Pyx_sst_abs(size) <= 1)) {
        ival = likely(size) ? digits[0] : 0;
        if (size == -1) ival = -ival;
        return ival;
    } else {
      switch (size) {
         case 2:
           if (8 * sizeof(Py_ssize_t) > 2 * PyLong_SHIFT) {
             return (Py_ssize_t) (((((size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
         case -2:
           if (8 * sizeof(Py_ssize_t) > 2 * PyLong_SHIFT) {
             return -(Py_ssize_t) (((((size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
         case 3:
           if (8 * sizeof(Py_ssize_t) > 3 * PyLong_SHIFT) {
             return (Py_ssize_t) (((((((size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
         case -3:
           if (8 * sizeof(Py_ssize_t) > 3 * PyLong_SHIFT) {
             return -(Py_ssize_t) (((((((size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
         case 4:
           if (8 * sizeof(Py_ssize_t) > 4 * PyLong_SHIFT) {
             return (Py_ssize_t) (((((((((size_t)digits[3]) << PyLong_SHIFT) | (size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
         case -4:
           if (8 * sizeof(Py_ssize_t) > 4 * PyLong_SHIFT) {
             return -(Py_ssize_t) (((((((((size_t)digits[3]) << PyLong_SHIFT) | (size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
      }
    }
    #endif
    return PyLong_AsSsize_t(b);
  }
  x = PyNumber_Index(b);
  if (!x) return -1;
  ival = PyInt_AsSsize_t(x);
  Py_DECREF(x);
  return ival;
}
static CYTHON_INLINE Py_hash_t __Pyx_PyIndex_AsHash_t(PyObject* o) {
  if (sizeof(Py_hash_t) == sizeof(Py_ssize_t)) {
    return (Py_hash_t) __Pyx_PyIndex_AsSsize_t(o);
#if PY_MAJOR_VERSION < 3
  } else if (likely(PyInt_CheckExact(o))) {
    return PyInt_AS_LONG(o);
#endif
  } else {
    Py_ssize_t ival;
    PyObject *x;
    x = PyNumber_Index(o);
    if (!x) return -1;
    ival = PyInt_AsLong(x);
    Py_DECREF(x);
    return ival;
  }
}
static CYTHON_INLINE PyObject * __Pyx_PyBool_FromLong(long b) {
  return b ? __Pyx_NewRef(Py_True) : __Pyx_NewRef(Py_False);
}
static CYTHON_INLINE PyObject * __Pyx_PyInt_FromSize_t(size_t ival) {
    return PyInt_FromSize_t(ival);
}


#endif /* Py_PYTHON_H */
