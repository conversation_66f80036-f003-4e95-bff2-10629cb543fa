"""
Example sentences to test spaCy and its language models.
>>> from spacy.lang.tt.examples import sentences
>>> docs = nlp.pipe(sentences)
"""

sentences = [
    "Apple Бөекбритания стартабын $1 миллиард өчен сатып алыун исәпли.",
    "Автоном автомобильләр иминият җаваплылыкны җитештерүчеләргә күчерә.",
    "Сан-Франциско тротуар буенча йөри торган робот-курьерларны тыю мөмкинлеген карый.",
    "Лондон - Бөекбританиядә урнашкан зур шәһәр.",
    "Син кайда?",
    "Францияда кем президент?",
    "Америка Кушма Штатларының башкаласы нинди шәһәр?",
    "Барак Обама кайчан туган?",
]
