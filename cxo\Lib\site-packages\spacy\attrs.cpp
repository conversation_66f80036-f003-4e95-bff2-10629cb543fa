/* Generated by Cython 0.29.35 */

/* BEGIN: Cython Metadata
{
    "distutils": {
        "extra_compile_args": [
            "-std=c++11"
        ],
        "include_dirs": [
            "C:\\hostedtoolcache\\windows\\Python\\3.11.4\\x64\\Lib\\site-packages\\numpy\\core\\include",
            "C:\\hostedtoolcache\\windows\\Python\\3.11.4\\x64\\include"
        ],
        "language": "c++",
        "name": "spacy.attrs",
        "sources": [
            "spacy/attrs.pyx"
        ]
    },
    "module_name": "spacy.attrs"
}
END: Cython Metadata */

#ifndef PY_SSIZE_T_CLEAN
#define PY_SSIZE_T_CLEAN
#endif /* PY_SSIZE_T_CLEAN */
#include "Python.h"
#ifndef Py_PYTHON_H
    #error Python headers needed to compile C extensions, please install development version of Python.
#elif PY_VERSION_HEX < 0x02060000 || (0x03000000 <= PY_VERSION_HEX && PY_VERSION_HEX < 0x03030000)
    #error Cython requires Python 2.6+ or Python 3.3+.
#else
#define CYTHON_ABI "0_29_35"
#define CYTHON_HEX_VERSION 0x001D23F0
#define CYTHON_FUTURE_DIVISION 0
#include <stddef.h>
#ifndef offsetof
  #define offsetof(type, member) ( (size_t) & ((type*)0) -> member )
#endif
#if !defined(WIN32) && !defined(MS_WINDOWS)
  #ifndef __stdcall
    #define __stdcall
  #endif
  #ifndef __cdecl
    #define __cdecl
  #endif
  #ifndef __fastcall
    #define __fastcall
  #endif
#endif
#ifndef DL_IMPORT
  #define DL_IMPORT(t) t
#endif
#ifndef DL_EXPORT
  #define DL_EXPORT(t) t
#endif
#define __PYX_COMMA ,
#ifndef HAVE_LONG_LONG
  #if PY_VERSION_HEX >= 0x02070000
    #define HAVE_LONG_LONG
  #endif
#endif
#ifndef PY_LONG_LONG
  #define PY_LONG_LONG LONG_LONG
#endif
#ifndef Py_HUGE_VAL
  #define Py_HUGE_VAL HUGE_VAL
#endif
#ifdef PYPY_VERSION
  #define CYTHON_COMPILING_IN_PYPY 1
  #define CYTHON_COMPILING_IN_PYSTON 0
  #define CYTHON_COMPILING_IN_CPYTHON 0
  #define CYTHON_COMPILING_IN_NOGIL 0
  #undef CYTHON_USE_TYPE_SLOTS
  #define CYTHON_USE_TYPE_SLOTS 0
  #undef CYTHON_USE_PYTYPE_LOOKUP
  #define CYTHON_USE_PYTYPE_LOOKUP 0
  #if PY_VERSION_HEX < 0x03050000
    #undef CYTHON_USE_ASYNC_SLOTS
    #define CYTHON_USE_ASYNC_SLOTS 0
  #elif !defined(CYTHON_USE_ASYNC_SLOTS)
    #define CYTHON_USE_ASYNC_SLOTS 1
  #endif
  #undef CYTHON_USE_PYLIST_INTERNALS
  #define CYTHON_USE_PYLIST_INTERNALS 0
  #undef CYTHON_USE_UNICODE_INTERNALS
  #define CYTHON_USE_UNICODE_INTERNALS 0
  #undef CYTHON_USE_UNICODE_WRITER
  #define CYTHON_USE_UNICODE_WRITER 0
  #undef CYTHON_USE_PYLONG_INTERNALS
  #define CYTHON_USE_PYLONG_INTERNALS 0
  #undef CYTHON_AVOID_BORROWED_REFS
  #define CYTHON_AVOID_BORROWED_REFS 1
  #undef CYTHON_ASSUME_SAFE_MACROS
  #define CYTHON_ASSUME_SAFE_MACROS 0
  #undef CYTHON_UNPACK_METHODS
  #define CYTHON_UNPACK_METHODS 0
  #undef CYTHON_FAST_THREAD_STATE
  #define CYTHON_FAST_THREAD_STATE 0
  #undef CYTHON_FAST_PYCALL
  #define CYTHON_FAST_PYCALL 0
  #if PY_VERSION_HEX < 0x03090000
    #undef CYTHON_PEP489_MULTI_PHASE_INIT
    #define CYTHON_PEP489_MULTI_PHASE_INIT 0
  #elif !defined(CYTHON_PEP489_MULTI_PHASE_INIT)
    #define CYTHON_PEP489_MULTI_PHASE_INIT 1
  #endif
  #undef CYTHON_USE_TP_FINALIZE
  #define CYTHON_USE_TP_FINALIZE 0
  #undef CYTHON_USE_DICT_VERSIONS
  #define CYTHON_USE_DICT_VERSIONS 0
  #undef CYTHON_USE_EXC_INFO_STACK
  #define CYTHON_USE_EXC_INFO_STACK 0
  #ifndef CYTHON_UPDATE_DESCRIPTOR_DOC
    #define CYTHON_UPDATE_DESCRIPTOR_DOC 0
  #endif
#elif defined(PYSTON_VERSION)
  #define CYTHON_COMPILING_IN_PYPY 0
  #define CYTHON_COMPILING_IN_PYSTON 1
  #define CYTHON_COMPILING_IN_CPYTHON 0
  #define CYTHON_COMPILING_IN_NOGIL 0
  #ifndef CYTHON_USE_TYPE_SLOTS
    #define CYTHON_USE_TYPE_SLOTS 1
  #endif
  #undef CYTHON_USE_PYTYPE_LOOKUP
  #define CYTHON_USE_PYTYPE_LOOKUP 0
  #undef CYTHON_USE_ASYNC_SLOTS
  #define CYTHON_USE_ASYNC_SLOTS 0
  #undef CYTHON_USE_PYLIST_INTERNALS
  #define CYTHON_USE_PYLIST_INTERNALS 0
  #ifndef CYTHON_USE_UNICODE_INTERNALS
    #define CYTHON_USE_UNICODE_INTERNALS 1
  #endif
  #undef CYTHON_USE_UNICODE_WRITER
  #define CYTHON_USE_UNICODE_WRITER 0
  #undef CYTHON_USE_PYLONG_INTERNALS
  #define CYTHON_USE_PYLONG_INTERNALS 0
  #ifndef CYTHON_AVOID_BORROWED_REFS
    #define CYTHON_AVOID_BORROWED_REFS 0
  #endif
  #ifndef CYTHON_ASSUME_SAFE_MACROS
    #define CYTHON_ASSUME_SAFE_MACROS 1
  #endif
  #ifndef CYTHON_UNPACK_METHODS
    #define CYTHON_UNPACK_METHODS 1
  #endif
  #undef CYTHON_FAST_THREAD_STATE
  #define CYTHON_FAST_THREAD_STATE 0
  #undef CYTHON_FAST_PYCALL
  #define CYTHON_FAST_PYCALL 0
  #undef CYTHON_PEP489_MULTI_PHASE_INIT
  #define CYTHON_PEP489_MULTI_PHASE_INIT 0
  #undef CYTHON_USE_TP_FINALIZE
  #define CYTHON_USE_TP_FINALIZE 0
  #undef CYTHON_USE_DICT_VERSIONS
  #define CYTHON_USE_DICT_VERSIONS 0
  #undef CYTHON_USE_EXC_INFO_STACK
  #define CYTHON_USE_EXC_INFO_STACK 0
  #ifndef CYTHON_UPDATE_DESCRIPTOR_DOC
    #define CYTHON_UPDATE_DESCRIPTOR_DOC 0
  #endif
#elif defined(PY_NOGIL)
  #define CYTHON_COMPILING_IN_PYPY 0
  #define CYTHON_COMPILING_IN_PYSTON 0
  #define CYTHON_COMPILING_IN_CPYTHON 0
  #define CYTHON_COMPILING_IN_NOGIL 1
  #ifndef CYTHON_USE_TYPE_SLOTS
    #define CYTHON_USE_TYPE_SLOTS 1
  #endif
  #undef CYTHON_USE_PYTYPE_LOOKUP
  #define CYTHON_USE_PYTYPE_LOOKUP 0
  #ifndef CYTHON_USE_ASYNC_SLOTS
    #define CYTHON_USE_ASYNC_SLOTS 1
  #endif
  #undef CYTHON_USE_PYLIST_INTERNALS
  #define CYTHON_USE_PYLIST_INTERNALS 0
  #ifndef CYTHON_USE_UNICODE_INTERNALS
    #define CYTHON_USE_UNICODE_INTERNALS 1
  #endif
  #undef CYTHON_USE_UNICODE_WRITER
  #define CYTHON_USE_UNICODE_WRITER 0
  #undef CYTHON_USE_PYLONG_INTERNALS
  #define CYTHON_USE_PYLONG_INTERNALS 0
  #ifndef CYTHON_AVOID_BORROWED_REFS
    #define CYTHON_AVOID_BORROWED_REFS 0
  #endif
  #ifndef CYTHON_ASSUME_SAFE_MACROS
    #define CYTHON_ASSUME_SAFE_MACROS 1
  #endif
  #ifndef CYTHON_UNPACK_METHODS
    #define CYTHON_UNPACK_METHODS 1
  #endif
  #undef CYTHON_FAST_THREAD_STATE
  #define CYTHON_FAST_THREAD_STATE 0
  #undef CYTHON_FAST_PYCALL
  #define CYTHON_FAST_PYCALL 0
  #ifndef CYTHON_PEP489_MULTI_PHASE_INIT
    #define CYTHON_PEP489_MULTI_PHASE_INIT 1
  #endif
  #ifndef CYTHON_USE_TP_FINALIZE
    #define CYTHON_USE_TP_FINALIZE 1
  #endif
  #undef CYTHON_USE_DICT_VERSIONS
  #define CYTHON_USE_DICT_VERSIONS 0
  #undef CYTHON_USE_EXC_INFO_STACK
  #define CYTHON_USE_EXC_INFO_STACK 0
#else
  #define CYTHON_COMPILING_IN_PYPY 0
  #define CYTHON_COMPILING_IN_PYSTON 0
  #define CYTHON_COMPILING_IN_CPYTHON 1
  #define CYTHON_COMPILING_IN_NOGIL 0
  #ifndef CYTHON_USE_TYPE_SLOTS
    #define CYTHON_USE_TYPE_SLOTS 1
  #endif
  #if PY_VERSION_HEX < 0x02070000
    #undef CYTHON_USE_PYTYPE_LOOKUP
    #define CYTHON_USE_PYTYPE_LOOKUP 0
  #elif !defined(CYTHON_USE_PYTYPE_LOOKUP)
    #define CYTHON_USE_PYTYPE_LOOKUP 1
  #endif
  #if PY_MAJOR_VERSION < 3
    #undef CYTHON_USE_ASYNC_SLOTS
    #define CYTHON_USE_ASYNC_SLOTS 0
  #elif !defined(CYTHON_USE_ASYNC_SLOTS)
    #define CYTHON_USE_ASYNC_SLOTS 1
  #endif
  #if PY_VERSION_HEX < 0x02070000
    #undef CYTHON_USE_PYLONG_INTERNALS
    #define CYTHON_USE_PYLONG_INTERNALS 0
  #elif !defined(CYTHON_USE_PYLONG_INTERNALS)
    #define CYTHON_USE_PYLONG_INTERNALS (PY_VERSION_HEX < 0x030C00A5)
  #endif
  #ifndef CYTHON_USE_PYLIST_INTERNALS
    #define CYTHON_USE_PYLIST_INTERNALS 1
  #endif
  #ifndef CYTHON_USE_UNICODE_INTERNALS
    #define CYTHON_USE_UNICODE_INTERNALS 1
  #endif
  #if PY_VERSION_HEX < 0x030300F0 || PY_VERSION_HEX >= 0x030B00A2
    #undef CYTHON_USE_UNICODE_WRITER
    #define CYTHON_USE_UNICODE_WRITER 0
  #elif !defined(CYTHON_USE_UNICODE_WRITER)
    #define CYTHON_USE_UNICODE_WRITER 1
  #endif
  #ifndef CYTHON_AVOID_BORROWED_REFS
    #define CYTHON_AVOID_BORROWED_REFS 0
  #endif
  #ifndef CYTHON_ASSUME_SAFE_MACROS
    #define CYTHON_ASSUME_SAFE_MACROS 1
  #endif
  #ifndef CYTHON_UNPACK_METHODS
    #define CYTHON_UNPACK_METHODS 1
  #endif
  #if PY_VERSION_HEX >= 0x030B00A4
    #undef CYTHON_FAST_THREAD_STATE
    #define CYTHON_FAST_THREAD_STATE 0
  #elif !defined(CYTHON_FAST_THREAD_STATE)
    #define CYTHON_FAST_THREAD_STATE 1
  #endif
  #ifndef CYTHON_FAST_PYCALL
    #define CYTHON_FAST_PYCALL (PY_VERSION_HEX < 0x030A0000)
  #endif
  #ifndef CYTHON_PEP489_MULTI_PHASE_INIT
    #define CYTHON_PEP489_MULTI_PHASE_INIT (PY_VERSION_HEX >= 0x03050000)
  #endif
  #ifndef CYTHON_USE_TP_FINALIZE
    #define CYTHON_USE_TP_FINALIZE (PY_VERSION_HEX >= 0x030400a1)
  #endif
  #ifndef CYTHON_USE_DICT_VERSIONS
    #define CYTHON_USE_DICT_VERSIONS ((PY_VERSION_HEX >= 0x030600B1) && (PY_VERSION_HEX < 0x030C00A5))
  #endif
  #if PY_VERSION_HEX >= 0x030B00A4
    #undef CYTHON_USE_EXC_INFO_STACK
    #define CYTHON_USE_EXC_INFO_STACK 0
  #elif !defined(CYTHON_USE_EXC_INFO_STACK)
    #define CYTHON_USE_EXC_INFO_STACK (PY_VERSION_HEX >= 0x030700A3)
  #endif
  #ifndef CYTHON_UPDATE_DESCRIPTOR_DOC
    #define CYTHON_UPDATE_DESCRIPTOR_DOC 1
  #endif
#endif
#if !defined(CYTHON_FAST_PYCCALL)
#define CYTHON_FAST_PYCCALL  (CYTHON_FAST_PYCALL && PY_VERSION_HEX >= 0x030600B1)
#endif
#if CYTHON_USE_PYLONG_INTERNALS
  #if PY_MAJOR_VERSION < 3
    #include "longintrepr.h"
  #endif
  #undef SHIFT
  #undef BASE
  #undef MASK
  #ifdef SIZEOF_VOID_P
    enum { __pyx_check_sizeof_voidp = 1 / (int)(SIZEOF_VOID_P == sizeof(void*)) };
  #endif
#endif
#ifndef __has_attribute
  #define __has_attribute(x) 0
#endif
#ifndef __has_cpp_attribute
  #define __has_cpp_attribute(x) 0
#endif
#ifndef CYTHON_RESTRICT
  #if defined(__GNUC__)
    #define CYTHON_RESTRICT __restrict__
  #elif defined(_MSC_VER) && _MSC_VER >= 1400
    #define CYTHON_RESTRICT __restrict
  #elif defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L
    #define CYTHON_RESTRICT restrict
  #else
    #define CYTHON_RESTRICT
  #endif
#endif
#ifndef CYTHON_UNUSED
# if defined(__GNUC__)
#   if !(defined(__cplusplus)) || (__GNUC__ > 3 || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4))
#     define CYTHON_UNUSED __attribute__ ((__unused__))
#   else
#     define CYTHON_UNUSED
#   endif
# elif defined(__ICC) || (defined(__INTEL_COMPILER) && !defined(_MSC_VER))
#   define CYTHON_UNUSED __attribute__ ((__unused__))
# else
#   define CYTHON_UNUSED
# endif
#endif
#ifndef CYTHON_MAYBE_UNUSED_VAR
#  if defined(__cplusplus)
     template<class T> void CYTHON_MAYBE_UNUSED_VAR( const T& ) { }
#  else
#    define CYTHON_MAYBE_UNUSED_VAR(x) (void)(x)
#  endif
#endif
#ifndef CYTHON_NCP_UNUSED
# if CYTHON_COMPILING_IN_CPYTHON
#  define CYTHON_NCP_UNUSED
# else
#  define CYTHON_NCP_UNUSED CYTHON_UNUSED
# endif
#endif
#define __Pyx_void_to_None(void_result) ((void)(void_result), Py_INCREF(Py_None), Py_None)
#ifdef _MSC_VER
    #ifndef _MSC_STDINT_H_
        #if _MSC_VER < 1300
           typedef unsigned char     uint8_t;
           typedef unsigned int      uint32_t;
        #else
           typedef unsigned __int8   uint8_t;
           typedef unsigned __int32  uint32_t;
        #endif
    #endif
#else
   #include <stdint.h>
#endif
#ifndef CYTHON_FALLTHROUGH
  #if defined(__cplusplus) && __cplusplus >= 201103L
    #if __has_cpp_attribute(fallthrough)
      #define CYTHON_FALLTHROUGH [[fallthrough]]
    #elif __has_cpp_attribute(clang::fallthrough)
      #define CYTHON_FALLTHROUGH [[clang::fallthrough]]
    #elif __has_cpp_attribute(gnu::fallthrough)
      #define CYTHON_FALLTHROUGH [[gnu::fallthrough]]
    #endif
  #endif
  #ifndef CYTHON_FALLTHROUGH
    #if __has_attribute(fallthrough)
      #define CYTHON_FALLTHROUGH __attribute__((fallthrough))
    #else
      #define CYTHON_FALLTHROUGH
    #endif
  #endif
  #if defined(__clang__ ) && defined(__apple_build_version__)
    #if __apple_build_version__ < 7000000
      #undef  CYTHON_FALLTHROUGH
      #define CYTHON_FALLTHROUGH
    #endif
  #endif
#endif

#ifndef __cplusplus
  #error "Cython files generated with the C++ option must be compiled with a C++ compiler."
#endif
#ifndef CYTHON_INLINE
  #if defined(__clang__)
    #define CYTHON_INLINE __inline__ __attribute__ ((__unused__))
  #else
    #define CYTHON_INLINE inline
  #endif
#endif
template<typename T>
void __Pyx_call_destructor(T& x) {
    x.~T();
}
template<typename T>
class __Pyx_FakeReference {
  public:
    __Pyx_FakeReference() : ptr(NULL) { }
    __Pyx_FakeReference(const T& ref) : ptr(const_cast<T*>(&ref)) { }
    T *operator->() { return ptr; }
    T *operator&() { return ptr; }
    operator T&() { return *ptr; }
    template<typename U> bool operator ==(U other) { return *ptr == other; }
    template<typename U> bool operator !=(U other) { return *ptr != other; }
  private:
    T *ptr;
};

#if CYTHON_COMPILING_IN_PYPY && PY_VERSION_HEX < 0x02070600 && !defined(Py_OptimizeFlag)
  #define Py_OptimizeFlag 0
#endif
#define __PYX_BUILD_PY_SSIZE_T "n"
#define CYTHON_FORMAT_SSIZE_T "z"
#if PY_MAJOR_VERSION < 3
  #define __Pyx_BUILTIN_MODULE_NAME "__builtin__"
  #define __Pyx_PyCode_New(a, k, l, s, f, code, c, n, v, fv, cell, fn, name, fline, lnos)\
          PyCode_New(a+k, l, s, f, code, c, n, v, fv, cell, fn, name, fline, lnos)
  #define __Pyx_DefaultClassType PyClass_Type
#else
  #define __Pyx_BUILTIN_MODULE_NAME "builtins"
  #define __Pyx_DefaultClassType PyType_Type
#if PY_VERSION_HEX >= 0x030B00A1
    static CYTHON_INLINE PyCodeObject* __Pyx_PyCode_New(int a, int k, int l, int s, int f,
                                                    PyObject *code, PyObject *c, PyObject* n, PyObject *v,
                                                    PyObject *fv, PyObject *cell, PyObject* fn,
                                                    PyObject *name, int fline, PyObject *lnos) {
        PyObject *kwds=NULL, *argcount=NULL, *posonlyargcount=NULL, *kwonlyargcount=NULL;
        PyObject *nlocals=NULL, *stacksize=NULL, *flags=NULL, *replace=NULL, *call_result=NULL, *empty=NULL;
        const char *fn_cstr=NULL;
        const char *name_cstr=NULL;
        PyCodeObject* co=NULL;
        PyObject *type, *value, *traceback;
        PyErr_Fetch(&type, &value, &traceback);
        if (!(kwds=PyDict_New())) goto end;
        if (!(argcount=PyLong_FromLong(a))) goto end;
        if (PyDict_SetItemString(kwds, "co_argcount", argcount) != 0) goto end;
        if (!(posonlyargcount=PyLong_FromLong(0))) goto end;
        if (PyDict_SetItemString(kwds, "co_posonlyargcount", posonlyargcount) != 0) goto end;
        if (!(kwonlyargcount=PyLong_FromLong(k))) goto end;
        if (PyDict_SetItemString(kwds, "co_kwonlyargcount", kwonlyargcount) != 0) goto end;
        if (!(nlocals=PyLong_FromLong(l))) goto end;
        if (PyDict_SetItemString(kwds, "co_nlocals", nlocals) != 0) goto end;
        if (!(stacksize=PyLong_FromLong(s))) goto end;
        if (PyDict_SetItemString(kwds, "co_stacksize", stacksize) != 0) goto end;
        if (!(flags=PyLong_FromLong(f))) goto end;
        if (PyDict_SetItemString(kwds, "co_flags", flags) != 0) goto end;
        if (PyDict_SetItemString(kwds, "co_code", code) != 0) goto end;
        if (PyDict_SetItemString(kwds, "co_consts", c) != 0) goto end;
        if (PyDict_SetItemString(kwds, "co_names", n) != 0) goto end;
        if (PyDict_SetItemString(kwds, "co_varnames", v) != 0) goto end;
        if (PyDict_SetItemString(kwds, "co_freevars", fv) != 0) goto end;
        if (PyDict_SetItemString(kwds, "co_cellvars", cell) != 0) goto end;
        if (PyDict_SetItemString(kwds, "co_linetable", lnos) != 0) goto end;
        if (!(fn_cstr=PyUnicode_AsUTF8AndSize(fn, NULL))) goto end;
        if (!(name_cstr=PyUnicode_AsUTF8AndSize(name, NULL))) goto end;
        if (!(co = PyCode_NewEmpty(fn_cstr, name_cstr, fline))) goto end;
        if (!(replace = PyObject_GetAttrString((PyObject*)co, "replace"))) goto cleanup_code_too;
        if (!(empty = PyTuple_New(0))) goto cleanup_code_too; // unfortunately __pyx_empty_tuple isn't available here
        if (!(call_result = PyObject_Call(replace, empty, kwds))) goto cleanup_code_too;
        Py_XDECREF((PyObject*)co);
        co = (PyCodeObject*)call_result;
        call_result = NULL;
        if (0) {
            cleanup_code_too:
            Py_XDECREF((PyObject*)co);
            co = NULL;
        }
        end:
        Py_XDECREF(kwds);
        Py_XDECREF(argcount);
        Py_XDECREF(posonlyargcount);
        Py_XDECREF(kwonlyargcount);
        Py_XDECREF(nlocals);
        Py_XDECREF(stacksize);
        Py_XDECREF(replace);
        Py_XDECREF(call_result);
        Py_XDECREF(empty);
        if (type) {
            PyErr_Restore(type, value, traceback);
        }
        return co;
    }
#else
  #define __Pyx_PyCode_New(a, k, l, s, f, code, c, n, v, fv, cell, fn, name, fline, lnos)\
          PyCode_New(a, k, l, s, f, code, c, n, v, fv, cell, fn, name, fline, lnos)
#endif
  #define __Pyx_DefaultClassType PyType_Type
#endif
#ifndef Py_TPFLAGS_CHECKTYPES
  #define Py_TPFLAGS_CHECKTYPES 0
#endif
#ifndef Py_TPFLAGS_HAVE_INDEX
  #define Py_TPFLAGS_HAVE_INDEX 0
#endif
#ifndef Py_TPFLAGS_HAVE_NEWBUFFER
  #define Py_TPFLAGS_HAVE_NEWBUFFER 0
#endif
#ifndef Py_TPFLAGS_HAVE_FINALIZE
  #define Py_TPFLAGS_HAVE_FINALIZE 0
#endif
#ifndef METH_STACKLESS
  #define METH_STACKLESS 0
#endif
#if PY_VERSION_HEX <= 0x030700A3 || !defined(METH_FASTCALL)
  #ifndef METH_FASTCALL
     #define METH_FASTCALL 0x80
  #endif
  typedef PyObject *(*__Pyx_PyCFunctionFast) (PyObject *self, PyObject *const *args, Py_ssize_t nargs);
  typedef PyObject *(*__Pyx_PyCFunctionFastWithKeywords) (PyObject *self, PyObject *const *args,
                                                          Py_ssize_t nargs, PyObject *kwnames);
#else
  #define __Pyx_PyCFunctionFast _PyCFunctionFast
  #define __Pyx_PyCFunctionFastWithKeywords _PyCFunctionFastWithKeywords
#endif
#if CYTHON_FAST_PYCCALL
#define __Pyx_PyFastCFunction_Check(func)\
    ((PyCFunction_Check(func) && (METH_FASTCALL == (PyCFunction_GET_FLAGS(func) & ~(METH_CLASS | METH_STATIC | METH_COEXIST | METH_KEYWORDS | METH_STACKLESS)))))
#else
#define __Pyx_PyFastCFunction_Check(func) 0
#endif
#if CYTHON_COMPILING_IN_PYPY && !defined(PyObject_Malloc)
  #define PyObject_Malloc(s)   PyMem_Malloc(s)
  #define PyObject_Free(p)     PyMem_Free(p)
  #define PyObject_Realloc(p)  PyMem_Realloc(p)
#endif
#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX < 0x030400A1
  #define PyMem_RawMalloc(n)           PyMem_Malloc(n)
  #define PyMem_RawRealloc(p, n)       PyMem_Realloc(p, n)
  #define PyMem_RawFree(p)             PyMem_Free(p)
#endif
#if CYTHON_COMPILING_IN_PYSTON
  #define __Pyx_PyCode_HasFreeVars(co)  PyCode_HasFreeVars(co)
  #define __Pyx_PyFrame_SetLineNumber(frame, lineno) PyFrame_SetLineNumber(frame, lineno)
#else
  #define __Pyx_PyCode_HasFreeVars(co)  (PyCode_GetNumFree(co) > 0)
  #define __Pyx_PyFrame_SetLineNumber(frame, lineno)  (frame)->f_lineno = (lineno)
#endif
#if !CYTHON_FAST_THREAD_STATE || PY_VERSION_HEX < 0x02070000
  #define __Pyx_PyThreadState_Current PyThreadState_GET()
#elif PY_VERSION_HEX >= 0x03060000
  #define __Pyx_PyThreadState_Current _PyThreadState_UncheckedGet()
#elif PY_VERSION_HEX >= 0x03000000
  #define __Pyx_PyThreadState_Current PyThreadState_GET()
#else
  #define __Pyx_PyThreadState_Current _PyThreadState_Current
#endif
#if PY_VERSION_HEX < 0x030700A2 && !defined(PyThread_tss_create) && !defined(Py_tss_NEEDS_INIT)
#include "pythread.h"
#define Py_tss_NEEDS_INIT 0
typedef int Py_tss_t;
static CYTHON_INLINE int PyThread_tss_create(Py_tss_t *key) {
  *key = PyThread_create_key();
  return 0;
}
static CYTHON_INLINE Py_tss_t * PyThread_tss_alloc(void) {
  Py_tss_t *key = (Py_tss_t *)PyObject_Malloc(sizeof(Py_tss_t));
  *key = Py_tss_NEEDS_INIT;
  return key;
}
static CYTHON_INLINE void PyThread_tss_free(Py_tss_t *key) {
  PyObject_Free(key);
}
static CYTHON_INLINE int PyThread_tss_is_created(Py_tss_t *key) {
  return *key != Py_tss_NEEDS_INIT;
}
static CYTHON_INLINE void PyThread_tss_delete(Py_tss_t *key) {
  PyThread_delete_key(*key);
  *key = Py_tss_NEEDS_INIT;
}
static CYTHON_INLINE int PyThread_tss_set(Py_tss_t *key, void *value) {
  return PyThread_set_key_value(*key, value);
}
static CYTHON_INLINE void * PyThread_tss_get(Py_tss_t *key) {
  return PyThread_get_key_value(*key);
}
#endif
#if CYTHON_COMPILING_IN_CPYTHON || defined(_PyDict_NewPresized)
#define __Pyx_PyDict_NewPresized(n)  ((n <= 8) ? PyDict_New() : _PyDict_NewPresized(n))
#else
#define __Pyx_PyDict_NewPresized(n)  PyDict_New()
#endif
#if PY_MAJOR_VERSION >= 3 || CYTHON_FUTURE_DIVISION
  #define __Pyx_PyNumber_Divide(x,y)         PyNumber_TrueDivide(x,y)
  #define __Pyx_PyNumber_InPlaceDivide(x,y)  PyNumber_InPlaceTrueDivide(x,y)
#else
  #define __Pyx_PyNumber_Divide(x,y)         PyNumber_Divide(x,y)
  #define __Pyx_PyNumber_InPlaceDivide(x,y)  PyNumber_InPlaceDivide(x,y)
#endif
#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX >= 0x030500A1 && CYTHON_USE_UNICODE_INTERNALS
#define __Pyx_PyDict_GetItemStr(dict, name)  _PyDict_GetItem_KnownHash(dict, name, ((PyASCIIObject *) name)->hash)
#else
#define __Pyx_PyDict_GetItemStr(dict, name)  PyDict_GetItem(dict, name)
#endif
#if PY_VERSION_HEX > 0x03030000 && defined(PyUnicode_KIND)
  #define CYTHON_PEP393_ENABLED 1
  #if PY_VERSION_HEX >= 0x030C0000
    #define __Pyx_PyUnicode_READY(op)       (0)
  #else
    #define __Pyx_PyUnicode_READY(op)       (likely(PyUnicode_IS_READY(op)) ?\
                                                0 : _PyUnicode_Ready((PyObject *)(op)))
  #endif
  #define __Pyx_PyUnicode_GET_LENGTH(u)   PyUnicode_GET_LENGTH(u)
  #define __Pyx_PyUnicode_READ_CHAR(u, i) PyUnicode_READ_CHAR(u, i)
  #define __Pyx_PyUnicode_MAX_CHAR_VALUE(u)   PyUnicode_MAX_CHAR_VALUE(u)
  #define __Pyx_PyUnicode_KIND(u)         PyUnicode_KIND(u)
  #define __Pyx_PyUnicode_DATA(u)         PyUnicode_DATA(u)
  #define __Pyx_PyUnicode_READ(k, d, i)   PyUnicode_READ(k, d, i)
  #define __Pyx_PyUnicode_WRITE(k, d, i, ch)  PyUnicode_WRITE(k, d, i, ch)
  #if PY_VERSION_HEX >= 0x030C0000
    #define __Pyx_PyUnicode_IS_TRUE(u)      (0 != PyUnicode_GET_LENGTH(u))
  #else
    #if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX >= 0x03090000
    #define __Pyx_PyUnicode_IS_TRUE(u)      (0 != (likely(PyUnicode_IS_READY(u)) ? PyUnicode_GET_LENGTH(u) : ((PyCompactUnicodeObject *)(u))->wstr_length))
    #else
    #define __Pyx_PyUnicode_IS_TRUE(u)      (0 != (likely(PyUnicode_IS_READY(u)) ? PyUnicode_GET_LENGTH(u) : PyUnicode_GET_SIZE(u)))
    #endif
  #endif
#else
  #define CYTHON_PEP393_ENABLED 0
  #define PyUnicode_1BYTE_KIND  1
  #define PyUnicode_2BYTE_KIND  2
  #define PyUnicode_4BYTE_KIND  4
  #define __Pyx_PyUnicode_READY(op)       (0)
  #define __Pyx_PyUnicode_GET_LENGTH(u)   PyUnicode_GET_SIZE(u)
  #define __Pyx_PyUnicode_READ_CHAR(u, i) ((Py_UCS4)(PyUnicode_AS_UNICODE(u)[i]))
  #define __Pyx_PyUnicode_MAX_CHAR_VALUE(u)   ((sizeof(Py_UNICODE) == 2) ? 65535 : 1114111)
  #define __Pyx_PyUnicode_KIND(u)         (sizeof(Py_UNICODE))
  #define __Pyx_PyUnicode_DATA(u)         ((void*)PyUnicode_AS_UNICODE(u))
  #define __Pyx_PyUnicode_READ(k, d, i)   ((void)(k), (Py_UCS4)(((Py_UNICODE*)d)[i]))
  #define __Pyx_PyUnicode_WRITE(k, d, i, ch)  (((void)(k)), ((Py_UNICODE*)d)[i] = ch)
  #define __Pyx_PyUnicode_IS_TRUE(u)      (0 != PyUnicode_GET_SIZE(u))
#endif
#if CYTHON_COMPILING_IN_PYPY
  #define __Pyx_PyUnicode_Concat(a, b)      PyNumber_Add(a, b)
  #define __Pyx_PyUnicode_ConcatSafe(a, b)  PyNumber_Add(a, b)
#else
  #define __Pyx_PyUnicode_Concat(a, b)      PyUnicode_Concat(a, b)
  #define __Pyx_PyUnicode_ConcatSafe(a, b)  ((unlikely((a) == Py_None) || unlikely((b) == Py_None)) ?\
      PyNumber_Add(a, b) : __Pyx_PyUnicode_Concat(a, b))
#endif
#if CYTHON_COMPILING_IN_PYPY && !defined(PyUnicode_Contains)
  #define PyUnicode_Contains(u, s)  PySequence_Contains(u, s)
#endif
#if CYTHON_COMPILING_IN_PYPY && !defined(PyByteArray_Check)
  #define PyByteArray_Check(obj)  PyObject_TypeCheck(obj, &PyByteArray_Type)
#endif
#if CYTHON_COMPILING_IN_PYPY && !defined(PyObject_Format)
  #define PyObject_Format(obj, fmt)  PyObject_CallMethod(obj, "__format__", "O", fmt)
#endif
#define __Pyx_PyString_FormatSafe(a, b)   ((unlikely((a) == Py_None || (PyString_Check(b) && !PyString_CheckExact(b)))) ? PyNumber_Remainder(a, b) : __Pyx_PyString_Format(a, b))
#define __Pyx_PyUnicode_FormatSafe(a, b)  ((unlikely((a) == Py_None || (PyUnicode_Check(b) && !PyUnicode_CheckExact(b)))) ? PyNumber_Remainder(a, b) : PyUnicode_Format(a, b))
#if PY_MAJOR_VERSION >= 3
  #define __Pyx_PyString_Format(a, b)  PyUnicode_Format(a, b)
#else
  #define __Pyx_PyString_Format(a, b)  PyString_Format(a, b)
#endif
#if PY_MAJOR_VERSION < 3 && !defined(PyObject_ASCII)
  #define PyObject_ASCII(o)            PyObject_Repr(o)
#endif
#if PY_MAJOR_VERSION >= 3
  #define PyBaseString_Type            PyUnicode_Type
  #define PyStringObject               PyUnicodeObject
  #define PyString_Type                PyUnicode_Type
  #define PyString_Check               PyUnicode_Check
  #define PyString_CheckExact          PyUnicode_CheckExact
#ifndef PyObject_Unicode
  #define PyObject_Unicode             PyObject_Str
#endif
#endif
#if PY_MAJOR_VERSION >= 3
  #define __Pyx_PyBaseString_Check(obj) PyUnicode_Check(obj)
  #define __Pyx_PyBaseString_CheckExact(obj) PyUnicode_CheckExact(obj)
#else
  #define __Pyx_PyBaseString_Check(obj) (PyString_Check(obj) || PyUnicode_Check(obj))
  #define __Pyx_PyBaseString_CheckExact(obj) (PyString_CheckExact(obj) || PyUnicode_CheckExact(obj))
#endif
#ifndef PySet_CheckExact
  #define PySet_CheckExact(obj)        (Py_TYPE(obj) == &PySet_Type)
#endif
#if PY_VERSION_HEX >= 0x030900A4
  #define __Pyx_SET_REFCNT(obj, refcnt) Py_SET_REFCNT(obj, refcnt)
  #define __Pyx_SET_SIZE(obj, size) Py_SET_SIZE(obj, size)
#else
  #define __Pyx_SET_REFCNT(obj, refcnt) Py_REFCNT(obj) = (refcnt)
  #define __Pyx_SET_SIZE(obj, size) Py_SIZE(obj) = (size)
#endif
#if CYTHON_ASSUME_SAFE_MACROS
  #define __Pyx_PySequence_SIZE(seq)  Py_SIZE(seq)
#else
  #define __Pyx_PySequence_SIZE(seq)  PySequence_Size(seq)
#endif
#if PY_MAJOR_VERSION >= 3
  #define PyIntObject                  PyLongObject
  #define PyInt_Type                   PyLong_Type
  #define PyInt_Check(op)              PyLong_Check(op)
  #define PyInt_CheckExact(op)         PyLong_CheckExact(op)
  #define PyInt_FromString             PyLong_FromString
  #define PyInt_FromUnicode            PyLong_FromUnicode
  #define PyInt_FromLong               PyLong_FromLong
  #define PyInt_FromSize_t             PyLong_FromSize_t
  #define PyInt_FromSsize_t            PyLong_FromSsize_t
  #define PyInt_AsLong                 PyLong_AsLong
  #define PyInt_AS_LONG                PyLong_AS_LONG
  #define PyInt_AsSsize_t              PyLong_AsSsize_t
  #define PyInt_AsUnsignedLongMask     PyLong_AsUnsignedLongMask
  #define PyInt_AsUnsignedLongLongMask PyLong_AsUnsignedLongLongMask
  #define PyNumber_Int                 PyNumber_Long
#endif
#if PY_MAJOR_VERSION >= 3
  #define PyBoolObject                 PyLongObject
#endif
#if PY_MAJOR_VERSION >= 3 && CYTHON_COMPILING_IN_PYPY
  #ifndef PyUnicode_InternFromString
    #define PyUnicode_InternFromString(s) PyUnicode_FromString(s)
  #endif
#endif
#if PY_VERSION_HEX < 0x030200A4
  typedef long Py_hash_t;
  #define __Pyx_PyInt_FromHash_t PyInt_FromLong
  #define __Pyx_PyInt_AsHash_t   __Pyx_PyIndex_AsHash_t
#else
  #define __Pyx_PyInt_FromHash_t PyInt_FromSsize_t
  #define __Pyx_PyInt_AsHash_t   __Pyx_PyIndex_AsSsize_t
#endif
#if PY_MAJOR_VERSION >= 3
  #define __Pyx_PyMethod_New(func, self, klass) ((self) ? ((void)(klass), PyMethod_New(func, self)) : __Pyx_NewRef(func))
#else
  #define __Pyx_PyMethod_New(func, self, klass) PyMethod_New(func, self, klass)
#endif
#if CYTHON_USE_ASYNC_SLOTS
  #if PY_VERSION_HEX >= 0x030500B1
    #define __Pyx_PyAsyncMethodsStruct PyAsyncMethods
    #define __Pyx_PyType_AsAsync(obj) (Py_TYPE(obj)->tp_as_async)
  #else
    #define __Pyx_PyType_AsAsync(obj) ((__Pyx_PyAsyncMethodsStruct*) (Py_TYPE(obj)->tp_reserved))
  #endif
#else
  #define __Pyx_PyType_AsAsync(obj) NULL
#endif
#ifndef __Pyx_PyAsyncMethodsStruct
    typedef struct {
        unaryfunc am_await;
        unaryfunc am_aiter;
        unaryfunc am_anext;
    } __Pyx_PyAsyncMethodsStruct;
#endif

#if defined(_WIN32) || defined(WIN32) || defined(MS_WINDOWS)
  #if !defined(_USE_MATH_DEFINES)
    #define _USE_MATH_DEFINES
  #endif
#endif
#include <math.h>
#ifdef NAN
#define __PYX_NAN() ((float) NAN)
#else
static CYTHON_INLINE float __PYX_NAN() {
  float value;
  memset(&value, 0xFF, sizeof(value));
  return value;
}
#endif
#if defined(__CYGWIN__) && defined(_LDBL_EQ_DBL)
#define __Pyx_truncl trunc
#else
#define __Pyx_truncl truncl
#endif

#define __PYX_MARK_ERR_POS(f_index, lineno) \
    { __pyx_filename = __pyx_f[f_index]; (void)__pyx_filename; __pyx_lineno = lineno; (void)__pyx_lineno; __pyx_clineno = __LINE__; (void)__pyx_clineno; }
#define __PYX_ERR(f_index, lineno, Ln_error) \
    { __PYX_MARK_ERR_POS(f_index, lineno) goto Ln_error; }

#ifndef __PYX_EXTERN_C
  #ifdef __cplusplus
    #define __PYX_EXTERN_C extern "C"
  #else
    #define __PYX_EXTERN_C extern
  #endif
#endif

#define __PYX_HAVE__spacy__attrs
#define __PYX_HAVE_API__spacy__attrs
/* Early includes */
#ifdef _OPENMP
#include <omp.h>
#endif /* _OPENMP */

#if defined(PYREX_WITHOUT_ASSERTIONS) && !defined(CYTHON_WITHOUT_ASSERTIONS)
#define CYTHON_WITHOUT_ASSERTIONS
#endif

typedef struct {PyObject **p; const char *s; const Py_ssize_t n; const char* encoding;
                const char is_unicode; const char is_str; const char intern; } __Pyx_StringTabEntry;

#define __PYX_DEFAULT_STRING_ENCODING_IS_ASCII 0
#define __PYX_DEFAULT_STRING_ENCODING_IS_UTF8 0
#define __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT (PY_MAJOR_VERSION >= 3 && __PYX_DEFAULT_STRING_ENCODING_IS_UTF8)
#define __PYX_DEFAULT_STRING_ENCODING ""
#define __Pyx_PyObject_FromString __Pyx_PyBytes_FromString
#define __Pyx_PyObject_FromStringAndSize __Pyx_PyBytes_FromStringAndSize
#define __Pyx_uchar_cast(c) ((unsigned char)c)
#define __Pyx_long_cast(x) ((long)x)
#define __Pyx_fits_Py_ssize_t(v, type, is_signed)  (\
    (sizeof(type) < sizeof(Py_ssize_t))  ||\
    (sizeof(type) > sizeof(Py_ssize_t) &&\
          likely(v < (type)PY_SSIZE_T_MAX ||\
                 v == (type)PY_SSIZE_T_MAX)  &&\
          (!is_signed || likely(v > (type)PY_SSIZE_T_MIN ||\
                                v == (type)PY_SSIZE_T_MIN)))  ||\
    (sizeof(type) == sizeof(Py_ssize_t) &&\
          (is_signed || likely(v < (type)PY_SSIZE_T_MAX ||\
                               v == (type)PY_SSIZE_T_MAX)))  )
static CYTHON_INLINE int __Pyx_is_valid_index(Py_ssize_t i, Py_ssize_t limit) {
    return (size_t) i < (size_t) limit;
}
#if defined (__cplusplus) && __cplusplus >= 201103L
    #include <cstdlib>
    #define __Pyx_sst_abs(value) std::abs(value)
#elif SIZEOF_INT >= SIZEOF_SIZE_T
    #define __Pyx_sst_abs(value) abs(value)
#elif SIZEOF_LONG >= SIZEOF_SIZE_T
    #define __Pyx_sst_abs(value) labs(value)
#elif defined (_MSC_VER)
    #define __Pyx_sst_abs(value) ((Py_ssize_t)_abs64(value))
#elif defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L
    #define __Pyx_sst_abs(value) llabs(value)
#elif defined (__GNUC__)
    #define __Pyx_sst_abs(value) __builtin_llabs(value)
#else
    #define __Pyx_sst_abs(value) ((value<0) ? -value : value)
#endif
static CYTHON_INLINE const char* __Pyx_PyObject_AsString(PyObject*);
static CYTHON_INLINE const char* __Pyx_PyObject_AsStringAndSize(PyObject*, Py_ssize_t* length);
#define __Pyx_PyByteArray_FromString(s) PyByteArray_FromStringAndSize((const char*)s, strlen((const char*)s))
#define __Pyx_PyByteArray_FromStringAndSize(s, l) PyByteArray_FromStringAndSize((const char*)s, l)
#define __Pyx_PyBytes_FromString        PyBytes_FromString
#define __Pyx_PyBytes_FromStringAndSize PyBytes_FromStringAndSize
static CYTHON_INLINE PyObject* __Pyx_PyUnicode_FromString(const char*);
#if PY_MAJOR_VERSION < 3
    #define __Pyx_PyStr_FromString        __Pyx_PyBytes_FromString
    #define __Pyx_PyStr_FromStringAndSize __Pyx_PyBytes_FromStringAndSize
#else
    #define __Pyx_PyStr_FromString        __Pyx_PyUnicode_FromString
    #define __Pyx_PyStr_FromStringAndSize __Pyx_PyUnicode_FromStringAndSize
#endif
#define __Pyx_PyBytes_AsWritableString(s)     ((char*) PyBytes_AS_STRING(s))
#define __Pyx_PyBytes_AsWritableSString(s)    ((signed char*) PyBytes_AS_STRING(s))
#define __Pyx_PyBytes_AsWritableUString(s)    ((unsigned char*) PyBytes_AS_STRING(s))
#define __Pyx_PyBytes_AsString(s)     ((const char*) PyBytes_AS_STRING(s))
#define __Pyx_PyBytes_AsSString(s)    ((const signed char*) PyBytes_AS_STRING(s))
#define __Pyx_PyBytes_AsUString(s)    ((const unsigned char*) PyBytes_AS_STRING(s))
#define __Pyx_PyObject_AsWritableString(s)    ((char*) __Pyx_PyObject_AsString(s))
#define __Pyx_PyObject_AsWritableSString(s)    ((signed char*) __Pyx_PyObject_AsString(s))
#define __Pyx_PyObject_AsWritableUString(s)    ((unsigned char*) __Pyx_PyObject_AsString(s))
#define __Pyx_PyObject_AsSString(s)    ((const signed char*) __Pyx_PyObject_AsString(s))
#define __Pyx_PyObject_AsUString(s)    ((const unsigned char*) __Pyx_PyObject_AsString(s))
#define __Pyx_PyObject_FromCString(s)  __Pyx_PyObject_FromString((const char*)s)
#define __Pyx_PyBytes_FromCString(s)   __Pyx_PyBytes_FromString((const char*)s)
#define __Pyx_PyByteArray_FromCString(s)   __Pyx_PyByteArray_FromString((const char*)s)
#define __Pyx_PyStr_FromCString(s)     __Pyx_PyStr_FromString((const char*)s)
#define __Pyx_PyUnicode_FromCString(s) __Pyx_PyUnicode_FromString((const char*)s)
static CYTHON_INLINE size_t __Pyx_Py_UNICODE_strlen(const Py_UNICODE *u) {
    const Py_UNICODE *u_end = u;
    while (*u_end++) ;
    return (size_t)(u_end - u - 1);
}
#define __Pyx_PyUnicode_FromUnicode(u)       PyUnicode_FromUnicode(u, __Pyx_Py_UNICODE_strlen(u))
#define __Pyx_PyUnicode_FromUnicodeAndLength PyUnicode_FromUnicode
#define __Pyx_PyUnicode_AsUnicode            PyUnicode_AsUnicode
#define __Pyx_NewRef(obj) (Py_INCREF(obj), obj)
#define __Pyx_Owned_Py_None(b) __Pyx_NewRef(Py_None)
static CYTHON_INLINE PyObject * __Pyx_PyBool_FromLong(long b);
static CYTHON_INLINE int __Pyx_PyObject_IsTrue(PyObject*);
static CYTHON_INLINE int __Pyx_PyObject_IsTrueAndDecref(PyObject*);
static CYTHON_INLINE PyObject* __Pyx_PyNumber_IntOrLong(PyObject* x);
#define __Pyx_PySequence_Tuple(obj)\
    (likely(PyTuple_CheckExact(obj)) ? __Pyx_NewRef(obj) : PySequence_Tuple(obj))
static CYTHON_INLINE Py_ssize_t __Pyx_PyIndex_AsSsize_t(PyObject*);
static CYTHON_INLINE PyObject * __Pyx_PyInt_FromSize_t(size_t);
static CYTHON_INLINE Py_hash_t __Pyx_PyIndex_AsHash_t(PyObject*);
#if CYTHON_ASSUME_SAFE_MACROS
#define __pyx_PyFloat_AsDouble(x) (PyFloat_CheckExact(x) ? PyFloat_AS_DOUBLE(x) : PyFloat_AsDouble(x))
#else
#define __pyx_PyFloat_AsDouble(x) PyFloat_AsDouble(x)
#endif
#define __pyx_PyFloat_AsFloat(x) ((float) __pyx_PyFloat_AsDouble(x))
#if PY_MAJOR_VERSION >= 3
#define __Pyx_PyNumber_Int(x) (PyLong_CheckExact(x) ? __Pyx_NewRef(x) : PyNumber_Long(x))
#else
#define __Pyx_PyNumber_Int(x) (PyInt_CheckExact(x) ? __Pyx_NewRef(x) : PyNumber_Int(x))
#endif
#define __Pyx_PyNumber_Float(x) (PyFloat_CheckExact(x) ? __Pyx_NewRef(x) : PyNumber_Float(x))
#if PY_MAJOR_VERSION < 3 && __PYX_DEFAULT_STRING_ENCODING_IS_ASCII
static int __Pyx_sys_getdefaultencoding_not_ascii;
static int __Pyx_init_sys_getdefaultencoding_params(void) {
    PyObject* sys;
    PyObject* default_encoding = NULL;
    PyObject* ascii_chars_u = NULL;
    PyObject* ascii_chars_b = NULL;
    const char* default_encoding_c;
    sys = PyImport_ImportModule("sys");
    if (!sys) goto bad;
    default_encoding = PyObject_CallMethod(sys, (char*) "getdefaultencoding", NULL);
    Py_DECREF(sys);
    if (!default_encoding) goto bad;
    default_encoding_c = PyBytes_AsString(default_encoding);
    if (!default_encoding_c) goto bad;
    if (strcmp(default_encoding_c, "ascii") == 0) {
        __Pyx_sys_getdefaultencoding_not_ascii = 0;
    } else {
        char ascii_chars[128];
        int c;
        for (c = 0; c < 128; c++) {
            ascii_chars[c] = c;
        }
        __Pyx_sys_getdefaultencoding_not_ascii = 1;
        ascii_chars_u = PyUnicode_DecodeASCII(ascii_chars, 128, NULL);
        if (!ascii_chars_u) goto bad;
        ascii_chars_b = PyUnicode_AsEncodedString(ascii_chars_u, default_encoding_c, NULL);
        if (!ascii_chars_b || !PyBytes_Check(ascii_chars_b) || memcmp(ascii_chars, PyBytes_AS_STRING(ascii_chars_b), 128) != 0) {
            PyErr_Format(
                PyExc_ValueError,
                "This module compiled with c_string_encoding=ascii, but default encoding '%.200s' is not a superset of ascii.",
                default_encoding_c);
            goto bad;
        }
        Py_DECREF(ascii_chars_u);
        Py_DECREF(ascii_chars_b);
    }
    Py_DECREF(default_encoding);
    return 0;
bad:
    Py_XDECREF(default_encoding);
    Py_XDECREF(ascii_chars_u);
    Py_XDECREF(ascii_chars_b);
    return -1;
}
#endif
#if __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT && PY_MAJOR_VERSION >= 3
#define __Pyx_PyUnicode_FromStringAndSize(c_str, size) PyUnicode_DecodeUTF8(c_str, size, NULL)
#else
#define __Pyx_PyUnicode_FromStringAndSize(c_str, size) PyUnicode_Decode(c_str, size, __PYX_DEFAULT_STRING_ENCODING, NULL)
#if __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT
static char* __PYX_DEFAULT_STRING_ENCODING;
static int __Pyx_init_sys_getdefaultencoding_params(void) {
    PyObject* sys;
    PyObject* default_encoding = NULL;
    char* default_encoding_c;
    sys = PyImport_ImportModule("sys");
    if (!sys) goto bad;
    default_encoding = PyObject_CallMethod(sys, (char*) (const char*) "getdefaultencoding", NULL);
    Py_DECREF(sys);
    if (!default_encoding) goto bad;
    default_encoding_c = PyBytes_AsString(default_encoding);
    if (!default_encoding_c) goto bad;
    __PYX_DEFAULT_STRING_ENCODING = (char*) malloc(strlen(default_encoding_c) + 1);
    if (!__PYX_DEFAULT_STRING_ENCODING) goto bad;
    strcpy(__PYX_DEFAULT_STRING_ENCODING, default_encoding_c);
    Py_DECREF(default_encoding);
    return 0;
bad:
    Py_XDECREF(default_encoding);
    return -1;
}
#endif
#endif


/* Test for GCC > 2.95 */
#if defined(__GNUC__)     && (__GNUC__ > 2 || (__GNUC__ == 2 && (__GNUC_MINOR__ > 95)))
  #define likely(x)   __builtin_expect(!!(x), 1)
  #define unlikely(x) __builtin_expect(!!(x), 0)
#else /* !__GNUC__ or GCC < 2.95 */
  #define likely(x)   (x)
  #define unlikely(x) (x)
#endif /* __GNUC__ */
static CYTHON_INLINE void __Pyx_pretend_to_initialize(void* ptr) { (void)ptr; }

static PyObject *__pyx_m = NULL;
static PyObject *__pyx_d;
static PyObject *__pyx_b;
static PyObject *__pyx_cython_runtime = NULL;
static PyObject *__pyx_empty_tuple;
static PyObject *__pyx_empty_bytes;
static PyObject *__pyx_empty_unicode;
static int __pyx_lineno;
static int __pyx_clineno = 0;
static const char * __pyx_cfilenm= __FILE__;
static const char *__pyx_filename;


static const char *__pyx_f[] = {
  "spacy\\attrs.pyx",
};

/*--- Type declarations ---*/

/* "symbols.pxd":1
 * cdef enum symbol_t:             # <<<<<<<<<<<<<<
 *     NIL
 *     IS_ALPHA
 */
enum __pyx_t_5spacy_7symbols_symbol_t {
  __pyx_e_5spacy_7symbols_NIL,
  __pyx_e_5spacy_7symbols_IS_ALPHA,
  __pyx_e_5spacy_7symbols_IS_ASCII,
  __pyx_e_5spacy_7symbols_IS_DIGIT,
  __pyx_e_5spacy_7symbols_IS_LOWER,
  __pyx_e_5spacy_7symbols_IS_PUNCT,
  __pyx_e_5spacy_7symbols_IS_SPACE,
  __pyx_e_5spacy_7symbols_IS_TITLE,
  __pyx_e_5spacy_7symbols_IS_UPPER,
  __pyx_e_5spacy_7symbols_LIKE_URL,
  __pyx_e_5spacy_7symbols_LIKE_NUM,
  __pyx_e_5spacy_7symbols_LIKE_EMAIL,
  __pyx_e_5spacy_7symbols_IS_STOP,
  __pyx_e_5spacy_7symbols_IS_OOV_DEPRECATED,
  __pyx_e_5spacy_7symbols_IS_BRACKET,
  __pyx_e_5spacy_7symbols_IS_QUOTE,
  __pyx_e_5spacy_7symbols_IS_LEFT_PUNCT,
  __pyx_e_5spacy_7symbols_IS_RIGHT_PUNCT,
  __pyx_e_5spacy_7symbols_IS_CURRENCY,
  __pyx_e_5spacy_7symbols_FLAG19 = 19,
  __pyx_e_5spacy_7symbols_FLAG20,
  __pyx_e_5spacy_7symbols_FLAG21,
  __pyx_e_5spacy_7symbols_FLAG22,
  __pyx_e_5spacy_7symbols_FLAG23,
  __pyx_e_5spacy_7symbols_FLAG24,
  __pyx_e_5spacy_7symbols_FLAG25,
  __pyx_e_5spacy_7symbols_FLAG26,
  __pyx_e_5spacy_7symbols_FLAG27,
  __pyx_e_5spacy_7symbols_FLAG28,
  __pyx_e_5spacy_7symbols_FLAG29,
  __pyx_e_5spacy_7symbols_FLAG30,
  __pyx_e_5spacy_7symbols_FLAG31,
  __pyx_e_5spacy_7symbols_FLAG32,
  __pyx_e_5spacy_7symbols_FLAG33,
  __pyx_e_5spacy_7symbols_FLAG34,
  __pyx_e_5spacy_7symbols_FLAG35,
  __pyx_e_5spacy_7symbols_FLAG36,
  __pyx_e_5spacy_7symbols_FLAG37,
  __pyx_e_5spacy_7symbols_FLAG38,
  __pyx_e_5spacy_7symbols_FLAG39,
  __pyx_e_5spacy_7symbols_FLAG40,
  __pyx_e_5spacy_7symbols_FLAG41,
  __pyx_e_5spacy_7symbols_FLAG42,
  __pyx_e_5spacy_7symbols_FLAG43,
  __pyx_e_5spacy_7symbols_FLAG44,
  __pyx_e_5spacy_7symbols_FLAG45,
  __pyx_e_5spacy_7symbols_FLAG46,
  __pyx_e_5spacy_7symbols_FLAG47,
  __pyx_e_5spacy_7symbols_FLAG48,
  __pyx_e_5spacy_7symbols_FLAG49,
  __pyx_e_5spacy_7symbols_FLAG50,
  __pyx_e_5spacy_7symbols_FLAG51,
  __pyx_e_5spacy_7symbols_FLAG52,
  __pyx_e_5spacy_7symbols_FLAG53,
  __pyx_e_5spacy_7symbols_FLAG54,
  __pyx_e_5spacy_7symbols_FLAG55,
  __pyx_e_5spacy_7symbols_FLAG56,
  __pyx_e_5spacy_7symbols_FLAG57,
  __pyx_e_5spacy_7symbols_FLAG58,
  __pyx_e_5spacy_7symbols_FLAG59,
  __pyx_e_5spacy_7symbols_FLAG60,
  __pyx_e_5spacy_7symbols_FLAG61,
  __pyx_e_5spacy_7symbols_FLAG62,
  __pyx_e_5spacy_7symbols_FLAG63,
  __pyx_e_5spacy_7symbols_ID,
  __pyx_e_5spacy_7symbols_ORTH,
  __pyx_e_5spacy_7symbols_LOWER,
  __pyx_e_5spacy_7symbols_NORM,
  __pyx_e_5spacy_7symbols_SHAPE,
  __pyx_e_5spacy_7symbols_PREFIX,
  __pyx_e_5spacy_7symbols_SUFFIX,
  __pyx_e_5spacy_7symbols_LENGTH,
  __pyx_e_5spacy_7symbols_CLUSTER,
  __pyx_e_5spacy_7symbols_LEMMA,
  __pyx_e_5spacy_7symbols_POS,
  __pyx_e_5spacy_7symbols_TAG,
  __pyx_e_5spacy_7symbols_DEP,
  __pyx_e_5spacy_7symbols_ENT_IOB,
  __pyx_e_5spacy_7symbols_ENT_TYPE,
  __pyx_e_5spacy_7symbols_HEAD,
  __pyx_e_5spacy_7symbols_SENT_START,
  __pyx_e_5spacy_7symbols_SPACY,
  __pyx_e_5spacy_7symbols_PROB,
  __pyx_e_5spacy_7symbols_LANG,
  __pyx_e_5spacy_7symbols_ADJ,
  __pyx_e_5spacy_7symbols_ADP,
  __pyx_e_5spacy_7symbols_ADV,
  __pyx_e_5spacy_7symbols_AUX,
  __pyx_e_5spacy_7symbols_CONJ,
  __pyx_e_5spacy_7symbols_CCONJ,
  __pyx_e_5spacy_7symbols_DET,
  __pyx_e_5spacy_7symbols_INTJ,
  __pyx_e_5spacy_7symbols_NOUN,
  __pyx_e_5spacy_7symbols_NUM,
  __pyx_e_5spacy_7symbols_PART,
  __pyx_e_5spacy_7symbols_PRON,
  __pyx_e_5spacy_7symbols_PROPN,
  __pyx_e_5spacy_7symbols_PUNCT,
  __pyx_e_5spacy_7symbols_SCONJ,
  __pyx_e_5spacy_7symbols_SYM,
  __pyx_e_5spacy_7symbols_VERB,
  __pyx_e_5spacy_7symbols_X,
  __pyx_e_5spacy_7symbols_EOL,
  __pyx_e_5spacy_7symbols_SPACE,
  __pyx_e_5spacy_7symbols_DEPRECATED001,
  __pyx_e_5spacy_7symbols_DEPRECATED002,
  __pyx_e_5spacy_7symbols_DEPRECATED003,
  __pyx_e_5spacy_7symbols_DEPRECATED004,
  __pyx_e_5spacy_7symbols_DEPRECATED005,
  __pyx_e_5spacy_7symbols_DEPRECATED006,
  __pyx_e_5spacy_7symbols_DEPRECATED007,
  __pyx_e_5spacy_7symbols_DEPRECATED008,
  __pyx_e_5spacy_7symbols_DEPRECATED009,
  __pyx_e_5spacy_7symbols_DEPRECATED010,
  __pyx_e_5spacy_7symbols_DEPRECATED011,
  __pyx_e_5spacy_7symbols_DEPRECATED012,
  __pyx_e_5spacy_7symbols_DEPRECATED013,
  __pyx_e_5spacy_7symbols_DEPRECATED014,
  __pyx_e_5spacy_7symbols_DEPRECATED015,
  __pyx_e_5spacy_7symbols_DEPRECATED016,
  __pyx_e_5spacy_7symbols_DEPRECATED017,
  __pyx_e_5spacy_7symbols_DEPRECATED018,
  __pyx_e_5spacy_7symbols_DEPRECATED019,
  __pyx_e_5spacy_7symbols_DEPRECATED020,
  __pyx_e_5spacy_7symbols_DEPRECATED021,
  __pyx_e_5spacy_7symbols_DEPRECATED022,
  __pyx_e_5spacy_7symbols_DEPRECATED023,
  __pyx_e_5spacy_7symbols_DEPRECATED024,
  __pyx_e_5spacy_7symbols_DEPRECATED025,
  __pyx_e_5spacy_7symbols_DEPRECATED026,
  __pyx_e_5spacy_7symbols_DEPRECATED027,
  __pyx_e_5spacy_7symbols_DEPRECATED028,
  __pyx_e_5spacy_7symbols_DEPRECATED029,
  __pyx_e_5spacy_7symbols_DEPRECATED030,
  __pyx_e_5spacy_7symbols_DEPRECATED031,
  __pyx_e_5spacy_7symbols_DEPRECATED032,
  __pyx_e_5spacy_7symbols_DEPRECATED033,
  __pyx_e_5spacy_7symbols_DEPRECATED034,
  __pyx_e_5spacy_7symbols_DEPRECATED035,
  __pyx_e_5spacy_7symbols_DEPRECATED036,
  __pyx_e_5spacy_7symbols_DEPRECATED037,
  __pyx_e_5spacy_7symbols_DEPRECATED038,
  __pyx_e_5spacy_7symbols_DEPRECATED039,
  __pyx_e_5spacy_7symbols_DEPRECATED040,
  __pyx_e_5spacy_7symbols_DEPRECATED041,
  __pyx_e_5spacy_7symbols_DEPRECATED042,
  __pyx_e_5spacy_7symbols_DEPRECATED043,
  __pyx_e_5spacy_7symbols_DEPRECATED044,
  __pyx_e_5spacy_7symbols_DEPRECATED045,
  __pyx_e_5spacy_7symbols_DEPRECATED046,
  __pyx_e_5spacy_7symbols_DEPRECATED047,
  __pyx_e_5spacy_7symbols_DEPRECATED048,
  __pyx_e_5spacy_7symbols_DEPRECATED049,
  __pyx_e_5spacy_7symbols_DEPRECATED050,
  __pyx_e_5spacy_7symbols_DEPRECATED051,
  __pyx_e_5spacy_7symbols_DEPRECATED052,
  __pyx_e_5spacy_7symbols_DEPRECATED053,
  __pyx_e_5spacy_7symbols_DEPRECATED054,
  __pyx_e_5spacy_7symbols_DEPRECATED055,
  __pyx_e_5spacy_7symbols_DEPRECATED056,
  __pyx_e_5spacy_7symbols_DEPRECATED057,
  __pyx_e_5spacy_7symbols_DEPRECATED058,
  __pyx_e_5spacy_7symbols_DEPRECATED059,
  __pyx_e_5spacy_7symbols_DEPRECATED060,
  __pyx_e_5spacy_7symbols_DEPRECATED061,
  __pyx_e_5spacy_7symbols_DEPRECATED062,
  __pyx_e_5spacy_7symbols_DEPRECATED063,
  __pyx_e_5spacy_7symbols_DEPRECATED064,
  __pyx_e_5spacy_7symbols_DEPRECATED065,
  __pyx_e_5spacy_7symbols_DEPRECATED066,
  __pyx_e_5spacy_7symbols_DEPRECATED067,
  __pyx_e_5spacy_7symbols_DEPRECATED068,
  __pyx_e_5spacy_7symbols_DEPRECATED069,
  __pyx_e_5spacy_7symbols_DEPRECATED070,
  __pyx_e_5spacy_7symbols_DEPRECATED071,
  __pyx_e_5spacy_7symbols_DEPRECATED072,
  __pyx_e_5spacy_7symbols_DEPRECATED073,
  __pyx_e_5spacy_7symbols_DEPRECATED074,
  __pyx_e_5spacy_7symbols_DEPRECATED075,
  __pyx_e_5spacy_7symbols_DEPRECATED076,
  __pyx_e_5spacy_7symbols_DEPRECATED077,
  __pyx_e_5spacy_7symbols_DEPRECATED078,
  __pyx_e_5spacy_7symbols_DEPRECATED079,
  __pyx_e_5spacy_7symbols_DEPRECATED080,
  __pyx_e_5spacy_7symbols_DEPRECATED081,
  __pyx_e_5spacy_7symbols_DEPRECATED082,
  __pyx_e_5spacy_7symbols_DEPRECATED083,
  __pyx_e_5spacy_7symbols_DEPRECATED084,
  __pyx_e_5spacy_7symbols_DEPRECATED085,
  __pyx_e_5spacy_7symbols_DEPRECATED086,
  __pyx_e_5spacy_7symbols_DEPRECATED087,
  __pyx_e_5spacy_7symbols_DEPRECATED088,
  __pyx_e_5spacy_7symbols_DEPRECATED089,
  __pyx_e_5spacy_7symbols_DEPRECATED090,
  __pyx_e_5spacy_7symbols_DEPRECATED091,
  __pyx_e_5spacy_7symbols_DEPRECATED092,
  __pyx_e_5spacy_7symbols_DEPRECATED093,
  __pyx_e_5spacy_7symbols_DEPRECATED094,
  __pyx_e_5spacy_7symbols_DEPRECATED095,
  __pyx_e_5spacy_7symbols_DEPRECATED096,
  __pyx_e_5spacy_7symbols_DEPRECATED097,
  __pyx_e_5spacy_7symbols_DEPRECATED098,
  __pyx_e_5spacy_7symbols_DEPRECATED099,
  __pyx_e_5spacy_7symbols_DEPRECATED100,
  __pyx_e_5spacy_7symbols_DEPRECATED101,
  __pyx_e_5spacy_7symbols_DEPRECATED102,
  __pyx_e_5spacy_7symbols_DEPRECATED103,
  __pyx_e_5spacy_7symbols_DEPRECATED104,
  __pyx_e_5spacy_7symbols_DEPRECATED105,
  __pyx_e_5spacy_7symbols_DEPRECATED106,
  __pyx_e_5spacy_7symbols_DEPRECATED107,
  __pyx_e_5spacy_7symbols_DEPRECATED108,
  __pyx_e_5spacy_7symbols_DEPRECATED109,
  __pyx_e_5spacy_7symbols_DEPRECATED110,
  __pyx_e_5spacy_7symbols_DEPRECATED111,
  __pyx_e_5spacy_7symbols_DEPRECATED112,
  __pyx_e_5spacy_7symbols_DEPRECATED113,
  __pyx_e_5spacy_7symbols_DEPRECATED114,
  __pyx_e_5spacy_7symbols_DEPRECATED115,
  __pyx_e_5spacy_7symbols_DEPRECATED116,
  __pyx_e_5spacy_7symbols_DEPRECATED117,
  __pyx_e_5spacy_7symbols_DEPRECATED118,
  __pyx_e_5spacy_7symbols_DEPRECATED119,
  __pyx_e_5spacy_7symbols_DEPRECATED120,
  __pyx_e_5spacy_7symbols_DEPRECATED121,
  __pyx_e_5spacy_7symbols_DEPRECATED122,
  __pyx_e_5spacy_7symbols_DEPRECATED123,
  __pyx_e_5spacy_7symbols_DEPRECATED124,
  __pyx_e_5spacy_7symbols_DEPRECATED125,
  __pyx_e_5spacy_7symbols_DEPRECATED126,
  __pyx_e_5spacy_7symbols_DEPRECATED127,
  __pyx_e_5spacy_7symbols_DEPRECATED128,
  __pyx_e_5spacy_7symbols_DEPRECATED129,
  __pyx_e_5spacy_7symbols_DEPRECATED130,
  __pyx_e_5spacy_7symbols_DEPRECATED131,
  __pyx_e_5spacy_7symbols_DEPRECATED132,
  __pyx_e_5spacy_7symbols_DEPRECATED133,
  __pyx_e_5spacy_7symbols_DEPRECATED134,
  __pyx_e_5spacy_7symbols_DEPRECATED135,
  __pyx_e_5spacy_7symbols_DEPRECATED136,
  __pyx_e_5spacy_7symbols_DEPRECATED137,
  __pyx_e_5spacy_7symbols_DEPRECATED138,
  __pyx_e_5spacy_7symbols_DEPRECATED139,
  __pyx_e_5spacy_7symbols_DEPRECATED140,
  __pyx_e_5spacy_7symbols_DEPRECATED141,
  __pyx_e_5spacy_7symbols_DEPRECATED142,
  __pyx_e_5spacy_7symbols_DEPRECATED143,
  __pyx_e_5spacy_7symbols_DEPRECATED144,
  __pyx_e_5spacy_7symbols_DEPRECATED145,
  __pyx_e_5spacy_7symbols_DEPRECATED146,
  __pyx_e_5spacy_7symbols_DEPRECATED147,
  __pyx_e_5spacy_7symbols_DEPRECATED148,
  __pyx_e_5spacy_7symbols_DEPRECATED149,
  __pyx_e_5spacy_7symbols_DEPRECATED150,
  __pyx_e_5spacy_7symbols_DEPRECATED151,
  __pyx_e_5spacy_7symbols_DEPRECATED152,
  __pyx_e_5spacy_7symbols_DEPRECATED153,
  __pyx_e_5spacy_7symbols_DEPRECATED154,
  __pyx_e_5spacy_7symbols_DEPRECATED155,
  __pyx_e_5spacy_7symbols_DEPRECATED156,
  __pyx_e_5spacy_7symbols_DEPRECATED157,
  __pyx_e_5spacy_7symbols_DEPRECATED158,
  __pyx_e_5spacy_7symbols_DEPRECATED159,
  __pyx_e_5spacy_7symbols_DEPRECATED160,
  __pyx_e_5spacy_7symbols_DEPRECATED161,
  __pyx_e_5spacy_7symbols_DEPRECATED162,
  __pyx_e_5spacy_7symbols_DEPRECATED163,
  __pyx_e_5spacy_7symbols_DEPRECATED164,
  __pyx_e_5spacy_7symbols_DEPRECATED165,
  __pyx_e_5spacy_7symbols_DEPRECATED166,
  __pyx_e_5spacy_7symbols_DEPRECATED167,
  __pyx_e_5spacy_7symbols_DEPRECATED168,
  __pyx_e_5spacy_7symbols_DEPRECATED169,
  __pyx_e_5spacy_7symbols_DEPRECATED170,
  __pyx_e_5spacy_7symbols_DEPRECATED171,
  __pyx_e_5spacy_7symbols_DEPRECATED172,
  __pyx_e_5spacy_7symbols_DEPRECATED173,
  __pyx_e_5spacy_7symbols_DEPRECATED174,
  __pyx_e_5spacy_7symbols_DEPRECATED175,
  __pyx_e_5spacy_7symbols_DEPRECATED176,
  __pyx_e_5spacy_7symbols_DEPRECATED177,
  __pyx_e_5spacy_7symbols_DEPRECATED178,
  __pyx_e_5spacy_7symbols_DEPRECATED179,
  __pyx_e_5spacy_7symbols_DEPRECATED180,
  __pyx_e_5spacy_7symbols_DEPRECATED181,
  __pyx_e_5spacy_7symbols_DEPRECATED182,
  __pyx_e_5spacy_7symbols_DEPRECATED183,
  __pyx_e_5spacy_7symbols_DEPRECATED184,
  __pyx_e_5spacy_7symbols_DEPRECATED185,
  __pyx_e_5spacy_7symbols_DEPRECATED186,
  __pyx_e_5spacy_7symbols_DEPRECATED187,
  __pyx_e_5spacy_7symbols_DEPRECATED188,
  __pyx_e_5spacy_7symbols_DEPRECATED189,
  __pyx_e_5spacy_7symbols_DEPRECATED190,
  __pyx_e_5spacy_7symbols_DEPRECATED191,
  __pyx_e_5spacy_7symbols_DEPRECATED192,
  __pyx_e_5spacy_7symbols_DEPRECATED193,
  __pyx_e_5spacy_7symbols_DEPRECATED194,
  __pyx_e_5spacy_7symbols_DEPRECATED195,
  __pyx_e_5spacy_7symbols_DEPRECATED196,
  __pyx_e_5spacy_7symbols_DEPRECATED197,
  __pyx_e_5spacy_7symbols_DEPRECATED198,
  __pyx_e_5spacy_7symbols_DEPRECATED199,
  __pyx_e_5spacy_7symbols_DEPRECATED200,
  __pyx_e_5spacy_7symbols_DEPRECATED201,
  __pyx_e_5spacy_7symbols_DEPRECATED202,
  __pyx_e_5spacy_7symbols_DEPRECATED203,
  __pyx_e_5spacy_7symbols_DEPRECATED204,
  __pyx_e_5spacy_7symbols_DEPRECATED205,
  __pyx_e_5spacy_7symbols_DEPRECATED206,
  __pyx_e_5spacy_7symbols_DEPRECATED207,
  __pyx_e_5spacy_7symbols_DEPRECATED208,
  __pyx_e_5spacy_7symbols_DEPRECATED209,
  __pyx_e_5spacy_7symbols_DEPRECATED210,
  __pyx_e_5spacy_7symbols_DEPRECATED211,
  __pyx_e_5spacy_7symbols_DEPRECATED212,
  __pyx_e_5spacy_7symbols_DEPRECATED213,
  __pyx_e_5spacy_7symbols_DEPRECATED214,
  __pyx_e_5spacy_7symbols_DEPRECATED215,
  __pyx_e_5spacy_7symbols_DEPRECATED216,
  __pyx_e_5spacy_7symbols_DEPRECATED217,
  __pyx_e_5spacy_7symbols_DEPRECATED218,
  __pyx_e_5spacy_7symbols_DEPRECATED219,
  __pyx_e_5spacy_7symbols_DEPRECATED220,
  __pyx_e_5spacy_7symbols_DEPRECATED221,
  __pyx_e_5spacy_7symbols_DEPRECATED222,
  __pyx_e_5spacy_7symbols_DEPRECATED223,
  __pyx_e_5spacy_7symbols_DEPRECATED224,
  __pyx_e_5spacy_7symbols_DEPRECATED225,
  __pyx_e_5spacy_7symbols_DEPRECATED226,
  __pyx_e_5spacy_7symbols_DEPRECATED227,
  __pyx_e_5spacy_7symbols_DEPRECATED228,
  __pyx_e_5spacy_7symbols_DEPRECATED229,
  __pyx_e_5spacy_7symbols_DEPRECATED230,
  __pyx_e_5spacy_7symbols_DEPRECATED231,
  __pyx_e_5spacy_7symbols_DEPRECATED232,
  __pyx_e_5spacy_7symbols_DEPRECATED233,
  __pyx_e_5spacy_7symbols_DEPRECATED234,
  __pyx_e_5spacy_7symbols_DEPRECATED235,
  __pyx_e_5spacy_7symbols_DEPRECATED236,
  __pyx_e_5spacy_7symbols_DEPRECATED237,
  __pyx_e_5spacy_7symbols_DEPRECATED238,
  __pyx_e_5spacy_7symbols_DEPRECATED239,
  __pyx_e_5spacy_7symbols_DEPRECATED240,
  __pyx_e_5spacy_7symbols_DEPRECATED241,
  __pyx_e_5spacy_7symbols_DEPRECATED242,
  __pyx_e_5spacy_7symbols_DEPRECATED243,
  __pyx_e_5spacy_7symbols_DEPRECATED244,
  __pyx_e_5spacy_7symbols_DEPRECATED245,
  __pyx_e_5spacy_7symbols_DEPRECATED246,
  __pyx_e_5spacy_7symbols_DEPRECATED247,
  __pyx_e_5spacy_7symbols_DEPRECATED248,
  __pyx_e_5spacy_7symbols_DEPRECATED249,
  __pyx_e_5spacy_7symbols_DEPRECATED250,
  __pyx_e_5spacy_7symbols_DEPRECATED251,
  __pyx_e_5spacy_7symbols_DEPRECATED252,
  __pyx_e_5spacy_7symbols_DEPRECATED253,
  __pyx_e_5spacy_7symbols_DEPRECATED254,
  __pyx_e_5spacy_7symbols_DEPRECATED255,
  __pyx_e_5spacy_7symbols_DEPRECATED256,
  __pyx_e_5spacy_7symbols_DEPRECATED257,
  __pyx_e_5spacy_7symbols_DEPRECATED258,
  __pyx_e_5spacy_7symbols_DEPRECATED259,
  __pyx_e_5spacy_7symbols_DEPRECATED260,
  __pyx_e_5spacy_7symbols_DEPRECATED261,
  __pyx_e_5spacy_7symbols_DEPRECATED262,
  __pyx_e_5spacy_7symbols_DEPRECATED263,
  __pyx_e_5spacy_7symbols_DEPRECATED264,
  __pyx_e_5spacy_7symbols_DEPRECATED265,
  __pyx_e_5spacy_7symbols_DEPRECATED266,
  __pyx_e_5spacy_7symbols_DEPRECATED267,
  __pyx_e_5spacy_7symbols_DEPRECATED268,
  __pyx_e_5spacy_7symbols_DEPRECATED269,
  __pyx_e_5spacy_7symbols_DEPRECATED270,
  __pyx_e_5spacy_7symbols_DEPRECATED271,
  __pyx_e_5spacy_7symbols_DEPRECATED272,
  __pyx_e_5spacy_7symbols_DEPRECATED273,
  __pyx_e_5spacy_7symbols_DEPRECATED274,
  __pyx_e_5spacy_7symbols_DEPRECATED275,
  __pyx_e_5spacy_7symbols_DEPRECATED276,
  __pyx_e_5spacy_7symbols_PERSON,
  __pyx_e_5spacy_7symbols_NORP,
  __pyx_e_5spacy_7symbols_FACILITY,
  __pyx_e_5spacy_7symbols_ORG,
  __pyx_e_5spacy_7symbols_GPE,
  __pyx_e_5spacy_7symbols_LOC,
  __pyx_e_5spacy_7symbols_PRODUCT,
  __pyx_e_5spacy_7symbols_EVENT,
  __pyx_e_5spacy_7symbols_WORK_OF_ART,
  __pyx_e_5spacy_7symbols_LANGUAGE,
  __pyx_e_5spacy_7symbols_LAW,
  __pyx_e_5spacy_7symbols_DATE,
  __pyx_e_5spacy_7symbols_TIME,
  __pyx_e_5spacy_7symbols_PERCENT,
  __pyx_e_5spacy_7symbols_MONEY,
  __pyx_e_5spacy_7symbols_QUANTITY,
  __pyx_e_5spacy_7symbols_ORDINAL,
  __pyx_e_5spacy_7symbols_CARDINAL,
  __pyx_e_5spacy_7symbols_acomp,
  __pyx_e_5spacy_7symbols_advcl,
  __pyx_e_5spacy_7symbols_advmod,
  __pyx_e_5spacy_7symbols_agent,
  __pyx_e_5spacy_7symbols_amod,
  __pyx_e_5spacy_7symbols_appos,
  __pyx_e_5spacy_7symbols_attr,
  __pyx_e_5spacy_7symbols_aux,
  __pyx_e_5spacy_7symbols_auxpass,
  __pyx_e_5spacy_7symbols_cc,
  __pyx_e_5spacy_7symbols_ccomp,
  __pyx_e_5spacy_7symbols_complm,
  __pyx_e_5spacy_7symbols_conj,
  __pyx_e_5spacy_7symbols_cop,
  __pyx_e_5spacy_7symbols_csubj,
  __pyx_e_5spacy_7symbols_csubjpass,
  __pyx_e_5spacy_7symbols_dep,
  __pyx_e_5spacy_7symbols_det,
  __pyx_e_5spacy_7symbols_dobj,
  __pyx_e_5spacy_7symbols_expl,
  __pyx_e_5spacy_7symbols_hmod,
  __pyx_e_5spacy_7symbols_hyph,
  __pyx_e_5spacy_7symbols_infmod,
  __pyx_e_5spacy_7symbols_intj,
  __pyx_e_5spacy_7symbols_iobj,
  __pyx_e_5spacy_7symbols_mark,
  __pyx_e_5spacy_7symbols_meta,
  __pyx_e_5spacy_7symbols_neg,
  __pyx_e_5spacy_7symbols_nmod,
  __pyx_e_5spacy_7symbols_nn,
  __pyx_e_5spacy_7symbols_npadvmod,
  __pyx_e_5spacy_7symbols_nsubj,
  __pyx_e_5spacy_7symbols_nsubjpass,
  __pyx_e_5spacy_7symbols_num,
  __pyx_e_5spacy_7symbols_number,
  __pyx_e_5spacy_7symbols_oprd,
  __pyx_e_5spacy_7symbols_obj,
  __pyx_e_5spacy_7symbols_obl,
  __pyx_e_5spacy_7symbols_parataxis,
  __pyx_e_5spacy_7symbols_partmod,
  __pyx_e_5spacy_7symbols_pcomp,
  __pyx_e_5spacy_7symbols_pobj,
  __pyx_e_5spacy_7symbols_poss,
  __pyx_e_5spacy_7symbols_possessive,
  __pyx_e_5spacy_7symbols_preconj,
  __pyx_e_5spacy_7symbols_prep,
  __pyx_e_5spacy_7symbols_prt,
  __pyx_e_5spacy_7symbols_punct,
  __pyx_e_5spacy_7symbols_quantmod,
  __pyx_e_5spacy_7symbols_relcl,
  __pyx_e_5spacy_7symbols_rcmod,
  __pyx_e_5spacy_7symbols_root,
  __pyx_e_5spacy_7symbols_xcomp,
  __pyx_e_5spacy_7symbols_acl,
  __pyx_e_5spacy_7symbols_ENT_KB_ID,
  __pyx_e_5spacy_7symbols_MORPH,
  __pyx_e_5spacy_7symbols_ENT_ID,
  __pyx_e_5spacy_7symbols_IDX,
  __pyx_e_5spacy_7symbols__
};

/* "spacy/attrs.pxd":4
 * from . cimport symbols
 * 
 * cdef enum attr_id_t:             # <<<<<<<<<<<<<<
 *     NULL_ATTR
 *     IS_ALPHA
 */
enum __pyx_t_5spacy_5attrs_attr_id_t {

  /* "spacy/attrs.pxd":95
 *     ENT_KB_ID = symbols.ENT_KB_ID
 *     MORPH
 *     ENT_ID = symbols.ENT_ID             # <<<<<<<<<<<<<<
 * 
 *     IDX
 */
  __pyx_e_5spacy_5attrs_NULL_ATTR,
  __pyx_e_5spacy_5attrs_IS_ALPHA,
  __pyx_e_5spacy_5attrs_IS_ASCII,
  __pyx_e_5spacy_5attrs_IS_DIGIT,
  __pyx_e_5spacy_5attrs_IS_LOWER,
  __pyx_e_5spacy_5attrs_IS_PUNCT,
  __pyx_e_5spacy_5attrs_IS_SPACE,
  __pyx_e_5spacy_5attrs_IS_TITLE,
  __pyx_e_5spacy_5attrs_IS_UPPER,
  __pyx_e_5spacy_5attrs_LIKE_URL,
  __pyx_e_5spacy_5attrs_LIKE_NUM,
  __pyx_e_5spacy_5attrs_LIKE_EMAIL,
  __pyx_e_5spacy_5attrs_IS_STOP,
  __pyx_e_5spacy_5attrs_IS_OOV_DEPRECATED,
  __pyx_e_5spacy_5attrs_IS_BRACKET,
  __pyx_e_5spacy_5attrs_IS_QUOTE,
  __pyx_e_5spacy_5attrs_IS_LEFT_PUNCT,
  __pyx_e_5spacy_5attrs_IS_RIGHT_PUNCT,
  __pyx_e_5spacy_5attrs_IS_CURRENCY,
  __pyx_e_5spacy_5attrs_FLAG19 = 19,
  __pyx_e_5spacy_5attrs_FLAG20,
  __pyx_e_5spacy_5attrs_FLAG21,
  __pyx_e_5spacy_5attrs_FLAG22,
  __pyx_e_5spacy_5attrs_FLAG23,
  __pyx_e_5spacy_5attrs_FLAG24,
  __pyx_e_5spacy_5attrs_FLAG25,
  __pyx_e_5spacy_5attrs_FLAG26,
  __pyx_e_5spacy_5attrs_FLAG27,
  __pyx_e_5spacy_5attrs_FLAG28,
  __pyx_e_5spacy_5attrs_FLAG29,
  __pyx_e_5spacy_5attrs_FLAG30,
  __pyx_e_5spacy_5attrs_FLAG31,
  __pyx_e_5spacy_5attrs_FLAG32,
  __pyx_e_5spacy_5attrs_FLAG33,
  __pyx_e_5spacy_5attrs_FLAG34,
  __pyx_e_5spacy_5attrs_FLAG35,
  __pyx_e_5spacy_5attrs_FLAG36,
  __pyx_e_5spacy_5attrs_FLAG37,
  __pyx_e_5spacy_5attrs_FLAG38,
  __pyx_e_5spacy_5attrs_FLAG39,
  __pyx_e_5spacy_5attrs_FLAG40,
  __pyx_e_5spacy_5attrs_FLAG41,
  __pyx_e_5spacy_5attrs_FLAG42,
  __pyx_e_5spacy_5attrs_FLAG43,
  __pyx_e_5spacy_5attrs_FLAG44,
  __pyx_e_5spacy_5attrs_FLAG45,
  __pyx_e_5spacy_5attrs_FLAG46,
  __pyx_e_5spacy_5attrs_FLAG47,
  __pyx_e_5spacy_5attrs_FLAG48,
  __pyx_e_5spacy_5attrs_FLAG49,
  __pyx_e_5spacy_5attrs_FLAG50,
  __pyx_e_5spacy_5attrs_FLAG51,
  __pyx_e_5spacy_5attrs_FLAG52,
  __pyx_e_5spacy_5attrs_FLAG53,
  __pyx_e_5spacy_5attrs_FLAG54,
  __pyx_e_5spacy_5attrs_FLAG55,
  __pyx_e_5spacy_5attrs_FLAG56,
  __pyx_e_5spacy_5attrs_FLAG57,
  __pyx_e_5spacy_5attrs_FLAG58,
  __pyx_e_5spacy_5attrs_FLAG59,
  __pyx_e_5spacy_5attrs_FLAG60,
  __pyx_e_5spacy_5attrs_FLAG61,
  __pyx_e_5spacy_5attrs_FLAG62,
  __pyx_e_5spacy_5attrs_FLAG63,
  __pyx_e_5spacy_5attrs_ID,
  __pyx_e_5spacy_5attrs_ORTH,
  __pyx_e_5spacy_5attrs_LOWER,
  __pyx_e_5spacy_5attrs_NORM,
  __pyx_e_5spacy_5attrs_SHAPE,
  __pyx_e_5spacy_5attrs_PREFIX,
  __pyx_e_5spacy_5attrs_SUFFIX,
  __pyx_e_5spacy_5attrs_LENGTH,
  __pyx_e_5spacy_5attrs_CLUSTER,
  __pyx_e_5spacy_5attrs_LEMMA,
  __pyx_e_5spacy_5attrs_POS,
  __pyx_e_5spacy_5attrs_TAG,
  __pyx_e_5spacy_5attrs_DEP,
  __pyx_e_5spacy_5attrs_ENT_IOB,
  __pyx_e_5spacy_5attrs_ENT_TYPE,
  __pyx_e_5spacy_5attrs_HEAD,
  __pyx_e_5spacy_5attrs_SENT_START,
  __pyx_e_5spacy_5attrs_SPACY,
  __pyx_e_5spacy_5attrs_PROB,
  __pyx_e_5spacy_5attrs_LANG,
  __pyx_e_5spacy_5attrs_ENT_KB_ID = __pyx_e_5spacy_7symbols_ENT_KB_ID,
  __pyx_e_5spacy_5attrs_MORPH,
  __pyx_e_5spacy_5attrs_ENT_ID = __pyx_e_5spacy_7symbols_ENT_ID,
  __pyx_e_5spacy_5attrs_IDX,
  __pyx_e_5spacy_5attrs_SENT_END
};

/* --- Runtime support code (head) --- */
/* Refnanny.proto */
#ifndef CYTHON_REFNANNY
  #define CYTHON_REFNANNY 0
#endif
#if CYTHON_REFNANNY
  typedef struct {
    void (*INCREF)(void*, PyObject*, int);
    void (*DECREF)(void*, PyObject*, int);
    void (*GOTREF)(void*, PyObject*, int);
    void (*GIVEREF)(void*, PyObject*, int);
    void* (*SetupContext)(const char*, int, const char*);
    void (*FinishContext)(void**);
  } __Pyx_RefNannyAPIStruct;
  static __Pyx_RefNannyAPIStruct *__Pyx_RefNanny = NULL;
  static __Pyx_RefNannyAPIStruct *__Pyx_RefNannyImportAPI(const char *modname);
  #define __Pyx_RefNannyDeclarations void *__pyx_refnanny = NULL;
#ifdef WITH_THREAD
  #define __Pyx_RefNannySetupContext(name, acquire_gil)\
          if (acquire_gil) {\
              PyGILState_STATE __pyx_gilstate_save = PyGILState_Ensure();\
              __pyx_refnanny = __Pyx_RefNanny->SetupContext((name), __LINE__, __FILE__);\
              PyGILState_Release(__pyx_gilstate_save);\
          } else {\
              __pyx_refnanny = __Pyx_RefNanny->SetupContext((name), __LINE__, __FILE__);\
          }
#else
  #define __Pyx_RefNannySetupContext(name, acquire_gil)\
          __pyx_refnanny = __Pyx_RefNanny->SetupContext((name), __LINE__, __FILE__)
#endif
  #define __Pyx_RefNannyFinishContext()\
          __Pyx_RefNanny->FinishContext(&__pyx_refnanny)
  #define __Pyx_INCREF(r)  __Pyx_RefNanny->INCREF(__pyx_refnanny, (PyObject *)(r), __LINE__)
  #define __Pyx_DECREF(r)  __Pyx_RefNanny->DECREF(__pyx_refnanny, (PyObject *)(r), __LINE__)
  #define __Pyx_GOTREF(r)  __Pyx_RefNanny->GOTREF(__pyx_refnanny, (PyObject *)(r), __LINE__)
  #define __Pyx_GIVEREF(r) __Pyx_RefNanny->GIVEREF(__pyx_refnanny, (PyObject *)(r), __LINE__)
  #define __Pyx_XINCREF(r)  do { if((r) != NULL) {__Pyx_INCREF(r); }} while(0)
  #define __Pyx_XDECREF(r)  do { if((r) != NULL) {__Pyx_DECREF(r); }} while(0)
  #define __Pyx_XGOTREF(r)  do { if((r) != NULL) {__Pyx_GOTREF(r); }} while(0)
  #define __Pyx_XGIVEREF(r) do { if((r) != NULL) {__Pyx_GIVEREF(r);}} while(0)
#else
  #define __Pyx_RefNannyDeclarations
  #define __Pyx_RefNannySetupContext(name, acquire_gil)
  #define __Pyx_RefNannyFinishContext()
  #define __Pyx_INCREF(r) Py_INCREF(r)
  #define __Pyx_DECREF(r) Py_DECREF(r)
  #define __Pyx_GOTREF(r)
  #define __Pyx_GIVEREF(r)
  #define __Pyx_XINCREF(r) Py_XINCREF(r)
  #define __Pyx_XDECREF(r) Py_XDECREF(r)
  #define __Pyx_XGOTREF(r)
  #define __Pyx_XGIVEREF(r)
#endif
#define __Pyx_XDECREF_SET(r, v) do {\
        PyObject *tmp = (PyObject *) r;\
        r = v; __Pyx_XDECREF(tmp);\
    } while (0)
#define __Pyx_DECREF_SET(r, v) do {\
        PyObject *tmp = (PyObject *) r;\
        r = v; __Pyx_DECREF(tmp);\
    } while (0)
#define __Pyx_CLEAR(r)    do { PyObject* tmp = ((PyObject*)(r)); r = NULL; __Pyx_DECREF(tmp);} while(0)
#define __Pyx_XCLEAR(r)   do { if((r) != NULL) {PyObject* tmp = ((PyObject*)(r)); r = NULL; __Pyx_DECREF(tmp);}} while(0)

/* PyObjectGetAttrStr.proto */
#if CYTHON_USE_TYPE_SLOTS
static CYTHON_INLINE PyObject* __Pyx_PyObject_GetAttrStr(PyObject* obj, PyObject* attr_name);
#else
#define __Pyx_PyObject_GetAttrStr(o,n) PyObject_GetAttr(o,n)
#endif

/* GetBuiltinName.proto */
static PyObject *__Pyx_GetBuiltinName(PyObject *name);

/* GetItemInt.proto */
#define __Pyx_GetItemInt(o, i, type, is_signed, to_py_func, is_list, wraparound, boundscheck)\
    (__Pyx_fits_Py_ssize_t(i, type, is_signed) ?\
    __Pyx_GetItemInt_Fast(o, (Py_ssize_t)i, is_list, wraparound, boundscheck) :\
    (is_list ? (PyErr_SetString(PyExc_IndexError, "list index out of range"), (PyObject*)NULL) :\
               __Pyx_GetItemInt_Generic(o, to_py_func(i))))
#define __Pyx_GetItemInt_List(o, i, type, is_signed, to_py_func, is_list, wraparound, boundscheck)\
    (__Pyx_fits_Py_ssize_t(i, type, is_signed) ?\
    __Pyx_GetItemInt_List_Fast(o, (Py_ssize_t)i, wraparound, boundscheck) :\
    (PyErr_SetString(PyExc_IndexError, "list index out of range"), (PyObject*)NULL))
static CYTHON_INLINE PyObject *__Pyx_GetItemInt_List_Fast(PyObject *o, Py_ssize_t i,
                                                              int wraparound, int boundscheck);
#define __Pyx_GetItemInt_Tuple(o, i, type, is_signed, to_py_func, is_list, wraparound, boundscheck)\
    (__Pyx_fits_Py_ssize_t(i, type, is_signed) ?\
    __Pyx_GetItemInt_Tuple_Fast(o, (Py_ssize_t)i, wraparound, boundscheck) :\
    (PyErr_SetString(PyExc_IndexError, "tuple index out of range"), (PyObject*)NULL))
static CYTHON_INLINE PyObject *__Pyx_GetItemInt_Tuple_Fast(PyObject *o, Py_ssize_t i,
                                                              int wraparound, int boundscheck);
static PyObject *__Pyx_GetItemInt_Generic(PyObject *o, PyObject* j);
static CYTHON_INLINE PyObject *__Pyx_GetItemInt_Fast(PyObject *o, Py_ssize_t i,
                                                     int is_list, int wraparound, int boundscheck);

/* RaiseDoubleKeywords.proto */
static void __Pyx_RaiseDoubleKeywordsError(const char* func_name, PyObject* kw_name);

/* ParseKeywords.proto */
static int __Pyx_ParseOptionalKeywords(PyObject *kwds, PyObject **argnames[],\
    PyObject *kwds2, PyObject *values[], Py_ssize_t num_pos_args,\
    const char* function_name);

/* RaiseArgTupleInvalid.proto */
static void __Pyx_RaiseArgtupleInvalid(const char* func_name, int exact,
    Py_ssize_t num_min, Py_ssize_t num_max, Py_ssize_t num_found);

/* PySequenceContains.proto */
static CYTHON_INLINE int __Pyx_PySequence_ContainsTF(PyObject* item, PyObject* seq, int eq) {
    int result = PySequence_Contains(seq, item);
    return unlikely(result < 0) ? result : (result == (eq == Py_EQ));
}

/* PyCFunctionFastCall.proto */
#if CYTHON_FAST_PYCCALL
static CYTHON_INLINE PyObject *__Pyx_PyCFunction_FastCall(PyObject *func, PyObject **args, Py_ssize_t nargs);
#else
#define __Pyx_PyCFunction_FastCall(func, args, nargs)  (assert(0), NULL)
#endif

/* PyFunctionFastCall.proto */
#if CYTHON_FAST_PYCALL
#define __Pyx_PyFunction_FastCall(func, args, nargs)\
    __Pyx_PyFunction_FastCallDict((func), (args), (nargs), NULL)
#if 1 || PY_VERSION_HEX < 0x030600B1
static PyObject *__Pyx_PyFunction_FastCallDict(PyObject *func, PyObject **args, Py_ssize_t nargs, PyObject *kwargs);
#else
#define __Pyx_PyFunction_FastCallDict(func, args, nargs, kwargs) _PyFunction_FastCallDict(func, args, nargs, kwargs)
#endif
#define __Pyx_BUILD_ASSERT_EXPR(cond)\
    (sizeof(char [1 - 2*!(cond)]) - 1)
#ifndef Py_MEMBER_SIZE
#define Py_MEMBER_SIZE(type, member) sizeof(((type *)0)->member)
#endif
#if CYTHON_FAST_PYCALL
  static size_t __pyx_pyframe_localsplus_offset = 0;
  #include "frameobject.h"
#if PY_VERSION_HEX >= 0x030b00a6
  #ifndef Py_BUILD_CORE
    #define Py_BUILD_CORE 1
  #endif
  #include "internal/pycore_frame.h"
#endif
  #define __Pxy_PyFrame_Initialize_Offsets()\
    ((void)__Pyx_BUILD_ASSERT_EXPR(sizeof(PyFrameObject) == offsetof(PyFrameObject, f_localsplus) + Py_MEMBER_SIZE(PyFrameObject, f_localsplus)),\
     (void)(__pyx_pyframe_localsplus_offset = ((size_t)PyFrame_Type.tp_basicsize) - Py_MEMBER_SIZE(PyFrameObject, f_localsplus)))
  #define __Pyx_PyFrame_GetLocalsplus(frame)\
    (assert(__pyx_pyframe_localsplus_offset), (PyObject **)(((char *)(frame)) + __pyx_pyframe_localsplus_offset))
#endif // CYTHON_FAST_PYCALL
#endif

/* PyObjectCall.proto */
#if CYTHON_COMPILING_IN_CPYTHON
static CYTHON_INLINE PyObject* __Pyx_PyObject_Call(PyObject *func, PyObject *arg, PyObject *kw);
#else
#define __Pyx_PyObject_Call(func, arg, kw) PyObject_Call(func, arg, kw)
#endif

/* PyObjectCall2Args.proto */
static CYTHON_UNUSED PyObject* __Pyx_PyObject_Call2Args(PyObject* function, PyObject* arg1, PyObject* arg2);

/* PyObjectCallMethO.proto */
#if CYTHON_COMPILING_IN_CPYTHON
static CYTHON_INLINE PyObject* __Pyx_PyObject_CallMethO(PyObject *func, PyObject *arg);
#endif

/* PyObjectCallOneArg.proto */
static CYTHON_INLINE PyObject* __Pyx_PyObject_CallOneArg(PyObject *func, PyObject *arg);

/* PyObjectCallNoArg.proto */
#if CYTHON_COMPILING_IN_CPYTHON
static CYTHON_INLINE PyObject* __Pyx_PyObject_CallNoArg(PyObject *func);
#else
#define __Pyx_PyObject_CallNoArg(func) __Pyx_PyObject_Call(func, __pyx_empty_tuple, NULL)
#endif

/* RaiseTooManyValuesToUnpack.proto */
static CYTHON_INLINE void __Pyx_RaiseTooManyValuesError(Py_ssize_t expected);

/* RaiseNeedMoreValuesToUnpack.proto */
static CYTHON_INLINE void __Pyx_RaiseNeedMoreValuesError(Py_ssize_t index);

/* IterFinish.proto */
static CYTHON_INLINE int __Pyx_IterFinish(void);

/* UnpackItemEndCheck.proto */
static int __Pyx_IternextUnpackEndCheck(PyObject *retval, Py_ssize_t expected);

/* PyDictVersioning.proto */
#if CYTHON_USE_DICT_VERSIONS && CYTHON_USE_TYPE_SLOTS
#define __PYX_DICT_VERSION_INIT  ((PY_UINT64_T) -1)
#define __PYX_GET_DICT_VERSION(dict)  (((PyDictObject*)(dict))->ma_version_tag)
#define __PYX_UPDATE_DICT_CACHE(dict, value, cache_var, version_var)\
    (version_var) = __PYX_GET_DICT_VERSION(dict);\
    (cache_var) = (value);
#define __PYX_PY_DICT_LOOKUP_IF_MODIFIED(VAR, DICT, LOOKUP) {\
    static PY_UINT64_T __pyx_dict_version = 0;\
    static PyObject *__pyx_dict_cached_value = NULL;\
    if (likely(__PYX_GET_DICT_VERSION(DICT) == __pyx_dict_version)) {\
        (VAR) = __pyx_dict_cached_value;\
    } else {\
        (VAR) = __pyx_dict_cached_value = (LOOKUP);\
        __pyx_dict_version = __PYX_GET_DICT_VERSION(DICT);\
    }\
}
static CYTHON_INLINE PY_UINT64_T __Pyx_get_tp_dict_version(PyObject *obj);
static CYTHON_INLINE PY_UINT64_T __Pyx_get_object_dict_version(PyObject *obj);
static CYTHON_INLINE int __Pyx_object_dict_version_matches(PyObject* obj, PY_UINT64_T tp_dict_version, PY_UINT64_T obj_dict_version);
#else
#define __PYX_GET_DICT_VERSION(dict)  (0)
#define __PYX_UPDATE_DICT_CACHE(dict, value, cache_var, version_var)
#define __PYX_PY_DICT_LOOKUP_IF_MODIFIED(VAR, DICT, LOOKUP)  (VAR) = (LOOKUP);
#endif

/* GetModuleGlobalName.proto */
#if CYTHON_USE_DICT_VERSIONS
#define __Pyx_GetModuleGlobalName(var, name)  do {\
    static PY_UINT64_T __pyx_dict_version = 0;\
    static PyObject *__pyx_dict_cached_value = NULL;\
    (var) = (likely(__pyx_dict_version == __PYX_GET_DICT_VERSION(__pyx_d))) ?\
        (likely(__pyx_dict_cached_value) ? __Pyx_NewRef(__pyx_dict_cached_value) : __Pyx_GetBuiltinName(name)) :\
        __Pyx__GetModuleGlobalName(name, &__pyx_dict_version, &__pyx_dict_cached_value);\
} while(0)
#define __Pyx_GetModuleGlobalNameUncached(var, name)  do {\
    PY_UINT64_T __pyx_dict_version;\
    PyObject *__pyx_dict_cached_value;\
    (var) = __Pyx__GetModuleGlobalName(name, &__pyx_dict_version, &__pyx_dict_cached_value);\
} while(0)
static PyObject *__Pyx__GetModuleGlobalName(PyObject *name, PY_UINT64_T *dict_version, PyObject **dict_cached_value);
#else
#define __Pyx_GetModuleGlobalName(var, name)  (var) = __Pyx__GetModuleGlobalName(name)
#define __Pyx_GetModuleGlobalNameUncached(var, name)  (var) = __Pyx__GetModuleGlobalName(name)
static CYTHON_INLINE PyObject *__Pyx__GetModuleGlobalName(PyObject *name);
#endif

/* PyThreadStateGet.proto */
#if CYTHON_FAST_THREAD_STATE
#define __Pyx_PyThreadState_declare  PyThreadState *__pyx_tstate;
#define __Pyx_PyThreadState_assign  __pyx_tstate = __Pyx_PyThreadState_Current;
#define __Pyx_PyErr_Occurred()  __pyx_tstate->curexc_type
#else
#define __Pyx_PyThreadState_declare
#define __Pyx_PyThreadState_assign
#define __Pyx_PyErr_Occurred()  PyErr_Occurred()
#endif

/* PyErrFetchRestore.proto */
#if CYTHON_FAST_THREAD_STATE
#define __Pyx_PyErr_Clear() __Pyx_ErrRestore(NULL, NULL, NULL)
#define __Pyx_ErrRestoreWithState(type, value, tb)  __Pyx_ErrRestoreInState(PyThreadState_GET(), type, value, tb)
#define __Pyx_ErrFetchWithState(type, value, tb)    __Pyx_ErrFetchInState(PyThreadState_GET(), type, value, tb)
#define __Pyx_ErrRestore(type, value, tb)  __Pyx_ErrRestoreInState(__pyx_tstate, type, value, tb)
#define __Pyx_ErrFetch(type, value, tb)    __Pyx_ErrFetchInState(__pyx_tstate, type, value, tb)
static CYTHON_INLINE void __Pyx_ErrRestoreInState(PyThreadState *tstate, PyObject *type, PyObject *value, PyObject *tb);
static CYTHON_INLINE void __Pyx_ErrFetchInState(PyThreadState *tstate, PyObject **type, PyObject **value, PyObject **tb);
#if CYTHON_COMPILING_IN_CPYTHON
#define __Pyx_PyErr_SetNone(exc) (Py_INCREF(exc), __Pyx_ErrRestore((exc), NULL, NULL))
#else
#define __Pyx_PyErr_SetNone(exc) PyErr_SetNone(exc)
#endif
#else
#define __Pyx_PyErr_Clear() PyErr_Clear()
#define __Pyx_PyErr_SetNone(exc) PyErr_SetNone(exc)
#define __Pyx_ErrRestoreWithState(type, value, tb)  PyErr_Restore(type, value, tb)
#define __Pyx_ErrFetchWithState(type, value, tb)  PyErr_Fetch(type, value, tb)
#define __Pyx_ErrRestoreInState(tstate, type, value, tb)  PyErr_Restore(type, value, tb)
#define __Pyx_ErrFetchInState(tstate, type, value, tb)  PyErr_Fetch(type, value, tb)
#define __Pyx_ErrRestore(type, value, tb)  PyErr_Restore(type, value, tb)
#define __Pyx_ErrFetch(type, value, tb)  PyErr_Fetch(type, value, tb)
#endif

/* RaiseException.proto */
static void __Pyx_Raise(PyObject *type, PyObject *value, PyObject *tb, PyObject *cause);

/* GetAttr.proto */
static CYTHON_INLINE PyObject *__Pyx_GetAttr(PyObject *, PyObject *);

/* HasAttr.proto */
static CYTHON_INLINE int __Pyx_HasAttr(PyObject *, PyObject *);

/* ObjectGetItem.proto */
#if CYTHON_USE_TYPE_SLOTS
static CYTHON_INLINE PyObject *__Pyx_PyObject_GetItem(PyObject *obj, PyObject* key);
#else
#define __Pyx_PyObject_GetItem(obj, key)  PyObject_GetItem(obj, key)
#endif

/* Import.proto */
static PyObject *__Pyx_Import(PyObject *name, PyObject *from_list, int level);

/* ImportFrom.proto */
static PyObject* __Pyx_ImportFrom(PyObject* module, PyObject* name);

/* FetchCommonType.proto */
static PyTypeObject* __Pyx_FetchCommonType(PyTypeObject* type);

/* CythonFunctionShared.proto */
#define __Pyx_CyFunction_USED 1
#define __Pyx_CYFUNCTION_STATICMETHOD  0x01
#define __Pyx_CYFUNCTION_CLASSMETHOD   0x02
#define __Pyx_CYFUNCTION_CCLASS        0x04
#define __Pyx_CyFunction_GetClosure(f)\
    (((__pyx_CyFunctionObject *) (f))->func_closure)
#define __Pyx_CyFunction_GetClassObj(f)\
    (((__pyx_CyFunctionObject *) (f))->func_classobj)
#define __Pyx_CyFunction_Defaults(type, f)\
    ((type *)(((__pyx_CyFunctionObject *) (f))->defaults))
#define __Pyx_CyFunction_SetDefaultsGetter(f, g)\
    ((__pyx_CyFunctionObject *) (f))->defaults_getter = (g)
typedef struct {
    PyCFunctionObject func;
#if PY_VERSION_HEX < 0x030500A0
    PyObject *func_weakreflist;
#endif
    PyObject *func_dict;
    PyObject *func_name;
    PyObject *func_qualname;
    PyObject *func_doc;
    PyObject *func_globals;
    PyObject *func_code;
    PyObject *func_closure;
    PyObject *func_classobj;
    void *defaults;
    int defaults_pyobjects;
    size_t defaults_size;  // used by FusedFunction for copying defaults
    int flags;
    PyObject *defaults_tuple;
    PyObject *defaults_kwdict;
    PyObject *(*defaults_getter)(PyObject *);
    PyObject *func_annotations;
} __pyx_CyFunctionObject;
static PyTypeObject *__pyx_CyFunctionType = 0;
#define __Pyx_CyFunction_Check(obj)  (__Pyx_TypeCheck(obj, __pyx_CyFunctionType))
static PyObject *__Pyx_CyFunction_Init(__pyx_CyFunctionObject* op, PyMethodDef *ml,
                                      int flags, PyObject* qualname,
                                      PyObject *self,
                                      PyObject *module, PyObject *globals,
                                      PyObject* code);
static CYTHON_INLINE void *__Pyx_CyFunction_InitDefaults(PyObject *m,
                                                         size_t size,
                                                         int pyobjects);
static CYTHON_INLINE void __Pyx_CyFunction_SetDefaultsTuple(PyObject *m,
                                                            PyObject *tuple);
static CYTHON_INLINE void __Pyx_CyFunction_SetDefaultsKwDict(PyObject *m,
                                                             PyObject *dict);
static CYTHON_INLINE void __Pyx_CyFunction_SetAnnotationsDict(PyObject *m,
                                                              PyObject *dict);
static int __pyx_CyFunction_init(void);

/* CythonFunction.proto */
static PyObject *__Pyx_CyFunction_New(PyMethodDef *ml,
                                      int flags, PyObject* qualname,
                                      PyObject *closure,
                                      PyObject *module, PyObject *globals,
                                      PyObject* code);

/* ListCompAppend.proto */
#if CYTHON_USE_PYLIST_INTERNALS && CYTHON_ASSUME_SAFE_MACROS
static CYTHON_INLINE int __Pyx_ListComp_Append(PyObject* list, PyObject* x) {
    PyListObject* L = (PyListObject*) list;
    Py_ssize_t len = Py_SIZE(list);
    if (likely(L->allocated > len)) {
        Py_INCREF(x);
        PyList_SET_ITEM(list, len, x);
        __Pyx_SET_SIZE(list, len + 1);
        return 0;
    }
    return PyList_Append(list, x);
}
#else
#define __Pyx_ListComp_Append(L,x) PyList_Append(L,x)
#endif

/* CLineInTraceback.proto */
#ifdef CYTHON_CLINE_IN_TRACEBACK
#define __Pyx_CLineForTraceback(tstate, c_line)  (((CYTHON_CLINE_IN_TRACEBACK)) ? c_line : 0)
#else
static int __Pyx_CLineForTraceback(PyThreadState *tstate, int c_line);
#endif

/* CodeObjectCache.proto */
typedef struct {
    PyCodeObject* code_object;
    int code_line;
} __Pyx_CodeObjectCacheEntry;
struct __Pyx_CodeObjectCache {
    int count;
    int max_count;
    __Pyx_CodeObjectCacheEntry* entries;
};
static struct __Pyx_CodeObjectCache __pyx_code_cache = {0,0,NULL};
static int __pyx_bisect_code_objects(__Pyx_CodeObjectCacheEntry* entries, int count, int code_line);
static PyCodeObject *__pyx_find_code_object(int code_line);
static void __pyx_insert_code_object(int code_line, PyCodeObject* code_object);

/* AddTraceback.proto */
static void __Pyx_AddTraceback(const char *funcname, int c_line,
                               int py_line, const char *filename);

/* GCCDiagnostics.proto */
#if defined(__GNUC__) && (__GNUC__ > 4 || (__GNUC__ == 4 && __GNUC_MINOR__ >= 6))
#define __Pyx_HAS_GCC_DIAGNOSTIC
#endif

/* CIntToPy.proto */
static CYTHON_INLINE PyObject* __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(enum __pyx_t_5spacy_5attrs_attr_id_t value);

/* CIntToPy.proto */
static CYTHON_INLINE PyObject* __Pyx_PyInt_From_long(long value);

/* Globals.proto */
static PyObject* __Pyx_Globals(void);

/* CIntFromPy.proto */
static CYTHON_INLINE long __Pyx_PyInt_As_long(PyObject *);

/* CIntFromPy.proto */
static CYTHON_INLINE int __Pyx_PyInt_As_int(PyObject *);

/* FastTypeChecks.proto */
#if CYTHON_COMPILING_IN_CPYTHON
#define __Pyx_TypeCheck(obj, type) __Pyx_IsSubtype(Py_TYPE(obj), (PyTypeObject *)type)
static CYTHON_INLINE int __Pyx_IsSubtype(PyTypeObject *a, PyTypeObject *b);
static CYTHON_INLINE int __Pyx_PyErr_GivenExceptionMatches(PyObject *err, PyObject *type);
static CYTHON_INLINE int __Pyx_PyErr_GivenExceptionMatches2(PyObject *err, PyObject *type1, PyObject *type2);
#else
#define __Pyx_TypeCheck(obj, type) PyObject_TypeCheck(obj, (PyTypeObject *)type)
#define __Pyx_PyErr_GivenExceptionMatches(err, type) PyErr_GivenExceptionMatches(err, type)
#define __Pyx_PyErr_GivenExceptionMatches2(err, type1, type2) (PyErr_GivenExceptionMatches(err, type1) || PyErr_GivenExceptionMatches(err, type2))
#endif
#define __Pyx_PyException_Check(obj) __Pyx_TypeCheck(obj, PyExc_Exception)

/* CheckBinaryVersion.proto */
static int __Pyx_check_binary_version(void);

/* InitStrings.proto */
static int __Pyx_InitStrings(__Pyx_StringTabEntry *t);


/* Module declarations from 'spacy' */

/* Module declarations from 'spacy.symbols' */

/* Module declarations from 'spacy.attrs' */
#define __Pyx_MODULE_NAME "spacy.attrs"
extern int __pyx_module_is_main_spacy__attrs;
int __pyx_module_is_main_spacy__attrs = 0;

/* Implementation of 'spacy.attrs' */
static PyObject *__pyx_builtin_sorted;
static PyObject *__pyx_builtin_ValueError;
static const char __pyx_k_[] = "";
static const char __pyx_k_B[] = "B";
static const char __pyx_k_F[] = "F";
static const char __pyx_k_I[] = "I";
static const char __pyx_k_L[] = "L";
static const char __pyx_k_O[] = "O";
static const char __pyx_k_ID[] = "ID";
static const char __pyx_k_DEP[] = "DEP";
static const char __pyx_k_IDS[] = "IDS";
static const char __pyx_k_IDX[] = "IDX";
static const char __pyx_k_POS[] = "POS";
static const char __pyx_k_TAG[] = "TAG";
static const char __pyx_k_add[] = "add";
static const char __pyx_k_key[] = "key";
static const char __pyx_k_pop[] = "pop";
static const char __pyx_k_pos[] = "pos";
static const char __pyx_k_Abbr[] = "Abbr";
static const char __pyx_k_Case[] = "Case";
static const char __pyx_k_Echo[] = "Echo";
static const char __pyx_k_HEAD[] = "HEAD";
static const char __pyx_k_Hyph[] = "Hyph";
static const char __pyx_k_LANG[] = "LANG";
static const char __pyx_k_Mood[] = "Mood";
static const char __pyx_k_NORM[] = "NORM";
static const char __pyx_k_ORTH[] = "ORTH";
static const char __pyx_k_Poss[] = "Poss";
static const char __pyx_k_main[] = "__main__";
static const char __pyx_k_name[] = "__name__";
static const char __pyx_k_test[] = "__test__";
static const char __pyx_k_E1025[] = "E1025";
static const char __pyx_k_LEMMA[] = "LEMMA";
static const char __pyx_k_LOWER[] = "LOWER";
static const char __pyx_k_MORPH[] = "MORPH";
static const char __pyx_k_NAMES[] = "NAMES";
static const char __pyx_k_Other[] = "Other";
static const char __pyx_k_SHAPE[] = "SHAPE";
static const char __pyx_k_SPACY[] = "SPACY";
static const char __pyx_k_Tense[] = "Tense";
static const char __pyx_k_Voice[] = "Voice";
static const char __pyx_k_index[] = "index";
static const char __pyx_k_items[] = "items";
static const char __pyx_k_lower[] = "lower";
static const char __pyx_k_morph[] = "morph";
static const char __pyx_k_upper[] = "upper";
static const char __pyx_k_value[] = "value";
static const char __pyx_k_Aspect[] = "Aspect";
static const char __pyx_k_Degree[] = "Degree";
static const char __pyx_k_ENT_ID[] = "ENT_ID";
static const char __pyx_k_Errors[] = "Errors";
static const char __pyx_k_FLAG19[] = "FLAG19";
static const char __pyx_k_FLAG20[] = "FLAG20";
static const char __pyx_k_FLAG21[] = "FLAG21";
static const char __pyx_k_FLAG22[] = "FLAG22";
static const char __pyx_k_FLAG23[] = "FLAG23";
static const char __pyx_k_FLAG24[] = "FLAG24";
static const char __pyx_k_FLAG25[] = "FLAG25";
static const char __pyx_k_FLAG26[] = "FLAG26";
static const char __pyx_k_FLAG27[] = "FLAG27";
static const char __pyx_k_FLAG28[] = "FLAG28";
static const char __pyx_k_FLAG29[] = "FLAG29";
static const char __pyx_k_FLAG30[] = "FLAG30";
static const char __pyx_k_FLAG31[] = "FLAG31";
static const char __pyx_k_FLAG32[] = "FLAG32";
static const char __pyx_k_FLAG33[] = "FLAG33";
static const char __pyx_k_FLAG34[] = "FLAG34";
static const char __pyx_k_FLAG35[] = "FLAG35";
static const char __pyx_k_FLAG36[] = "FLAG36";
static const char __pyx_k_FLAG37[] = "FLAG37";
static const char __pyx_k_FLAG38[] = "FLAG38";
static const char __pyx_k_FLAG39[] = "FLAG39";
static const char __pyx_k_FLAG40[] = "FLAG40";
static const char __pyx_k_FLAG41[] = "FLAG41";
static const char __pyx_k_FLAG42[] = "FLAG42";
static const char __pyx_k_FLAG43[] = "FLAG43";
static const char __pyx_k_FLAG44[] = "FLAG44";
static const char __pyx_k_FLAG45[] = "FLAG45";
static const char __pyx_k_FLAG46[] = "FLAG46";
static const char __pyx_k_FLAG47[] = "FLAG47";
static const char __pyx_k_FLAG48[] = "FLAG48";
static const char __pyx_k_FLAG49[] = "FLAG49";
static const char __pyx_k_FLAG50[] = "FLAG50";
static const char __pyx_k_FLAG51[] = "FLAG51";
static const char __pyx_k_FLAG52[] = "FLAG52";
static const char __pyx_k_FLAG53[] = "FLAG53";
static const char __pyx_k_FLAG54[] = "FLAG54";
static const char __pyx_k_FLAG55[] = "FLAG55";
static const char __pyx_k_FLAG56[] = "FLAG56";
static const char __pyx_k_FLAG57[] = "FLAG57";
static const char __pyx_k_FLAG58[] = "FLAG58";
static const char __pyx_k_FLAG59[] = "FLAG59";
static const char __pyx_k_FLAG60[] = "FLAG60";
static const char __pyx_k_FLAG61[] = "FLAG61";
static const char __pyx_k_FLAG62[] = "FLAG62";
static const char __pyx_k_FLAG63[] = "FLAG63";
static const char __pyx_k_Gender[] = "Gender";
static const char __pyx_k_LENGTH[] = "LENGTH";
static const char __pyx_k_Number[] = "Number";
static const char __pyx_k_PREFIX[] = "PREFIX";
static const char __pyx_k_Person[] = "Person";
static const char __pyx_k_Polite[] = "Polite";
static const char __pyx_k_Reflex[] = "Reflex";
static const char __pyx_k_SUFFIX[] = "SUFFIX";
static const char __pyx_k_errors[] = "errors";
static const char __pyx_k_format[] = "format";
static const char __pyx_k_import[] = "__import__";
static const char __pyx_k_lambda[] = "<lambda>";
static const char __pyx_k_morphs[] = "morphs";
static const char __pyx_k_name_2[] = "name";
static const char __pyx_k_number[] = "number";
static const char __pyx_k_sorted[] = "sorted";
static const char __pyx_k_update[] = "update";
static const char __pyx_k_AdjType[] = "AdjType";
static const char __pyx_k_AdpType[] = "AdpType";
static const char __pyx_k_AdvType[] = "AdvType";
static const char __pyx_k_Animacy[] = "Animacy";
static const char __pyx_k_ENT_IOB[] = "ENT_IOB";
static const char __pyx_k_Foreign[] = "Foreign";
static const char __pyx_k_IS_STOP[] = "IS_STOP";
static const char __pyx_k_NumForm[] = "NumForm";
static const char __pyx_k_NumType[] = "NumType";
static const char __pyx_k_Variant[] = "Variant";
static const char __pyx_k_int_key[] = "int_key";
static const char __pyx_k_ConjType[] = "ConjType";
static const char __pyx_k_ENT_TYPE[] = "ENT_TYPE";
static const char __pyx_k_IS_ALPHA[] = "IS_ALPHA";
static const char __pyx_k_IS_ASCII[] = "IS_ASCII";
static const char __pyx_k_IS_DIGIT[] = "IS_DIGIT";
static const char __pyx_k_IS_LOWER[] = "IS_LOWER";
static const char __pyx_k_IS_PUNCT[] = "IS_PUNCT";
static const char __pyx_k_IS_QUOTE[] = "IS_QUOTE";
static const char __pyx_k_IS_SPACE[] = "IS_SPACE";
static const char __pyx_k_IS_TITLE[] = "IS_TITLE";
static const char __pyx_k_IS_UPPER[] = "IS_UPPER";
static const char __pyx_k_LIKE_NUM[] = "LIKE_NUM";
static const char __pyx_k_LIKE_URL[] = "LIKE_URL";
static const char __pyx_k_NameType[] = "NameType";
static const char __pyx_k_Negative[] = "Negative";
static const char __pyx_k_NounType[] = "NounType";
static const char __pyx_k_NumValue[] = "NumValue";
static const char __pyx_k_PartType[] = "PartType";
static const char __pyx_k_Polarity[] = "Polarity";
static const char __pyx_k_PrepCase[] = "PrepCase";
static const char __pyx_k_PronType[] = "PronType";
static const char __pyx_k_VerbForm[] = "VerbForm";
static const char __pyx_k_VerbType[] = "VerbType";
static const char __pyx_k_tenspect[] = "tenspect";
static const char __pyx_k_ENT_KB_ID[] = "ENT_KB_ID";
static const char __pyx_k_PunctSide[] = "PunctSide";
static const char __pyx_k_PunctType[] = "PunctType";
static const char __pyx_k_Derivation[] = "Derivation";
static const char __pyx_k_IS_BRACKET[] = "IS_BRACKET";
static const char __pyx_k_LIKE_EMAIL[] = "LIKE_EMAIL";
static const char __pyx_k_SENT_START[] = "SENT_START";
static const char __pyx_k_ValueError[] = "ValueError";
static const char __pyx_k_inty_attrs[] = "inty_attrs";
static const char __pyx_k_morph_keys[] = "morph_keys";
static const char __pyx_k_IOB_STRINGS[] = "IOB_STRINGS";
static const char __pyx_k_IS_CURRENCY[] = "IS_CURRENCY";
static const char __pyx_k_intify_attr[] = "intify_attr";
static const char __pyx_k_spacy_attrs[] = "spacy.attrs";
static const char __pyx_k_strings_map[] = "strings_map";
static const char __pyx_k_StyleVariant[] = "StyleVariant";
static const char __pyx_k_intify_attrs[] = "intify_attrs";
static const char __pyx_k_IS_LEFT_PUNCT[] = "IS_LEFT_PUNCT";
static const char __pyx_k_do_deprecated[] = "_do_deprecated";
static const char __pyx_k_stringy_attrs[] = "stringy_attrs";
static const char __pyx_k_IS_RIGHT_PUNCT[] = "IS_RIGHT_PUNCT";
static const char __pyx_k_spacy_attrs_pyx[] = "spacy\\attrs.pyx";
static const char __pyx_k_IS_OOV_DEPRECATED[] = "IS_OOV_DEPRECATED";
static const char __pyx_k_cline_in_traceback[] = "cline_in_traceback";
static PyObject *__pyx_kp_s_;
static PyObject *__pyx_n_s_Abbr;
static PyObject *__pyx_n_s_AdjType;
static PyObject *__pyx_n_s_AdpType;
static PyObject *__pyx_n_s_AdvType;
static PyObject *__pyx_n_s_Animacy;
static PyObject *__pyx_n_s_Aspect;
static PyObject *__pyx_n_s_B;
static PyObject *__pyx_n_s_Case;
static PyObject *__pyx_n_s_ConjType;
static PyObject *__pyx_n_s_DEP;
static PyObject *__pyx_n_s_Degree;
static PyObject *__pyx_n_s_Derivation;
static PyObject *__pyx_n_s_E1025;
static PyObject *__pyx_n_s_ENT_ID;
static PyObject *__pyx_n_s_ENT_IOB;
static PyObject *__pyx_n_s_ENT_KB_ID;
static PyObject *__pyx_n_s_ENT_TYPE;
static PyObject *__pyx_n_s_Echo;
static PyObject *__pyx_n_s_Errors;
static PyObject *__pyx_n_s_F;
static PyObject *__pyx_n_s_FLAG19;
static PyObject *__pyx_n_s_FLAG20;
static PyObject *__pyx_n_s_FLAG21;
static PyObject *__pyx_n_s_FLAG22;
static PyObject *__pyx_n_s_FLAG23;
static PyObject *__pyx_n_s_FLAG24;
static PyObject *__pyx_n_s_FLAG25;
static PyObject *__pyx_n_s_FLAG26;
static PyObject *__pyx_n_s_FLAG27;
static PyObject *__pyx_n_s_FLAG28;
static PyObject *__pyx_n_s_FLAG29;
static PyObject *__pyx_n_s_FLAG30;
static PyObject *__pyx_n_s_FLAG31;
static PyObject *__pyx_n_s_FLAG32;
static PyObject *__pyx_n_s_FLAG33;
static PyObject *__pyx_n_s_FLAG34;
static PyObject *__pyx_n_s_FLAG35;
static PyObject *__pyx_n_s_FLAG36;
static PyObject *__pyx_n_s_FLAG37;
static PyObject *__pyx_n_s_FLAG38;
static PyObject *__pyx_n_s_FLAG39;
static PyObject *__pyx_n_s_FLAG40;
static PyObject *__pyx_n_s_FLAG41;
static PyObject *__pyx_n_s_FLAG42;
static PyObject *__pyx_n_s_FLAG43;
static PyObject *__pyx_n_s_FLAG44;
static PyObject *__pyx_n_s_FLAG45;
static PyObject *__pyx_n_s_FLAG46;
static PyObject *__pyx_n_s_FLAG47;
static PyObject *__pyx_n_s_FLAG48;
static PyObject *__pyx_n_s_FLAG49;
static PyObject *__pyx_n_s_FLAG50;
static PyObject *__pyx_n_s_FLAG51;
static PyObject *__pyx_n_s_FLAG52;
static PyObject *__pyx_n_s_FLAG53;
static PyObject *__pyx_n_s_FLAG54;
static PyObject *__pyx_n_s_FLAG55;
static PyObject *__pyx_n_s_FLAG56;
static PyObject *__pyx_n_s_FLAG57;
static PyObject *__pyx_n_s_FLAG58;
static PyObject *__pyx_n_s_FLAG59;
static PyObject *__pyx_n_s_FLAG60;
static PyObject *__pyx_n_s_FLAG61;
static PyObject *__pyx_n_s_FLAG62;
static PyObject *__pyx_n_s_FLAG63;
static PyObject *__pyx_n_s_Foreign;
static PyObject *__pyx_n_s_Gender;
static PyObject *__pyx_n_s_HEAD;
static PyObject *__pyx_n_s_Hyph;
static PyObject *__pyx_n_s_I;
static PyObject *__pyx_n_s_ID;
static PyObject *__pyx_n_s_IDS;
static PyObject *__pyx_n_s_IDX;
static PyObject *__pyx_n_s_IOB_STRINGS;
static PyObject *__pyx_n_s_IS_ALPHA;
static PyObject *__pyx_n_s_IS_ASCII;
static PyObject *__pyx_n_s_IS_BRACKET;
static PyObject *__pyx_n_s_IS_CURRENCY;
static PyObject *__pyx_n_s_IS_DIGIT;
static PyObject *__pyx_n_s_IS_LEFT_PUNCT;
static PyObject *__pyx_n_s_IS_LOWER;
static PyObject *__pyx_n_s_IS_OOV_DEPRECATED;
static PyObject *__pyx_n_s_IS_PUNCT;
static PyObject *__pyx_n_s_IS_QUOTE;
static PyObject *__pyx_n_s_IS_RIGHT_PUNCT;
static PyObject *__pyx_n_s_IS_SPACE;
static PyObject *__pyx_n_s_IS_STOP;
static PyObject *__pyx_n_s_IS_TITLE;
static PyObject *__pyx_n_s_IS_UPPER;
static PyObject *__pyx_n_s_L;
static PyObject *__pyx_n_s_LANG;
static PyObject *__pyx_n_s_LEMMA;
static PyObject *__pyx_n_s_LENGTH;
static PyObject *__pyx_n_s_LIKE_EMAIL;
static PyObject *__pyx_n_s_LIKE_NUM;
static PyObject *__pyx_n_s_LIKE_URL;
static PyObject *__pyx_n_s_LOWER;
static PyObject *__pyx_n_s_MORPH;
static PyObject *__pyx_n_s_Mood;
static PyObject *__pyx_n_s_NAMES;
static PyObject *__pyx_n_s_NORM;
static PyObject *__pyx_n_s_NameType;
static PyObject *__pyx_n_s_Negative;
static PyObject *__pyx_n_s_NounType;
static PyObject *__pyx_n_s_NumForm;
static PyObject *__pyx_n_s_NumType;
static PyObject *__pyx_n_s_NumValue;
static PyObject *__pyx_n_s_Number;
static PyObject *__pyx_n_s_O;
static PyObject *__pyx_n_s_ORTH;
static PyObject *__pyx_n_s_Other;
static PyObject *__pyx_n_s_POS;
static PyObject *__pyx_n_s_PREFIX;
static PyObject *__pyx_n_s_PartType;
static PyObject *__pyx_n_s_Person;
static PyObject *__pyx_n_s_Polarity;
static PyObject *__pyx_n_s_Polite;
static PyObject *__pyx_n_s_Poss;
static PyObject *__pyx_n_s_PrepCase;
static PyObject *__pyx_n_s_PronType;
static PyObject *__pyx_n_s_PunctSide;
static PyObject *__pyx_n_s_PunctType;
static PyObject *__pyx_n_s_Reflex;
static PyObject *__pyx_n_s_SENT_START;
static PyObject *__pyx_n_s_SHAPE;
static PyObject *__pyx_n_s_SPACY;
static PyObject *__pyx_n_s_SUFFIX;
static PyObject *__pyx_n_s_StyleVariant;
static PyObject *__pyx_n_s_TAG;
static PyObject *__pyx_n_s_Tense;
static PyObject *__pyx_n_s_ValueError;
static PyObject *__pyx_n_s_Variant;
static PyObject *__pyx_n_s_VerbForm;
static PyObject *__pyx_n_s_VerbType;
static PyObject *__pyx_n_s_Voice;
static PyObject *__pyx_n_s_add;
static PyObject *__pyx_n_s_cline_in_traceback;
static PyObject *__pyx_n_s_do_deprecated;
static PyObject *__pyx_n_s_errors;
static PyObject *__pyx_n_s_format;
static PyObject *__pyx_n_s_import;
static PyObject *__pyx_n_s_index;
static PyObject *__pyx_n_s_int_key;
static PyObject *__pyx_n_s_intify_attr;
static PyObject *__pyx_n_s_intify_attrs;
static PyObject *__pyx_n_s_inty_attrs;
static PyObject *__pyx_n_s_items;
static PyObject *__pyx_n_s_key;
static PyObject *__pyx_n_s_lambda;
static PyObject *__pyx_n_s_lower;
static PyObject *__pyx_n_s_main;
static PyObject *__pyx_n_s_morph;
static PyObject *__pyx_n_s_morph_keys;
static PyObject *__pyx_n_s_morphs;
static PyObject *__pyx_n_s_name;
static PyObject *__pyx_n_s_name_2;
static PyObject *__pyx_n_s_number;
static PyObject *__pyx_n_s_pop;
static PyObject *__pyx_n_s_pos;
static PyObject *__pyx_n_s_sorted;
static PyObject *__pyx_n_s_spacy_attrs;
static PyObject *__pyx_kp_s_spacy_attrs_pyx;
static PyObject *__pyx_n_s_strings_map;
static PyObject *__pyx_n_s_stringy_attrs;
static PyObject *__pyx_n_s_tenspect;
static PyObject *__pyx_n_s_test;
static PyObject *__pyx_n_s_update;
static PyObject *__pyx_n_s_upper;
static PyObject *__pyx_n_s_value;
static PyObject *__pyx_lambda_funcdef_5spacy_5attrs_lambda(CYTHON_UNUSED PyObject *__pyx_self, PyObject *__pyx_v_item); /* proto */
static PyObject *__pyx_pf_5spacy_5attrs_intify_attrs(CYTHON_UNUSED PyObject *__pyx_self, PyObject *__pyx_v_stringy_attrs, PyObject *__pyx_v_strings_map, PyObject *__pyx_v__do_deprecated); /* proto */
static PyObject *__pyx_pf_5spacy_5attrs_2intify_attr(CYTHON_UNUSED PyObject *__pyx_self, PyObject *__pyx_v_name); /* proto */
static PyObject *__pyx_tuple__2;
static PyObject *__pyx_tuple__3;
static PyObject *__pyx_tuple__5;
static PyObject *__pyx_codeobj__4;
static PyObject *__pyx_codeobj__6;
/* Late includes */

/* "spacy/attrs.pyx":96
 * 
 * # ATTR IDs, in order of the symbol
 * NAMES = [key for key, value in sorted(IDS.items(), key=lambda item: item[1])]             # <<<<<<<<<<<<<<
 * locals().update(IDS)
 * 
 */

/* Python wrapper */
static PyObject *__pyx_pw_5spacy_5attrs_4lambda(PyObject *__pyx_self, PyObject *__pyx_v_item); /*proto*/
static PyMethodDef __pyx_mdef_5spacy_5attrs_4lambda = {"lambda", (PyCFunction)__pyx_pw_5spacy_5attrs_4lambda, METH_O, 0};
static PyObject *__pyx_pw_5spacy_5attrs_4lambda(PyObject *__pyx_self, PyObject *__pyx_v_item) {
  PyObject *__pyx_r = 0;
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("lambda (wrapper)", 0);
  __pyx_r = __pyx_lambda_funcdef_5spacy_5attrs_lambda(__pyx_self, ((PyObject *)__pyx_v_item));

  /* function exit code */
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

static PyObject *__pyx_lambda_funcdef_5spacy_5attrs_lambda(CYTHON_UNUSED PyObject *__pyx_self, PyObject *__pyx_v_item) {
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;
  __Pyx_RefNannySetupContext("lambda", 0);
  __Pyx_XDECREF(__pyx_r);
  __pyx_t_1 = __Pyx_GetItemInt(__pyx_v_item, 1, long, 1, __Pyx_PyInt_From_long, 0, 0, 1); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 96, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_r = __pyx_t_1;
  __pyx_t_1 = 0;
  goto __pyx_L0;

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_AddTraceback("spacy.attrs.lambda", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = NULL;
  __pyx_L0:;
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "spacy/attrs.pyx":100
 * 
 * 
 * def intify_attrs(stringy_attrs, strings_map=None, _do_deprecated=False):             # <<<<<<<<<<<<<<
 *     """
 *     Normalize a dictionary of attributes, converting them to ints.
 */

/* Python wrapper */
static PyObject *__pyx_pw_5spacy_5attrs_1intify_attrs(PyObject *__pyx_self, PyObject *__pyx_args, PyObject *__pyx_kwds); /*proto*/
static char __pyx_doc_5spacy_5attrs_intify_attrs[] = "intify_attrs(stringy_attrs, strings_map=None, _do_deprecated=False)\n\n    Normalize a dictionary of attributes, converting them to ints.\n\n    stringy_attrs (dict): Dictionary keyed by attribute string names. Values\n        can be ints or strings.\n    strings_map (StringStore): Defaults to None. If provided, encodes string\n        values into ints.\n    RETURNS (dict): Attributes dictionary with keys and optionally values\n        converted to ints.\n    ";
static PyMethodDef __pyx_mdef_5spacy_5attrs_1intify_attrs = {"intify_attrs", (PyCFunction)(void*)(PyCFunctionWithKeywords)__pyx_pw_5spacy_5attrs_1intify_attrs, METH_VARARGS|METH_KEYWORDS, __pyx_doc_5spacy_5attrs_intify_attrs};
static PyObject *__pyx_pw_5spacy_5attrs_1intify_attrs(PyObject *__pyx_self, PyObject *__pyx_args, PyObject *__pyx_kwds) {
  PyObject *__pyx_v_stringy_attrs = 0;
  PyObject *__pyx_v_strings_map = 0;
  PyObject *__pyx_v__do_deprecated = 0;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;
  PyObject *__pyx_r = 0;
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("intify_attrs (wrapper)", 0);
  {
    static PyObject **__pyx_pyargnames[] = {&__pyx_n_s_stringy_attrs,&__pyx_n_s_strings_map,&__pyx_n_s_do_deprecated,0};
    PyObject* values[3] = {0,0,0};
    values[1] = ((PyObject *)Py_None);
    values[2] = ((PyObject *)Py_False);
    if (unlikely(__pyx_kwds)) {
      Py_ssize_t kw_args;
      const Py_ssize_t pos_args = PyTuple_GET_SIZE(__pyx_args);
      switch (pos_args) {
        case  3: values[2] = PyTuple_GET_ITEM(__pyx_args, 2);
        CYTHON_FALLTHROUGH;
        case  2: values[1] = PyTuple_GET_ITEM(__pyx_args, 1);
        CYTHON_FALLTHROUGH;
        case  1: values[0] = PyTuple_GET_ITEM(__pyx_args, 0);
        CYTHON_FALLTHROUGH;
        case  0: break;
        default: goto __pyx_L5_argtuple_error;
      }
      kw_args = PyDict_Size(__pyx_kwds);
      switch (pos_args) {
        case  0:
        if (likely((values[0] = __Pyx_PyDict_GetItemStr(__pyx_kwds, __pyx_n_s_stringy_attrs)) != 0)) kw_args--;
        else goto __pyx_L5_argtuple_error;
        CYTHON_FALLTHROUGH;
        case  1:
        if (kw_args > 0) {
          PyObject* value = __Pyx_PyDict_GetItemStr(__pyx_kwds, __pyx_n_s_strings_map);
          if (value) { values[1] = value; kw_args--; }
        }
        CYTHON_FALLTHROUGH;
        case  2:
        if (kw_args > 0) {
          PyObject* value = __Pyx_PyDict_GetItemStr(__pyx_kwds, __pyx_n_s_do_deprecated);
          if (value) { values[2] = value; kw_args--; }
        }
      }
      if (unlikely(kw_args > 0)) {
        if (unlikely(__Pyx_ParseOptionalKeywords(__pyx_kwds, __pyx_pyargnames, 0, values, pos_args, "intify_attrs") < 0)) __PYX_ERR(0, 100, __pyx_L3_error)
      }
    } else {
      switch (PyTuple_GET_SIZE(__pyx_args)) {
        case  3: values[2] = PyTuple_GET_ITEM(__pyx_args, 2);
        CYTHON_FALLTHROUGH;
        case  2: values[1] = PyTuple_GET_ITEM(__pyx_args, 1);
        CYTHON_FALLTHROUGH;
        case  1: values[0] = PyTuple_GET_ITEM(__pyx_args, 0);
        break;
        default: goto __pyx_L5_argtuple_error;
      }
    }
    __pyx_v_stringy_attrs = values[0];
    __pyx_v_strings_map = values[1];
    __pyx_v__do_deprecated = values[2];
  }
  goto __pyx_L4_argument_unpacking_done;
  __pyx_L5_argtuple_error:;
  __Pyx_RaiseArgtupleInvalid("intify_attrs", 0, 1, 3, PyTuple_GET_SIZE(__pyx_args)); __PYX_ERR(0, 100, __pyx_L3_error)
  __pyx_L3_error:;
  __Pyx_AddTraceback("spacy.attrs.intify_attrs", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __Pyx_RefNannyFinishContext();
  return NULL;
  __pyx_L4_argument_unpacking_done:;
  __pyx_r = __pyx_pf_5spacy_5attrs_intify_attrs(__pyx_self, __pyx_v_stringy_attrs, __pyx_v_strings_map, __pyx_v__do_deprecated);

  /* function exit code */
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

static PyObject *__pyx_pf_5spacy_5attrs_intify_attrs(CYTHON_UNUSED PyObject *__pyx_self, PyObject *__pyx_v_stringy_attrs, PyObject *__pyx_v_strings_map, PyObject *__pyx_v__do_deprecated) {
  PyObject *__pyx_v_inty_attrs = NULL;
  CYTHON_UNUSED PyObject *__pyx_v_morphs = NULL;
  PyObject *__pyx_v_morph_keys = NULL;
  PyObject *__pyx_v_key = NULL;
  PyObject *__pyx_v_name = NULL;
  PyObject *__pyx_v_value = NULL;
  PyObject *__pyx_v_int_key = NULL;
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  int __pyx_t_2;
  int __pyx_t_3;
  PyObject *__pyx_t_4 = NULL;
  PyObject *__pyx_t_5 = NULL;
  Py_ssize_t __pyx_t_6;
  PyObject *__pyx_t_7 = NULL;
  PyObject *__pyx_t_8 = NULL;
  PyObject *__pyx_t_9 = NULL;
  PyObject *(*__pyx_t_10)(PyObject *);
  PyObject *(*__pyx_t_11)(PyObject *);
  int __pyx_t_12;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;
  __Pyx_RefNannySetupContext("intify_attrs", 0);

  /* "spacy/attrs.pyx":111
 *         converted to ints.
 *     """
 *     inty_attrs = {}             # <<<<<<<<<<<<<<
 *     if _do_deprecated:
 *         if "F" in stringy_attrs:
 */
  __pyx_t_1 = __Pyx_PyDict_NewPresized(0); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 111, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_v_inty_attrs = ((PyObject*)__pyx_t_1);
  __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":112
 *     """
 *     inty_attrs = {}
 *     if _do_deprecated:             # <<<<<<<<<<<<<<
 *         if "F" in stringy_attrs:
 *             stringy_attrs["ORTH"] = stringy_attrs.pop("F")
 */
  __pyx_t_2 = __Pyx_PyObject_IsTrue(__pyx_v__do_deprecated); if (unlikely(__pyx_t_2 < 0)) __PYX_ERR(0, 112, __pyx_L1_error)
  if (__pyx_t_2) {

    /* "spacy/attrs.pyx":113
 *     inty_attrs = {}
 *     if _do_deprecated:
 *         if "F" in stringy_attrs:             # <<<<<<<<<<<<<<
 *             stringy_attrs["ORTH"] = stringy_attrs.pop("F")
 *         if "L" in stringy_attrs:
 */
    __pyx_t_2 = (__Pyx_PySequence_ContainsTF(__pyx_n_s_F, __pyx_v_stringy_attrs, Py_EQ)); if (unlikely(__pyx_t_2 < 0)) __PYX_ERR(0, 113, __pyx_L1_error)
    __pyx_t_3 = (__pyx_t_2 != 0);
    if (__pyx_t_3) {

      /* "spacy/attrs.pyx":114
 *     if _do_deprecated:
 *         if "F" in stringy_attrs:
 *             stringy_attrs["ORTH"] = stringy_attrs.pop("F")             # <<<<<<<<<<<<<<
 *         if "L" in stringy_attrs:
 *             stringy_attrs["LEMMA"] = stringy_attrs.pop("L")
 */
      __pyx_t_4 = __Pyx_PyObject_GetAttrStr(__pyx_v_stringy_attrs, __pyx_n_s_pop); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 114, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_4);
      __pyx_t_5 = NULL;
      if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_4))) {
        __pyx_t_5 = PyMethod_GET_SELF(__pyx_t_4);
        if (likely(__pyx_t_5)) {
          PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_4);
          __Pyx_INCREF(__pyx_t_5);
          __Pyx_INCREF(function);
          __Pyx_DECREF_SET(__pyx_t_4, function);
        }
      }
      __pyx_t_1 = (__pyx_t_5) ? __Pyx_PyObject_Call2Args(__pyx_t_4, __pyx_t_5, __pyx_n_s_F) : __Pyx_PyObject_CallOneArg(__pyx_t_4, __pyx_n_s_F);
      __Pyx_XDECREF(__pyx_t_5); __pyx_t_5 = 0;
      if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 114, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_1);
      __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
      if (unlikely(PyObject_SetItem(__pyx_v_stringy_attrs, __pyx_n_s_ORTH, __pyx_t_1) < 0)) __PYX_ERR(0, 114, __pyx_L1_error)
      __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

      /* "spacy/attrs.pyx":113
 *     inty_attrs = {}
 *     if _do_deprecated:
 *         if "F" in stringy_attrs:             # <<<<<<<<<<<<<<
 *             stringy_attrs["ORTH"] = stringy_attrs.pop("F")
 *         if "L" in stringy_attrs:
 */
    }

    /* "spacy/attrs.pyx":115
 *         if "F" in stringy_attrs:
 *             stringy_attrs["ORTH"] = stringy_attrs.pop("F")
 *         if "L" in stringy_attrs:             # <<<<<<<<<<<<<<
 *             stringy_attrs["LEMMA"] = stringy_attrs.pop("L")
 *         if "pos" in stringy_attrs:
 */
    __pyx_t_3 = (__Pyx_PySequence_ContainsTF(__pyx_n_s_L, __pyx_v_stringy_attrs, Py_EQ)); if (unlikely(__pyx_t_3 < 0)) __PYX_ERR(0, 115, __pyx_L1_error)
    __pyx_t_2 = (__pyx_t_3 != 0);
    if (__pyx_t_2) {

      /* "spacy/attrs.pyx":116
 *             stringy_attrs["ORTH"] = stringy_attrs.pop("F")
 *         if "L" in stringy_attrs:
 *             stringy_attrs["LEMMA"] = stringy_attrs.pop("L")             # <<<<<<<<<<<<<<
 *         if "pos" in stringy_attrs:
 *             stringy_attrs["TAG"] = stringy_attrs.pop("pos")
 */
      __pyx_t_4 = __Pyx_PyObject_GetAttrStr(__pyx_v_stringy_attrs, __pyx_n_s_pop); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 116, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_4);
      __pyx_t_5 = NULL;
      if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_4))) {
        __pyx_t_5 = PyMethod_GET_SELF(__pyx_t_4);
        if (likely(__pyx_t_5)) {
          PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_4);
          __Pyx_INCREF(__pyx_t_5);
          __Pyx_INCREF(function);
          __Pyx_DECREF_SET(__pyx_t_4, function);
        }
      }
      __pyx_t_1 = (__pyx_t_5) ? __Pyx_PyObject_Call2Args(__pyx_t_4, __pyx_t_5, __pyx_n_s_L) : __Pyx_PyObject_CallOneArg(__pyx_t_4, __pyx_n_s_L);
      __Pyx_XDECREF(__pyx_t_5); __pyx_t_5 = 0;
      if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 116, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_1);
      __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
      if (unlikely(PyObject_SetItem(__pyx_v_stringy_attrs, __pyx_n_s_LEMMA, __pyx_t_1) < 0)) __PYX_ERR(0, 116, __pyx_L1_error)
      __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

      /* "spacy/attrs.pyx":115
 *         if "F" in stringy_attrs:
 *             stringy_attrs["ORTH"] = stringy_attrs.pop("F")
 *         if "L" in stringy_attrs:             # <<<<<<<<<<<<<<
 *             stringy_attrs["LEMMA"] = stringy_attrs.pop("L")
 *         if "pos" in stringy_attrs:
 */
    }

    /* "spacy/attrs.pyx":117
 *         if "L" in stringy_attrs:
 *             stringy_attrs["LEMMA"] = stringy_attrs.pop("L")
 *         if "pos" in stringy_attrs:             # <<<<<<<<<<<<<<
 *             stringy_attrs["TAG"] = stringy_attrs.pop("pos")
 *         if "morph" in stringy_attrs:
 */
    __pyx_t_2 = (__Pyx_PySequence_ContainsTF(__pyx_n_s_pos, __pyx_v_stringy_attrs, Py_EQ)); if (unlikely(__pyx_t_2 < 0)) __PYX_ERR(0, 117, __pyx_L1_error)
    __pyx_t_3 = (__pyx_t_2 != 0);
    if (__pyx_t_3) {

      /* "spacy/attrs.pyx":118
 *             stringy_attrs["LEMMA"] = stringy_attrs.pop("L")
 *         if "pos" in stringy_attrs:
 *             stringy_attrs["TAG"] = stringy_attrs.pop("pos")             # <<<<<<<<<<<<<<
 *         if "morph" in stringy_attrs:
 *             morphs = stringy_attrs.pop("morph")
 */
      __pyx_t_4 = __Pyx_PyObject_GetAttrStr(__pyx_v_stringy_attrs, __pyx_n_s_pop); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 118, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_4);
      __pyx_t_5 = NULL;
      if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_4))) {
        __pyx_t_5 = PyMethod_GET_SELF(__pyx_t_4);
        if (likely(__pyx_t_5)) {
          PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_4);
          __Pyx_INCREF(__pyx_t_5);
          __Pyx_INCREF(function);
          __Pyx_DECREF_SET(__pyx_t_4, function);
        }
      }
      __pyx_t_1 = (__pyx_t_5) ? __Pyx_PyObject_Call2Args(__pyx_t_4, __pyx_t_5, __pyx_n_s_pos) : __Pyx_PyObject_CallOneArg(__pyx_t_4, __pyx_n_s_pos);
      __Pyx_XDECREF(__pyx_t_5); __pyx_t_5 = 0;
      if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 118, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_1);
      __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
      if (unlikely(PyObject_SetItem(__pyx_v_stringy_attrs, __pyx_n_s_TAG, __pyx_t_1) < 0)) __PYX_ERR(0, 118, __pyx_L1_error)
      __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

      /* "spacy/attrs.pyx":117
 *         if "L" in stringy_attrs:
 *             stringy_attrs["LEMMA"] = stringy_attrs.pop("L")
 *         if "pos" in stringy_attrs:             # <<<<<<<<<<<<<<
 *             stringy_attrs["TAG"] = stringy_attrs.pop("pos")
 *         if "morph" in stringy_attrs:
 */
    }

    /* "spacy/attrs.pyx":119
 *         if "pos" in stringy_attrs:
 *             stringy_attrs["TAG"] = stringy_attrs.pop("pos")
 *         if "morph" in stringy_attrs:             # <<<<<<<<<<<<<<
 *             morphs = stringy_attrs.pop("morph")
 *         if "number" in stringy_attrs:
 */
    __pyx_t_3 = (__Pyx_PySequence_ContainsTF(__pyx_n_s_morph, __pyx_v_stringy_attrs, Py_EQ)); if (unlikely(__pyx_t_3 < 0)) __PYX_ERR(0, 119, __pyx_L1_error)
    __pyx_t_2 = (__pyx_t_3 != 0);
    if (__pyx_t_2) {

      /* "spacy/attrs.pyx":120
 *             stringy_attrs["TAG"] = stringy_attrs.pop("pos")
 *         if "morph" in stringy_attrs:
 *             morphs = stringy_attrs.pop("morph")             # <<<<<<<<<<<<<<
 *         if "number" in stringy_attrs:
 *             stringy_attrs.pop("number")
 */
      __pyx_t_4 = __Pyx_PyObject_GetAttrStr(__pyx_v_stringy_attrs, __pyx_n_s_pop); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 120, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_4);
      __pyx_t_5 = NULL;
      if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_4))) {
        __pyx_t_5 = PyMethod_GET_SELF(__pyx_t_4);
        if (likely(__pyx_t_5)) {
          PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_4);
          __Pyx_INCREF(__pyx_t_5);
          __Pyx_INCREF(function);
          __Pyx_DECREF_SET(__pyx_t_4, function);
        }
      }
      __pyx_t_1 = (__pyx_t_5) ? __Pyx_PyObject_Call2Args(__pyx_t_4, __pyx_t_5, __pyx_n_s_morph) : __Pyx_PyObject_CallOneArg(__pyx_t_4, __pyx_n_s_morph);
      __Pyx_XDECREF(__pyx_t_5); __pyx_t_5 = 0;
      if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 120, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_1);
      __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
      __pyx_v_morphs = __pyx_t_1;
      __pyx_t_1 = 0;

      /* "spacy/attrs.pyx":119
 *         if "pos" in stringy_attrs:
 *             stringy_attrs["TAG"] = stringy_attrs.pop("pos")
 *         if "morph" in stringy_attrs:             # <<<<<<<<<<<<<<
 *             morphs = stringy_attrs.pop("morph")
 *         if "number" in stringy_attrs:
 */
    }

    /* "spacy/attrs.pyx":121
 *         if "morph" in stringy_attrs:
 *             morphs = stringy_attrs.pop("morph")
 *         if "number" in stringy_attrs:             # <<<<<<<<<<<<<<
 *             stringy_attrs.pop("number")
 *         if "tenspect" in stringy_attrs:
 */
    __pyx_t_2 = (__Pyx_PySequence_ContainsTF(__pyx_n_s_number, __pyx_v_stringy_attrs, Py_EQ)); if (unlikely(__pyx_t_2 < 0)) __PYX_ERR(0, 121, __pyx_L1_error)
    __pyx_t_3 = (__pyx_t_2 != 0);
    if (__pyx_t_3) {

      /* "spacy/attrs.pyx":122
 *             morphs = stringy_attrs.pop("morph")
 *         if "number" in stringy_attrs:
 *             stringy_attrs.pop("number")             # <<<<<<<<<<<<<<
 *         if "tenspect" in stringy_attrs:
 *             stringy_attrs.pop("tenspect")
 */
      __pyx_t_4 = __Pyx_PyObject_GetAttrStr(__pyx_v_stringy_attrs, __pyx_n_s_pop); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 122, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_4);
      __pyx_t_5 = NULL;
      if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_4))) {
        __pyx_t_5 = PyMethod_GET_SELF(__pyx_t_4);
        if (likely(__pyx_t_5)) {
          PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_4);
          __Pyx_INCREF(__pyx_t_5);
          __Pyx_INCREF(function);
          __Pyx_DECREF_SET(__pyx_t_4, function);
        }
      }
      __pyx_t_1 = (__pyx_t_5) ? __Pyx_PyObject_Call2Args(__pyx_t_4, __pyx_t_5, __pyx_n_s_number) : __Pyx_PyObject_CallOneArg(__pyx_t_4, __pyx_n_s_number);
      __Pyx_XDECREF(__pyx_t_5); __pyx_t_5 = 0;
      if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 122, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_1);
      __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
      __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

      /* "spacy/attrs.pyx":121
 *         if "morph" in stringy_attrs:
 *             morphs = stringy_attrs.pop("morph")
 *         if "number" in stringy_attrs:             # <<<<<<<<<<<<<<
 *             stringy_attrs.pop("number")
 *         if "tenspect" in stringy_attrs:
 */
    }

    /* "spacy/attrs.pyx":123
 *         if "number" in stringy_attrs:
 *             stringy_attrs.pop("number")
 *         if "tenspect" in stringy_attrs:             # <<<<<<<<<<<<<<
 *             stringy_attrs.pop("tenspect")
 *         morph_keys = [
 */
    __pyx_t_3 = (__Pyx_PySequence_ContainsTF(__pyx_n_s_tenspect, __pyx_v_stringy_attrs, Py_EQ)); if (unlikely(__pyx_t_3 < 0)) __PYX_ERR(0, 123, __pyx_L1_error)
    __pyx_t_2 = (__pyx_t_3 != 0);
    if (__pyx_t_2) {

      /* "spacy/attrs.pyx":124
 *             stringy_attrs.pop("number")
 *         if "tenspect" in stringy_attrs:
 *             stringy_attrs.pop("tenspect")             # <<<<<<<<<<<<<<
 *         morph_keys = [
 *             "PunctType",
 */
      __pyx_t_4 = __Pyx_PyObject_GetAttrStr(__pyx_v_stringy_attrs, __pyx_n_s_pop); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 124, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_4);
      __pyx_t_5 = NULL;
      if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_4))) {
        __pyx_t_5 = PyMethod_GET_SELF(__pyx_t_4);
        if (likely(__pyx_t_5)) {
          PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_4);
          __Pyx_INCREF(__pyx_t_5);
          __Pyx_INCREF(function);
          __Pyx_DECREF_SET(__pyx_t_4, function);
        }
      }
      __pyx_t_1 = (__pyx_t_5) ? __Pyx_PyObject_Call2Args(__pyx_t_4, __pyx_t_5, __pyx_n_s_tenspect) : __Pyx_PyObject_CallOneArg(__pyx_t_4, __pyx_n_s_tenspect);
      __Pyx_XDECREF(__pyx_t_5); __pyx_t_5 = 0;
      if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 124, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_1);
      __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
      __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

      /* "spacy/attrs.pyx":123
 *         if "number" in stringy_attrs:
 *             stringy_attrs.pop("number")
 *         if "tenspect" in stringy_attrs:             # <<<<<<<<<<<<<<
 *             stringy_attrs.pop("tenspect")
 *         morph_keys = [
 */
    }

    /* "spacy/attrs.pyx":125
 *         if "tenspect" in stringy_attrs:
 *             stringy_attrs.pop("tenspect")
 *         morph_keys = [             # <<<<<<<<<<<<<<
 *             "PunctType",
 *             "PunctSide",
 */
    __pyx_t_1 = PyList_New(47); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 125, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __Pyx_INCREF(__pyx_n_s_PunctType);
    __Pyx_GIVEREF(__pyx_n_s_PunctType);
    PyList_SET_ITEM(__pyx_t_1, 0, __pyx_n_s_PunctType);
    __Pyx_INCREF(__pyx_n_s_PunctSide);
    __Pyx_GIVEREF(__pyx_n_s_PunctSide);
    PyList_SET_ITEM(__pyx_t_1, 1, __pyx_n_s_PunctSide);
    __Pyx_INCREF(__pyx_n_s_Other);
    __Pyx_GIVEREF(__pyx_n_s_Other);
    PyList_SET_ITEM(__pyx_t_1, 2, __pyx_n_s_Other);
    __Pyx_INCREF(__pyx_n_s_Degree);
    __Pyx_GIVEREF(__pyx_n_s_Degree);
    PyList_SET_ITEM(__pyx_t_1, 3, __pyx_n_s_Degree);
    __Pyx_INCREF(__pyx_n_s_AdvType);
    __Pyx_GIVEREF(__pyx_n_s_AdvType);
    PyList_SET_ITEM(__pyx_t_1, 4, __pyx_n_s_AdvType);
    __Pyx_INCREF(__pyx_n_s_Number);
    __Pyx_GIVEREF(__pyx_n_s_Number);
    PyList_SET_ITEM(__pyx_t_1, 5, __pyx_n_s_Number);
    __Pyx_INCREF(__pyx_n_s_VerbForm);
    __Pyx_GIVEREF(__pyx_n_s_VerbForm);
    PyList_SET_ITEM(__pyx_t_1, 6, __pyx_n_s_VerbForm);
    __Pyx_INCREF(__pyx_n_s_PronType);
    __Pyx_GIVEREF(__pyx_n_s_PronType);
    PyList_SET_ITEM(__pyx_t_1, 7, __pyx_n_s_PronType);
    __Pyx_INCREF(__pyx_n_s_Aspect);
    __Pyx_GIVEREF(__pyx_n_s_Aspect);
    PyList_SET_ITEM(__pyx_t_1, 8, __pyx_n_s_Aspect);
    __Pyx_INCREF(__pyx_n_s_Tense);
    __Pyx_GIVEREF(__pyx_n_s_Tense);
    PyList_SET_ITEM(__pyx_t_1, 9, __pyx_n_s_Tense);
    __Pyx_INCREF(__pyx_n_s_PartType);
    __Pyx_GIVEREF(__pyx_n_s_PartType);
    PyList_SET_ITEM(__pyx_t_1, 10, __pyx_n_s_PartType);
    __Pyx_INCREF(__pyx_n_s_Poss);
    __Pyx_GIVEREF(__pyx_n_s_Poss);
    PyList_SET_ITEM(__pyx_t_1, 11, __pyx_n_s_Poss);
    __Pyx_INCREF(__pyx_n_s_Hyph);
    __Pyx_GIVEREF(__pyx_n_s_Hyph);
    PyList_SET_ITEM(__pyx_t_1, 12, __pyx_n_s_Hyph);
    __Pyx_INCREF(__pyx_n_s_ConjType);
    __Pyx_GIVEREF(__pyx_n_s_ConjType);
    PyList_SET_ITEM(__pyx_t_1, 13, __pyx_n_s_ConjType);
    __Pyx_INCREF(__pyx_n_s_NumType);
    __Pyx_GIVEREF(__pyx_n_s_NumType);
    PyList_SET_ITEM(__pyx_t_1, 14, __pyx_n_s_NumType);
    __Pyx_INCREF(__pyx_n_s_Foreign);
    __Pyx_GIVEREF(__pyx_n_s_Foreign);
    PyList_SET_ITEM(__pyx_t_1, 15, __pyx_n_s_Foreign);
    __Pyx_INCREF(__pyx_n_s_VerbType);
    __Pyx_GIVEREF(__pyx_n_s_VerbType);
    PyList_SET_ITEM(__pyx_t_1, 16, __pyx_n_s_VerbType);
    __Pyx_INCREF(__pyx_n_s_NounType);
    __Pyx_GIVEREF(__pyx_n_s_NounType);
    PyList_SET_ITEM(__pyx_t_1, 17, __pyx_n_s_NounType);
    __Pyx_INCREF(__pyx_n_s_Gender);
    __Pyx_GIVEREF(__pyx_n_s_Gender);
    PyList_SET_ITEM(__pyx_t_1, 18, __pyx_n_s_Gender);
    __Pyx_INCREF(__pyx_n_s_Mood);
    __Pyx_GIVEREF(__pyx_n_s_Mood);
    PyList_SET_ITEM(__pyx_t_1, 19, __pyx_n_s_Mood);
    __Pyx_INCREF(__pyx_n_s_Negative);
    __Pyx_GIVEREF(__pyx_n_s_Negative);
    PyList_SET_ITEM(__pyx_t_1, 20, __pyx_n_s_Negative);
    __Pyx_INCREF(__pyx_n_s_Tense);
    __Pyx_GIVEREF(__pyx_n_s_Tense);
    PyList_SET_ITEM(__pyx_t_1, 21, __pyx_n_s_Tense);
    __Pyx_INCREF(__pyx_n_s_Voice);
    __Pyx_GIVEREF(__pyx_n_s_Voice);
    PyList_SET_ITEM(__pyx_t_1, 22, __pyx_n_s_Voice);
    __Pyx_INCREF(__pyx_n_s_Abbr);
    __Pyx_GIVEREF(__pyx_n_s_Abbr);
    PyList_SET_ITEM(__pyx_t_1, 23, __pyx_n_s_Abbr);
    __Pyx_INCREF(__pyx_n_s_Derivation);
    __Pyx_GIVEREF(__pyx_n_s_Derivation);
    PyList_SET_ITEM(__pyx_t_1, 24, __pyx_n_s_Derivation);
    __Pyx_INCREF(__pyx_n_s_Echo);
    __Pyx_GIVEREF(__pyx_n_s_Echo);
    PyList_SET_ITEM(__pyx_t_1, 25, __pyx_n_s_Echo);
    __Pyx_INCREF(__pyx_n_s_Foreign);
    __Pyx_GIVEREF(__pyx_n_s_Foreign);
    PyList_SET_ITEM(__pyx_t_1, 26, __pyx_n_s_Foreign);
    __Pyx_INCREF(__pyx_n_s_NameType);
    __Pyx_GIVEREF(__pyx_n_s_NameType);
    PyList_SET_ITEM(__pyx_t_1, 27, __pyx_n_s_NameType);
    __Pyx_INCREF(__pyx_n_s_NounType);
    __Pyx_GIVEREF(__pyx_n_s_NounType);
    PyList_SET_ITEM(__pyx_t_1, 28, __pyx_n_s_NounType);
    __Pyx_INCREF(__pyx_n_s_NumForm);
    __Pyx_GIVEREF(__pyx_n_s_NumForm);
    PyList_SET_ITEM(__pyx_t_1, 29, __pyx_n_s_NumForm);
    __Pyx_INCREF(__pyx_n_s_NumValue);
    __Pyx_GIVEREF(__pyx_n_s_NumValue);
    PyList_SET_ITEM(__pyx_t_1, 30, __pyx_n_s_NumValue);
    __Pyx_INCREF(__pyx_n_s_PartType);
    __Pyx_GIVEREF(__pyx_n_s_PartType);
    PyList_SET_ITEM(__pyx_t_1, 31, __pyx_n_s_PartType);
    __Pyx_INCREF(__pyx_n_s_Polite);
    __Pyx_GIVEREF(__pyx_n_s_Polite);
    PyList_SET_ITEM(__pyx_t_1, 32, __pyx_n_s_Polite);
    __Pyx_INCREF(__pyx_n_s_StyleVariant);
    __Pyx_GIVEREF(__pyx_n_s_StyleVariant);
    PyList_SET_ITEM(__pyx_t_1, 33, __pyx_n_s_StyleVariant);
    __Pyx_INCREF(__pyx_n_s_PronType);
    __Pyx_GIVEREF(__pyx_n_s_PronType);
    PyList_SET_ITEM(__pyx_t_1, 34, __pyx_n_s_PronType);
    __Pyx_INCREF(__pyx_n_s_AdjType);
    __Pyx_GIVEREF(__pyx_n_s_AdjType);
    PyList_SET_ITEM(__pyx_t_1, 35, __pyx_n_s_AdjType);
    __Pyx_INCREF(__pyx_n_s_Person);
    __Pyx_GIVEREF(__pyx_n_s_Person);
    PyList_SET_ITEM(__pyx_t_1, 36, __pyx_n_s_Person);
    __Pyx_INCREF(__pyx_n_s_Variant);
    __Pyx_GIVEREF(__pyx_n_s_Variant);
    PyList_SET_ITEM(__pyx_t_1, 37, __pyx_n_s_Variant);
    __Pyx_INCREF(__pyx_n_s_AdpType);
    __Pyx_GIVEREF(__pyx_n_s_AdpType);
    PyList_SET_ITEM(__pyx_t_1, 38, __pyx_n_s_AdpType);
    __Pyx_INCREF(__pyx_n_s_Reflex);
    __Pyx_GIVEREF(__pyx_n_s_Reflex);
    PyList_SET_ITEM(__pyx_t_1, 39, __pyx_n_s_Reflex);
    __Pyx_INCREF(__pyx_n_s_Negative);
    __Pyx_GIVEREF(__pyx_n_s_Negative);
    PyList_SET_ITEM(__pyx_t_1, 40, __pyx_n_s_Negative);
    __Pyx_INCREF(__pyx_n_s_Mood);
    __Pyx_GIVEREF(__pyx_n_s_Mood);
    PyList_SET_ITEM(__pyx_t_1, 41, __pyx_n_s_Mood);
    __Pyx_INCREF(__pyx_n_s_Aspect);
    __Pyx_GIVEREF(__pyx_n_s_Aspect);
    PyList_SET_ITEM(__pyx_t_1, 42, __pyx_n_s_Aspect);
    __Pyx_INCREF(__pyx_n_s_Case);
    __Pyx_GIVEREF(__pyx_n_s_Case);
    PyList_SET_ITEM(__pyx_t_1, 43, __pyx_n_s_Case);
    __Pyx_INCREF(__pyx_n_s_Polarity);
    __Pyx_GIVEREF(__pyx_n_s_Polarity);
    PyList_SET_ITEM(__pyx_t_1, 44, __pyx_n_s_Polarity);
    __Pyx_INCREF(__pyx_n_s_PrepCase);
    __Pyx_GIVEREF(__pyx_n_s_PrepCase);
    PyList_SET_ITEM(__pyx_t_1, 45, __pyx_n_s_PrepCase);
    __Pyx_INCREF(__pyx_n_s_Animacy);
    __Pyx_GIVEREF(__pyx_n_s_Animacy);
    PyList_SET_ITEM(__pyx_t_1, 46, __pyx_n_s_Animacy);
    __pyx_v_morph_keys = ((PyObject*)__pyx_t_1);
    __pyx_t_1 = 0;

    /* "spacy/attrs.pyx":174
 *             "Animacy",  # U20
 *         ]
 *         for key in morph_keys:             # <<<<<<<<<<<<<<
 *             if key in stringy_attrs:
 *                 stringy_attrs.pop(key)
 */
    __pyx_t_1 = __pyx_v_morph_keys; __Pyx_INCREF(__pyx_t_1); __pyx_t_6 = 0;
    for (;;) {
      if (__pyx_t_6 >= PyList_GET_SIZE(__pyx_t_1)) break;
      #if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS
      __pyx_t_4 = PyList_GET_ITEM(__pyx_t_1, __pyx_t_6); __Pyx_INCREF(__pyx_t_4); __pyx_t_6++; if (unlikely(0 < 0)) __PYX_ERR(0, 174, __pyx_L1_error)
      #else
      __pyx_t_4 = PySequence_ITEM(__pyx_t_1, __pyx_t_6); __pyx_t_6++; if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 174, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_4);
      #endif
      __Pyx_XDECREF_SET(__pyx_v_key, __pyx_t_4);
      __pyx_t_4 = 0;

      /* "spacy/attrs.pyx":175
 *         ]
 *         for key in morph_keys:
 *             if key in stringy_attrs:             # <<<<<<<<<<<<<<
 *                 stringy_attrs.pop(key)
 *             elif key.lower() in stringy_attrs:
 */
      __pyx_t_2 = (__Pyx_PySequence_ContainsTF(__pyx_v_key, __pyx_v_stringy_attrs, Py_EQ)); if (unlikely(__pyx_t_2 < 0)) __PYX_ERR(0, 175, __pyx_L1_error)
      __pyx_t_3 = (__pyx_t_2 != 0);
      if (__pyx_t_3) {

        /* "spacy/attrs.pyx":176
 *         for key in morph_keys:
 *             if key in stringy_attrs:
 *                 stringy_attrs.pop(key)             # <<<<<<<<<<<<<<
 *             elif key.lower() in stringy_attrs:
 *                 stringy_attrs.pop(key.lower())
 */
        __pyx_t_5 = __Pyx_PyObject_GetAttrStr(__pyx_v_stringy_attrs, __pyx_n_s_pop); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 176, __pyx_L1_error)
        __Pyx_GOTREF(__pyx_t_5);
        __pyx_t_7 = NULL;
        if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_5))) {
          __pyx_t_7 = PyMethod_GET_SELF(__pyx_t_5);
          if (likely(__pyx_t_7)) {
            PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_5);
            __Pyx_INCREF(__pyx_t_7);
            __Pyx_INCREF(function);
            __Pyx_DECREF_SET(__pyx_t_5, function);
          }
        }
        __pyx_t_4 = (__pyx_t_7) ? __Pyx_PyObject_Call2Args(__pyx_t_5, __pyx_t_7, __pyx_v_key) : __Pyx_PyObject_CallOneArg(__pyx_t_5, __pyx_v_key);
        __Pyx_XDECREF(__pyx_t_7); __pyx_t_7 = 0;
        if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 176, __pyx_L1_error)
        __Pyx_GOTREF(__pyx_t_4);
        __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
        __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;

        /* "spacy/attrs.pyx":175
 *         ]
 *         for key in morph_keys:
 *             if key in stringy_attrs:             # <<<<<<<<<<<<<<
 *                 stringy_attrs.pop(key)
 *             elif key.lower() in stringy_attrs:
 */
        goto __pyx_L12;
      }

      /* "spacy/attrs.pyx":177
 *             if key in stringy_attrs:
 *                 stringy_attrs.pop(key)
 *             elif key.lower() in stringy_attrs:             # <<<<<<<<<<<<<<
 *                 stringy_attrs.pop(key.lower())
 *             elif key.upper() in stringy_attrs:
 */
      __pyx_t_5 = __Pyx_PyObject_GetAttrStr(__pyx_v_key, __pyx_n_s_lower); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 177, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_5);
      __pyx_t_7 = NULL;
      if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_5))) {
        __pyx_t_7 = PyMethod_GET_SELF(__pyx_t_5);
        if (likely(__pyx_t_7)) {
          PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_5);
          __Pyx_INCREF(__pyx_t_7);
          __Pyx_INCREF(function);
          __Pyx_DECREF_SET(__pyx_t_5, function);
        }
      }
      __pyx_t_4 = (__pyx_t_7) ? __Pyx_PyObject_CallOneArg(__pyx_t_5, __pyx_t_7) : __Pyx_PyObject_CallNoArg(__pyx_t_5);
      __Pyx_XDECREF(__pyx_t_7); __pyx_t_7 = 0;
      if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 177, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_4);
      __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
      __pyx_t_3 = (__Pyx_PySequence_ContainsTF(__pyx_t_4, __pyx_v_stringy_attrs, Py_EQ)); if (unlikely(__pyx_t_3 < 0)) __PYX_ERR(0, 177, __pyx_L1_error)
      __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
      __pyx_t_2 = (__pyx_t_3 != 0);
      if (__pyx_t_2) {

        /* "spacy/attrs.pyx":178
 *                 stringy_attrs.pop(key)
 *             elif key.lower() in stringy_attrs:
 *                 stringy_attrs.pop(key.lower())             # <<<<<<<<<<<<<<
 *             elif key.upper() in stringy_attrs:
 *                 stringy_attrs.pop(key.upper())
 */
        __pyx_t_5 = __Pyx_PyObject_GetAttrStr(__pyx_v_stringy_attrs, __pyx_n_s_pop); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 178, __pyx_L1_error)
        __Pyx_GOTREF(__pyx_t_5);
        __pyx_t_8 = __Pyx_PyObject_GetAttrStr(__pyx_v_key, __pyx_n_s_lower); if (unlikely(!__pyx_t_8)) __PYX_ERR(0, 178, __pyx_L1_error)
        __Pyx_GOTREF(__pyx_t_8);
        __pyx_t_9 = NULL;
        if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_8))) {
          __pyx_t_9 = PyMethod_GET_SELF(__pyx_t_8);
          if (likely(__pyx_t_9)) {
            PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_8);
            __Pyx_INCREF(__pyx_t_9);
            __Pyx_INCREF(function);
            __Pyx_DECREF_SET(__pyx_t_8, function);
          }
        }
        __pyx_t_7 = (__pyx_t_9) ? __Pyx_PyObject_CallOneArg(__pyx_t_8, __pyx_t_9) : __Pyx_PyObject_CallNoArg(__pyx_t_8);
        __Pyx_XDECREF(__pyx_t_9); __pyx_t_9 = 0;
        if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 178, __pyx_L1_error)
        __Pyx_GOTREF(__pyx_t_7);
        __Pyx_DECREF(__pyx_t_8); __pyx_t_8 = 0;
        __pyx_t_8 = NULL;
        if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_5))) {
          __pyx_t_8 = PyMethod_GET_SELF(__pyx_t_5);
          if (likely(__pyx_t_8)) {
            PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_5);
            __Pyx_INCREF(__pyx_t_8);
            __Pyx_INCREF(function);
            __Pyx_DECREF_SET(__pyx_t_5, function);
          }
        }
        __pyx_t_4 = (__pyx_t_8) ? __Pyx_PyObject_Call2Args(__pyx_t_5, __pyx_t_8, __pyx_t_7) : __Pyx_PyObject_CallOneArg(__pyx_t_5, __pyx_t_7);
        __Pyx_XDECREF(__pyx_t_8); __pyx_t_8 = 0;
        __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;
        if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 178, __pyx_L1_error)
        __Pyx_GOTREF(__pyx_t_4);
        __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
        __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;

        /* "spacy/attrs.pyx":177
 *             if key in stringy_attrs:
 *                 stringy_attrs.pop(key)
 *             elif key.lower() in stringy_attrs:             # <<<<<<<<<<<<<<
 *                 stringy_attrs.pop(key.lower())
 *             elif key.upper() in stringy_attrs:
 */
        goto __pyx_L12;
      }

      /* "spacy/attrs.pyx":179
 *             elif key.lower() in stringy_attrs:
 *                 stringy_attrs.pop(key.lower())
 *             elif key.upper() in stringy_attrs:             # <<<<<<<<<<<<<<
 *                 stringy_attrs.pop(key.upper())
 *     for name, value in stringy_attrs.items():
 */
      __pyx_t_5 = __Pyx_PyObject_GetAttrStr(__pyx_v_key, __pyx_n_s_upper); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 179, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_5);
      __pyx_t_7 = NULL;
      if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_5))) {
        __pyx_t_7 = PyMethod_GET_SELF(__pyx_t_5);
        if (likely(__pyx_t_7)) {
          PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_5);
          __Pyx_INCREF(__pyx_t_7);
          __Pyx_INCREF(function);
          __Pyx_DECREF_SET(__pyx_t_5, function);
        }
      }
      __pyx_t_4 = (__pyx_t_7) ? __Pyx_PyObject_CallOneArg(__pyx_t_5, __pyx_t_7) : __Pyx_PyObject_CallNoArg(__pyx_t_5);
      __Pyx_XDECREF(__pyx_t_7); __pyx_t_7 = 0;
      if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 179, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_4);
      __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
      __pyx_t_2 = (__Pyx_PySequence_ContainsTF(__pyx_t_4, __pyx_v_stringy_attrs, Py_EQ)); if (unlikely(__pyx_t_2 < 0)) __PYX_ERR(0, 179, __pyx_L1_error)
      __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
      __pyx_t_3 = (__pyx_t_2 != 0);
      if (__pyx_t_3) {

        /* "spacy/attrs.pyx":180
 *                 stringy_attrs.pop(key.lower())
 *             elif key.upper() in stringy_attrs:
 *                 stringy_attrs.pop(key.upper())             # <<<<<<<<<<<<<<
 *     for name, value in stringy_attrs.items():
 *         int_key = intify_attr(name)
 */
        __pyx_t_5 = __Pyx_PyObject_GetAttrStr(__pyx_v_stringy_attrs, __pyx_n_s_pop); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 180, __pyx_L1_error)
        __Pyx_GOTREF(__pyx_t_5);
        __pyx_t_8 = __Pyx_PyObject_GetAttrStr(__pyx_v_key, __pyx_n_s_upper); if (unlikely(!__pyx_t_8)) __PYX_ERR(0, 180, __pyx_L1_error)
        __Pyx_GOTREF(__pyx_t_8);
        __pyx_t_9 = NULL;
        if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_8))) {
          __pyx_t_9 = PyMethod_GET_SELF(__pyx_t_8);
          if (likely(__pyx_t_9)) {
            PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_8);
            __Pyx_INCREF(__pyx_t_9);
            __Pyx_INCREF(function);
            __Pyx_DECREF_SET(__pyx_t_8, function);
          }
        }
        __pyx_t_7 = (__pyx_t_9) ? __Pyx_PyObject_CallOneArg(__pyx_t_8, __pyx_t_9) : __Pyx_PyObject_CallNoArg(__pyx_t_8);
        __Pyx_XDECREF(__pyx_t_9); __pyx_t_9 = 0;
        if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 180, __pyx_L1_error)
        __Pyx_GOTREF(__pyx_t_7);
        __Pyx_DECREF(__pyx_t_8); __pyx_t_8 = 0;
        __pyx_t_8 = NULL;
        if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_5))) {
          __pyx_t_8 = PyMethod_GET_SELF(__pyx_t_5);
          if (likely(__pyx_t_8)) {
            PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_5);
            __Pyx_INCREF(__pyx_t_8);
            __Pyx_INCREF(function);
            __Pyx_DECREF_SET(__pyx_t_5, function);
          }
        }
        __pyx_t_4 = (__pyx_t_8) ? __Pyx_PyObject_Call2Args(__pyx_t_5, __pyx_t_8, __pyx_t_7) : __Pyx_PyObject_CallOneArg(__pyx_t_5, __pyx_t_7);
        __Pyx_XDECREF(__pyx_t_8); __pyx_t_8 = 0;
        __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;
        if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 180, __pyx_L1_error)
        __Pyx_GOTREF(__pyx_t_4);
        __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
        __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;

        /* "spacy/attrs.pyx":179
 *             elif key.lower() in stringy_attrs:
 *                 stringy_attrs.pop(key.lower())
 *             elif key.upper() in stringy_attrs:             # <<<<<<<<<<<<<<
 *                 stringy_attrs.pop(key.upper())
 *     for name, value in stringy_attrs.items():
 */
      }
      __pyx_L12:;

      /* "spacy/attrs.pyx":174
 *             "Animacy",  # U20
 *         ]
 *         for key in morph_keys:             # <<<<<<<<<<<<<<
 *             if key in stringy_attrs:
 *                 stringy_attrs.pop(key)
 */
    }
    __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

    /* "spacy/attrs.pyx":112
 *     """
 *     inty_attrs = {}
 *     if _do_deprecated:             # <<<<<<<<<<<<<<
 *         if "F" in stringy_attrs:
 *             stringy_attrs["ORTH"] = stringy_attrs.pop("F")
 */
  }

  /* "spacy/attrs.pyx":181
 *             elif key.upper() in stringy_attrs:
 *                 stringy_attrs.pop(key.upper())
 *     for name, value in stringy_attrs.items():             # <<<<<<<<<<<<<<
 *         int_key = intify_attr(name)
 *         if int_key is not None:
 */
  __pyx_t_4 = __Pyx_PyObject_GetAttrStr(__pyx_v_stringy_attrs, __pyx_n_s_items); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 181, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_4);
  __pyx_t_5 = NULL;
  if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_4))) {
    __pyx_t_5 = PyMethod_GET_SELF(__pyx_t_4);
    if (likely(__pyx_t_5)) {
      PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_4);
      __Pyx_INCREF(__pyx_t_5);
      __Pyx_INCREF(function);
      __Pyx_DECREF_SET(__pyx_t_4, function);
    }
  }
  __pyx_t_1 = (__pyx_t_5) ? __Pyx_PyObject_CallOneArg(__pyx_t_4, __pyx_t_5) : __Pyx_PyObject_CallNoArg(__pyx_t_4);
  __Pyx_XDECREF(__pyx_t_5); __pyx_t_5 = 0;
  if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 181, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
  if (likely(PyList_CheckExact(__pyx_t_1)) || PyTuple_CheckExact(__pyx_t_1)) {
    __pyx_t_4 = __pyx_t_1; __Pyx_INCREF(__pyx_t_4); __pyx_t_6 = 0;
    __pyx_t_10 = NULL;
  } else {
    __pyx_t_6 = -1; __pyx_t_4 = PyObject_GetIter(__pyx_t_1); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 181, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_4);
    __pyx_t_10 = Py_TYPE(__pyx_t_4)->tp_iternext; if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 181, __pyx_L1_error)
  }
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  for (;;) {
    if (likely(!__pyx_t_10)) {
      if (likely(PyList_CheckExact(__pyx_t_4))) {
        if (__pyx_t_6 >= PyList_GET_SIZE(__pyx_t_4)) break;
        #if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS
        __pyx_t_1 = PyList_GET_ITEM(__pyx_t_4, __pyx_t_6); __Pyx_INCREF(__pyx_t_1); __pyx_t_6++; if (unlikely(0 < 0)) __PYX_ERR(0, 181, __pyx_L1_error)
        #else
        __pyx_t_1 = PySequence_ITEM(__pyx_t_4, __pyx_t_6); __pyx_t_6++; if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 181, __pyx_L1_error)
        __Pyx_GOTREF(__pyx_t_1);
        #endif
      } else {
        if (__pyx_t_6 >= PyTuple_GET_SIZE(__pyx_t_4)) break;
        #if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS
        __pyx_t_1 = PyTuple_GET_ITEM(__pyx_t_4, __pyx_t_6); __Pyx_INCREF(__pyx_t_1); __pyx_t_6++; if (unlikely(0 < 0)) __PYX_ERR(0, 181, __pyx_L1_error)
        #else
        __pyx_t_1 = PySequence_ITEM(__pyx_t_4, __pyx_t_6); __pyx_t_6++; if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 181, __pyx_L1_error)
        __Pyx_GOTREF(__pyx_t_1);
        #endif
      }
    } else {
      __pyx_t_1 = __pyx_t_10(__pyx_t_4);
      if (unlikely(!__pyx_t_1)) {
        PyObject* exc_type = PyErr_Occurred();
        if (exc_type) {
          if (likely(__Pyx_PyErr_GivenExceptionMatches(exc_type, PyExc_StopIteration))) PyErr_Clear();
          else __PYX_ERR(0, 181, __pyx_L1_error)
        }
        break;
      }
      __Pyx_GOTREF(__pyx_t_1);
    }
    if ((likely(PyTuple_CheckExact(__pyx_t_1))) || (PyList_CheckExact(__pyx_t_1))) {
      PyObject* sequence = __pyx_t_1;
      Py_ssize_t size = __Pyx_PySequence_SIZE(sequence);
      if (unlikely(size != 2)) {
        if (size > 2) __Pyx_RaiseTooManyValuesError(2);
        else if (size >= 0) __Pyx_RaiseNeedMoreValuesError(size);
        __PYX_ERR(0, 181, __pyx_L1_error)
      }
      #if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS
      if (likely(PyTuple_CheckExact(sequence))) {
        __pyx_t_5 = PyTuple_GET_ITEM(sequence, 0); 
        __pyx_t_7 = PyTuple_GET_ITEM(sequence, 1); 
      } else {
        __pyx_t_5 = PyList_GET_ITEM(sequence, 0); 
        __pyx_t_7 = PyList_GET_ITEM(sequence, 1); 
      }
      __Pyx_INCREF(__pyx_t_5);
      __Pyx_INCREF(__pyx_t_7);
      #else
      __pyx_t_5 = PySequence_ITEM(sequence, 0); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 181, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_5);
      __pyx_t_7 = PySequence_ITEM(sequence, 1); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 181, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_7);
      #endif
      __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
    } else {
      Py_ssize_t index = -1;
      __pyx_t_8 = PyObject_GetIter(__pyx_t_1); if (unlikely(!__pyx_t_8)) __PYX_ERR(0, 181, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_8);
      __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
      __pyx_t_11 = Py_TYPE(__pyx_t_8)->tp_iternext;
      index = 0; __pyx_t_5 = __pyx_t_11(__pyx_t_8); if (unlikely(!__pyx_t_5)) goto __pyx_L15_unpacking_failed;
      __Pyx_GOTREF(__pyx_t_5);
      index = 1; __pyx_t_7 = __pyx_t_11(__pyx_t_8); if (unlikely(!__pyx_t_7)) goto __pyx_L15_unpacking_failed;
      __Pyx_GOTREF(__pyx_t_7);
      if (__Pyx_IternextUnpackEndCheck(__pyx_t_11(__pyx_t_8), 2) < 0) __PYX_ERR(0, 181, __pyx_L1_error)
      __pyx_t_11 = NULL;
      __Pyx_DECREF(__pyx_t_8); __pyx_t_8 = 0;
      goto __pyx_L16_unpacking_done;
      __pyx_L15_unpacking_failed:;
      __Pyx_DECREF(__pyx_t_8); __pyx_t_8 = 0;
      __pyx_t_11 = NULL;
      if (__Pyx_IterFinish() == 0) __Pyx_RaiseNeedMoreValuesError(index);
      __PYX_ERR(0, 181, __pyx_L1_error)
      __pyx_L16_unpacking_done:;
    }
    __Pyx_XDECREF_SET(__pyx_v_name, __pyx_t_5);
    __pyx_t_5 = 0;
    __Pyx_XDECREF_SET(__pyx_v_value, __pyx_t_7);
    __pyx_t_7 = 0;

    /* "spacy/attrs.pyx":182
 *                 stringy_attrs.pop(key.upper())
 *     for name, value in stringy_attrs.items():
 *         int_key = intify_attr(name)             # <<<<<<<<<<<<<<
 *         if int_key is not None:
 *             if int_key == ENT_IOB:
 */
    __Pyx_GetModuleGlobalName(__pyx_t_7, __pyx_n_s_intify_attr); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 182, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_7);
    __pyx_t_5 = NULL;
    if (CYTHON_UNPACK_METHODS && unlikely(PyMethod_Check(__pyx_t_7))) {
      __pyx_t_5 = PyMethod_GET_SELF(__pyx_t_7);
      if (likely(__pyx_t_5)) {
        PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_7);
        __Pyx_INCREF(__pyx_t_5);
        __Pyx_INCREF(function);
        __Pyx_DECREF_SET(__pyx_t_7, function);
      }
    }
    __pyx_t_1 = (__pyx_t_5) ? __Pyx_PyObject_Call2Args(__pyx_t_7, __pyx_t_5, __pyx_v_name) : __Pyx_PyObject_CallOneArg(__pyx_t_7, __pyx_v_name);
    __Pyx_XDECREF(__pyx_t_5); __pyx_t_5 = 0;
    if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 182, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;
    __Pyx_XDECREF_SET(__pyx_v_int_key, __pyx_t_1);
    __pyx_t_1 = 0;

    /* "spacy/attrs.pyx":183
 *     for name, value in stringy_attrs.items():
 *         int_key = intify_attr(name)
 *         if int_key is not None:             # <<<<<<<<<<<<<<
 *             if int_key == ENT_IOB:
 *                 if value in IOB_STRINGS:
 */
    __pyx_t_3 = (__pyx_v_int_key != Py_None);
    __pyx_t_2 = (__pyx_t_3 != 0);
    if (__pyx_t_2) {

      /* "spacy/attrs.pyx":184
 *         int_key = intify_attr(name)
 *         if int_key is not None:
 *             if int_key == ENT_IOB:             # <<<<<<<<<<<<<<
 *                 if value in IOB_STRINGS:
 *                     value = IOB_STRINGS.index(value)
 */
      __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_ENT_IOB); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 184, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_1);
      __pyx_t_7 = PyObject_RichCompare(__pyx_v_int_key, __pyx_t_1, Py_EQ); __Pyx_XGOTREF(__pyx_t_7); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 184, __pyx_L1_error)
      __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
      __pyx_t_2 = __Pyx_PyObject_IsTrue(__pyx_t_7); if (unlikely(__pyx_t_2 < 0)) __PYX_ERR(0, 184, __pyx_L1_error)
      __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;
      if (__pyx_t_2) {

        /* "spacy/attrs.pyx":185
 *         if int_key is not None:
 *             if int_key == ENT_IOB:
 *                 if value in IOB_STRINGS:             # <<<<<<<<<<<<<<
 *                     value = IOB_STRINGS.index(value)
 *                 elif isinstance(value, str):
 */
        __Pyx_GetModuleGlobalName(__pyx_t_7, __pyx_n_s_IOB_STRINGS); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 185, __pyx_L1_error)
        __Pyx_GOTREF(__pyx_t_7);
        __pyx_t_2 = (__Pyx_PySequence_ContainsTF(__pyx_v_value, __pyx_t_7, Py_EQ)); if (unlikely(__pyx_t_2 < 0)) __PYX_ERR(0, 185, __pyx_L1_error)
        __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;
        __pyx_t_3 = (__pyx_t_2 != 0);
        if (__pyx_t_3) {

          /* "spacy/attrs.pyx":186
 *             if int_key == ENT_IOB:
 *                 if value in IOB_STRINGS:
 *                     value = IOB_STRINGS.index(value)             # <<<<<<<<<<<<<<
 *                 elif isinstance(value, str):
 *                     raise ValueError(Errors.E1025.format(value=value))
 */
          __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_IOB_STRINGS); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 186, __pyx_L1_error)
          __Pyx_GOTREF(__pyx_t_1);
          __pyx_t_5 = __Pyx_PyObject_GetAttrStr(__pyx_t_1, __pyx_n_s_index); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 186, __pyx_L1_error)
          __Pyx_GOTREF(__pyx_t_5);
          __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
          __pyx_t_1 = NULL;
          if (CYTHON_UNPACK_METHODS && unlikely(PyMethod_Check(__pyx_t_5))) {
            __pyx_t_1 = PyMethod_GET_SELF(__pyx_t_5);
            if (likely(__pyx_t_1)) {
              PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_5);
              __Pyx_INCREF(__pyx_t_1);
              __Pyx_INCREF(function);
              __Pyx_DECREF_SET(__pyx_t_5, function);
            }
          }
          __pyx_t_7 = (__pyx_t_1) ? __Pyx_PyObject_Call2Args(__pyx_t_5, __pyx_t_1, __pyx_v_value) : __Pyx_PyObject_CallOneArg(__pyx_t_5, __pyx_v_value);
          __Pyx_XDECREF(__pyx_t_1); __pyx_t_1 = 0;
          if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 186, __pyx_L1_error)
          __Pyx_GOTREF(__pyx_t_7);
          __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
          __Pyx_DECREF_SET(__pyx_v_value, __pyx_t_7);
          __pyx_t_7 = 0;

          /* "spacy/attrs.pyx":185
 *         if int_key is not None:
 *             if int_key == ENT_IOB:
 *                 if value in IOB_STRINGS:             # <<<<<<<<<<<<<<
 *                     value = IOB_STRINGS.index(value)
 *                 elif isinstance(value, str):
 */
          goto __pyx_L19;
        }

        /* "spacy/attrs.pyx":187
 *                 if value in IOB_STRINGS:
 *                     value = IOB_STRINGS.index(value)
 *                 elif isinstance(value, str):             # <<<<<<<<<<<<<<
 *                     raise ValueError(Errors.E1025.format(value=value))
 *             if strings_map is not None and isinstance(value, str):
 */
        __pyx_t_3 = PyString_Check(__pyx_v_value); 
        __pyx_t_2 = (__pyx_t_3 != 0);
        if (unlikely(__pyx_t_2)) {

          /* "spacy/attrs.pyx":188
 *                     value = IOB_STRINGS.index(value)
 *                 elif isinstance(value, str):
 *                     raise ValueError(Errors.E1025.format(value=value))             # <<<<<<<<<<<<<<
 *             if strings_map is not None and isinstance(value, str):
 *                 if hasattr(strings_map, "add"):
 */
          __Pyx_GetModuleGlobalName(__pyx_t_7, __pyx_n_s_Errors); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 188, __pyx_L1_error)
          __Pyx_GOTREF(__pyx_t_7);
          __pyx_t_5 = __Pyx_PyObject_GetAttrStr(__pyx_t_7, __pyx_n_s_E1025); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 188, __pyx_L1_error)
          __Pyx_GOTREF(__pyx_t_5);
          __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;
          __pyx_t_7 = __Pyx_PyObject_GetAttrStr(__pyx_t_5, __pyx_n_s_format); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 188, __pyx_L1_error)
          __Pyx_GOTREF(__pyx_t_7);
          __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
          __pyx_t_5 = __Pyx_PyDict_NewPresized(1); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 188, __pyx_L1_error)
          __Pyx_GOTREF(__pyx_t_5);
          if (PyDict_SetItem(__pyx_t_5, __pyx_n_s_value, __pyx_v_value) < 0) __PYX_ERR(0, 188, __pyx_L1_error)
          __pyx_t_1 = __Pyx_PyObject_Call(__pyx_t_7, __pyx_empty_tuple, __pyx_t_5); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 188, __pyx_L1_error)
          __Pyx_GOTREF(__pyx_t_1);
          __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;
          __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
          __pyx_t_5 = __Pyx_PyObject_CallOneArg(__pyx_builtin_ValueError, __pyx_t_1); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 188, __pyx_L1_error)
          __Pyx_GOTREF(__pyx_t_5);
          __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
          __Pyx_Raise(__pyx_t_5, 0, 0, 0);
          __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
          __PYX_ERR(0, 188, __pyx_L1_error)

          /* "spacy/attrs.pyx":187
 *                 if value in IOB_STRINGS:
 *                     value = IOB_STRINGS.index(value)
 *                 elif isinstance(value, str):             # <<<<<<<<<<<<<<
 *                     raise ValueError(Errors.E1025.format(value=value))
 *             if strings_map is not None and isinstance(value, str):
 */
        }
        __pyx_L19:;

        /* "spacy/attrs.pyx":184
 *         int_key = intify_attr(name)
 *         if int_key is not None:
 *             if int_key == ENT_IOB:             # <<<<<<<<<<<<<<
 *                 if value in IOB_STRINGS:
 *                     value = IOB_STRINGS.index(value)
 */
      }

      /* "spacy/attrs.pyx":189
 *                 elif isinstance(value, str):
 *                     raise ValueError(Errors.E1025.format(value=value))
 *             if strings_map is not None and isinstance(value, str):             # <<<<<<<<<<<<<<
 *                 if hasattr(strings_map, "add"):
 *                     value = strings_map.add(value)
 */
      __pyx_t_3 = (__pyx_v_strings_map != Py_None);
      __pyx_t_12 = (__pyx_t_3 != 0);
      if (__pyx_t_12) {
      } else {
        __pyx_t_2 = __pyx_t_12;
        goto __pyx_L21_bool_binop_done;
      }
      __pyx_t_12 = PyString_Check(__pyx_v_value); 
      __pyx_t_3 = (__pyx_t_12 != 0);
      __pyx_t_2 = __pyx_t_3;
      __pyx_L21_bool_binop_done:;
      if (__pyx_t_2) {

        /* "spacy/attrs.pyx":190
 *                     raise ValueError(Errors.E1025.format(value=value))
 *             if strings_map is not None and isinstance(value, str):
 *                 if hasattr(strings_map, "add"):             # <<<<<<<<<<<<<<
 *                     value = strings_map.add(value)
 *                 else:
 */
        __pyx_t_2 = __Pyx_HasAttr(__pyx_v_strings_map, __pyx_n_s_add); if (unlikely(__pyx_t_2 == ((int)-1))) __PYX_ERR(0, 190, __pyx_L1_error)
        __pyx_t_3 = (__pyx_t_2 != 0);
        if (__pyx_t_3) {

          /* "spacy/attrs.pyx":191
 *             if strings_map is not None and isinstance(value, str):
 *                 if hasattr(strings_map, "add"):
 *                     value = strings_map.add(value)             # <<<<<<<<<<<<<<
 *                 else:
 *                     value = strings_map[value]
 */
          __pyx_t_1 = __Pyx_PyObject_GetAttrStr(__pyx_v_strings_map, __pyx_n_s_add); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 191, __pyx_L1_error)
          __Pyx_GOTREF(__pyx_t_1);
          __pyx_t_7 = NULL;
          if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_1))) {
            __pyx_t_7 = PyMethod_GET_SELF(__pyx_t_1);
            if (likely(__pyx_t_7)) {
              PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_1);
              __Pyx_INCREF(__pyx_t_7);
              __Pyx_INCREF(function);
              __Pyx_DECREF_SET(__pyx_t_1, function);
            }
          }
          __pyx_t_5 = (__pyx_t_7) ? __Pyx_PyObject_Call2Args(__pyx_t_1, __pyx_t_7, __pyx_v_value) : __Pyx_PyObject_CallOneArg(__pyx_t_1, __pyx_v_value);
          __Pyx_XDECREF(__pyx_t_7); __pyx_t_7 = 0;
          if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 191, __pyx_L1_error)
          __Pyx_GOTREF(__pyx_t_5);
          __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
          __Pyx_DECREF_SET(__pyx_v_value, __pyx_t_5);
          __pyx_t_5 = 0;

          /* "spacy/attrs.pyx":190
 *                     raise ValueError(Errors.E1025.format(value=value))
 *             if strings_map is not None and isinstance(value, str):
 *                 if hasattr(strings_map, "add"):             # <<<<<<<<<<<<<<
 *                     value = strings_map.add(value)
 *                 else:
 */
          goto __pyx_L23;
        }

        /* "spacy/attrs.pyx":193
 *                     value = strings_map.add(value)
 *                 else:
 *                     value = strings_map[value]             # <<<<<<<<<<<<<<
 *             inty_attrs[int_key] = value
 *     return inty_attrs
 */
        /*else*/ {
          __pyx_t_5 = __Pyx_PyObject_GetItem(__pyx_v_strings_map, __pyx_v_value); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 193, __pyx_L1_error)
          __Pyx_GOTREF(__pyx_t_5);
          __Pyx_DECREF_SET(__pyx_v_value, __pyx_t_5);
          __pyx_t_5 = 0;
        }
        __pyx_L23:;

        /* "spacy/attrs.pyx":189
 *                 elif isinstance(value, str):
 *                     raise ValueError(Errors.E1025.format(value=value))
 *             if strings_map is not None and isinstance(value, str):             # <<<<<<<<<<<<<<
 *                 if hasattr(strings_map, "add"):
 *                     value = strings_map.add(value)
 */
      }

      /* "spacy/attrs.pyx":194
 *                 else:
 *                     value = strings_map[value]
 *             inty_attrs[int_key] = value             # <<<<<<<<<<<<<<
 *     return inty_attrs
 * 
 */
      if (unlikely(PyDict_SetItem(__pyx_v_inty_attrs, __pyx_v_int_key, __pyx_v_value) < 0)) __PYX_ERR(0, 194, __pyx_L1_error)

      /* "spacy/attrs.pyx":183
 *     for name, value in stringy_attrs.items():
 *         int_key = intify_attr(name)
 *         if int_key is not None:             # <<<<<<<<<<<<<<
 *             if int_key == ENT_IOB:
 *                 if value in IOB_STRINGS:
 */
    }

    /* "spacy/attrs.pyx":181
 *             elif key.upper() in stringy_attrs:
 *                 stringy_attrs.pop(key.upper())
 *     for name, value in stringy_attrs.items():             # <<<<<<<<<<<<<<
 *         int_key = intify_attr(name)
 *         if int_key is not None:
 */
  }
  __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;

  /* "spacy/attrs.pyx":195
 *                     value = strings_map[value]
 *             inty_attrs[int_key] = value
 *     return inty_attrs             # <<<<<<<<<<<<<<
 * 
 * 
 */
  __Pyx_XDECREF(__pyx_r);
  __Pyx_INCREF(__pyx_v_inty_attrs);
  __pyx_r = __pyx_v_inty_attrs;
  goto __pyx_L0;

  /* "spacy/attrs.pyx":100
 * 
 * 
 * def intify_attrs(stringy_attrs, strings_map=None, _do_deprecated=False):             # <<<<<<<<<<<<<<
 *     """
 *     Normalize a dictionary of attributes, converting them to ints.
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_XDECREF(__pyx_t_4);
  __Pyx_XDECREF(__pyx_t_5);
  __Pyx_XDECREF(__pyx_t_7);
  __Pyx_XDECREF(__pyx_t_8);
  __Pyx_XDECREF(__pyx_t_9);
  __Pyx_AddTraceback("spacy.attrs.intify_attrs", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = NULL;
  __pyx_L0:;
  __Pyx_XDECREF(__pyx_v_inty_attrs);
  __Pyx_XDECREF(__pyx_v_morphs);
  __Pyx_XDECREF(__pyx_v_morph_keys);
  __Pyx_XDECREF(__pyx_v_key);
  __Pyx_XDECREF(__pyx_v_name);
  __Pyx_XDECREF(__pyx_v_value);
  __Pyx_XDECREF(__pyx_v_int_key);
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "spacy/attrs.pyx":198
 * 
 * 
 * def intify_attr(name):             # <<<<<<<<<<<<<<
 *     """
 *     Normalize an attribute name, converting it to int.
 */

/* Python wrapper */
static PyObject *__pyx_pw_5spacy_5attrs_3intify_attr(PyObject *__pyx_self, PyObject *__pyx_v_name); /*proto*/
static char __pyx_doc_5spacy_5attrs_2intify_attr[] = "intify_attr(name)\n\n    Normalize an attribute name, converting it to int.\n\n    stringy_attr (string): Attribute string name. Can also be int (will then be left unchanged)\n    RETURNS (int): int representation of the attribute, or None if it couldn't be converted.\n    ";
static PyMethodDef __pyx_mdef_5spacy_5attrs_3intify_attr = {"intify_attr", (PyCFunction)__pyx_pw_5spacy_5attrs_3intify_attr, METH_O, __pyx_doc_5spacy_5attrs_2intify_attr};
static PyObject *__pyx_pw_5spacy_5attrs_3intify_attr(PyObject *__pyx_self, PyObject *__pyx_v_name) {
  PyObject *__pyx_r = 0;
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("intify_attr (wrapper)", 0);
  __pyx_r = __pyx_pf_5spacy_5attrs_2intify_attr(__pyx_self, ((PyObject *)__pyx_v_name));

  /* function exit code */
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

static PyObject *__pyx_pf_5spacy_5attrs_2intify_attr(CYTHON_UNUSED PyObject *__pyx_self, PyObject *__pyx_v_name) {
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  int __pyx_t_1;
  int __pyx_t_2;
  PyObject *__pyx_t_3 = NULL;
  PyObject *__pyx_t_4 = NULL;
  PyObject *__pyx_t_5 = NULL;
  PyObject *__pyx_t_6 = NULL;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;
  __Pyx_RefNannySetupContext("intify_attr", 0);

  /* "spacy/attrs.pyx":205
 *     RETURNS (int): int representation of the attribute, or None if it couldn't be converted.
 *     """
 *     if isinstance(name, int):             # <<<<<<<<<<<<<<
 *         return name
 *     elif name in IDS:
 */
  __pyx_t_1 = PyInt_Check(__pyx_v_name); 
  __pyx_t_2 = (__pyx_t_1 != 0);
  if (__pyx_t_2) {

    /* "spacy/attrs.pyx":206
 *     """
 *     if isinstance(name, int):
 *         return name             # <<<<<<<<<<<<<<
 *     elif name in IDS:
 *         return IDS[name]
 */
    __Pyx_XDECREF(__pyx_r);
    __Pyx_INCREF(__pyx_v_name);
    __pyx_r = __pyx_v_name;
    goto __pyx_L0;

    /* "spacy/attrs.pyx":205
 *     RETURNS (int): int representation of the attribute, or None if it couldn't be converted.
 *     """
 *     if isinstance(name, int):             # <<<<<<<<<<<<<<
 *         return name
 *     elif name in IDS:
 */
  }

  /* "spacy/attrs.pyx":207
 *     if isinstance(name, int):
 *         return name
 *     elif name in IDS:             # <<<<<<<<<<<<<<
 *         return IDS[name]
 *     elif name.upper() in IDS:
 */
  __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_IDS); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 207, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __pyx_t_2 = (__Pyx_PySequence_ContainsTF(__pyx_v_name, __pyx_t_3, Py_EQ)); if (unlikely(__pyx_t_2 < 0)) __PYX_ERR(0, 207, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_1 = (__pyx_t_2 != 0);
  if (__pyx_t_1) {

    /* "spacy/attrs.pyx":208
 *         return name
 *     elif name in IDS:
 *         return IDS[name]             # <<<<<<<<<<<<<<
 *     elif name.upper() in IDS:
 *         return IDS[name.upper()]
 */
    __Pyx_XDECREF(__pyx_r);
    __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_IDS); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 208, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_3);
    __pyx_t_4 = __Pyx_PyObject_GetItem(__pyx_t_3, __pyx_v_name); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 208, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_4);
    __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
    __pyx_r = __pyx_t_4;
    __pyx_t_4 = 0;
    goto __pyx_L0;

    /* "spacy/attrs.pyx":207
 *     if isinstance(name, int):
 *         return name
 *     elif name in IDS:             # <<<<<<<<<<<<<<
 *         return IDS[name]
 *     elif name.upper() in IDS:
 */
  }

  /* "spacy/attrs.pyx":209
 *     elif name in IDS:
 *         return IDS[name]
 *     elif name.upper() in IDS:             # <<<<<<<<<<<<<<
 *         return IDS[name.upper()]
 *     return None
 */
  __pyx_t_3 = __Pyx_PyObject_GetAttrStr(__pyx_v_name, __pyx_n_s_upper); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 209, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __pyx_t_5 = NULL;
  if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_3))) {
    __pyx_t_5 = PyMethod_GET_SELF(__pyx_t_3);
    if (likely(__pyx_t_5)) {
      PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_3);
      __Pyx_INCREF(__pyx_t_5);
      __Pyx_INCREF(function);
      __Pyx_DECREF_SET(__pyx_t_3, function);
    }
  }
  __pyx_t_4 = (__pyx_t_5) ? __Pyx_PyObject_CallOneArg(__pyx_t_3, __pyx_t_5) : __Pyx_PyObject_CallNoArg(__pyx_t_3);
  __Pyx_XDECREF(__pyx_t_5); __pyx_t_5 = 0;
  if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 209, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_4);
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_IDS); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 209, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __pyx_t_1 = (__Pyx_PySequence_ContainsTF(__pyx_t_4, __pyx_t_3, Py_EQ)); if (unlikely(__pyx_t_1 < 0)) __PYX_ERR(0, 209, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_2 = (__pyx_t_1 != 0);
  if (__pyx_t_2) {

    /* "spacy/attrs.pyx":210
 *         return IDS[name]
 *     elif name.upper() in IDS:
 *         return IDS[name.upper()]             # <<<<<<<<<<<<<<
 *     return None
 */
    __Pyx_XDECREF(__pyx_r);
    __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_IDS); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 210, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_3);
    __pyx_t_5 = __Pyx_PyObject_GetAttrStr(__pyx_v_name, __pyx_n_s_upper); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 210, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_5);
    __pyx_t_6 = NULL;
    if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_5))) {
      __pyx_t_6 = PyMethod_GET_SELF(__pyx_t_5);
      if (likely(__pyx_t_6)) {
        PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_5);
        __Pyx_INCREF(__pyx_t_6);
        __Pyx_INCREF(function);
        __Pyx_DECREF_SET(__pyx_t_5, function);
      }
    }
    __pyx_t_4 = (__pyx_t_6) ? __Pyx_PyObject_CallOneArg(__pyx_t_5, __pyx_t_6) : __Pyx_PyObject_CallNoArg(__pyx_t_5);
    __Pyx_XDECREF(__pyx_t_6); __pyx_t_6 = 0;
    if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 210, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_4);
    __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
    __pyx_t_5 = __Pyx_PyObject_GetItem(__pyx_t_3, __pyx_t_4); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 210, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_5);
    __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
    __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
    __pyx_r = __pyx_t_5;
    __pyx_t_5 = 0;
    goto __pyx_L0;

    /* "spacy/attrs.pyx":209
 *     elif name in IDS:
 *         return IDS[name]
 *     elif name.upper() in IDS:             # <<<<<<<<<<<<<<
 *         return IDS[name.upper()]
 *     return None
 */
  }

  /* "spacy/attrs.pyx":211
 *     elif name.upper() in IDS:
 *         return IDS[name.upper()]
 *     return None             # <<<<<<<<<<<<<<
 */
  __Pyx_XDECREF(__pyx_r);
  __pyx_r = Py_None; __Pyx_INCREF(Py_None);
  goto __pyx_L0;

  /* "spacy/attrs.pyx":198
 * 
 * 
 * def intify_attr(name):             # <<<<<<<<<<<<<<
 *     """
 *     Normalize an attribute name, converting it to int.
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_3);
  __Pyx_XDECREF(__pyx_t_4);
  __Pyx_XDECREF(__pyx_t_5);
  __Pyx_XDECREF(__pyx_t_6);
  __Pyx_AddTraceback("spacy.attrs.intify_attr", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = NULL;
  __pyx_L0:;
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

static PyMethodDef __pyx_methods[] = {
  {0, 0, 0, 0}
};

#if PY_MAJOR_VERSION >= 3
#if CYTHON_PEP489_MULTI_PHASE_INIT
static PyObject* __pyx_pymod_create(PyObject *spec, PyModuleDef *def); /*proto*/
static int __pyx_pymod_exec_attrs(PyObject* module); /*proto*/
static PyModuleDef_Slot __pyx_moduledef_slots[] = {
  {Py_mod_create, (void*)__pyx_pymod_create},
  {Py_mod_exec, (void*)__pyx_pymod_exec_attrs},
  {0, NULL}
};
#endif

static struct PyModuleDef __pyx_moduledef = {
    PyModuleDef_HEAD_INIT,
    "attrs",
    0, /* m_doc */
  #if CYTHON_PEP489_MULTI_PHASE_INIT
    0, /* m_size */
  #else
    -1, /* m_size */
  #endif
    __pyx_methods /* m_methods */,
  #if CYTHON_PEP489_MULTI_PHASE_INIT
    __pyx_moduledef_slots, /* m_slots */
  #else
    NULL, /* m_reload */
  #endif
    NULL, /* m_traverse */
    NULL, /* m_clear */
    NULL /* m_free */
};
#endif
#ifndef CYTHON_SMALL_CODE
#if defined(__clang__)
    #define CYTHON_SMALL_CODE
#elif defined(__GNUC__) && (__GNUC__ > 4 || (__GNUC__ == 4 && __GNUC_MINOR__ >= 3))
    #define CYTHON_SMALL_CODE __attribute__((cold))
#else
    #define CYTHON_SMALL_CODE
#endif
#endif

static __Pyx_StringTabEntry __pyx_string_tab[] = {
  {&__pyx_kp_s_, __pyx_k_, sizeof(__pyx_k_), 0, 0, 1, 0},
  {&__pyx_n_s_Abbr, __pyx_k_Abbr, sizeof(__pyx_k_Abbr), 0, 0, 1, 1},
  {&__pyx_n_s_AdjType, __pyx_k_AdjType, sizeof(__pyx_k_AdjType), 0, 0, 1, 1},
  {&__pyx_n_s_AdpType, __pyx_k_AdpType, sizeof(__pyx_k_AdpType), 0, 0, 1, 1},
  {&__pyx_n_s_AdvType, __pyx_k_AdvType, sizeof(__pyx_k_AdvType), 0, 0, 1, 1},
  {&__pyx_n_s_Animacy, __pyx_k_Animacy, sizeof(__pyx_k_Animacy), 0, 0, 1, 1},
  {&__pyx_n_s_Aspect, __pyx_k_Aspect, sizeof(__pyx_k_Aspect), 0, 0, 1, 1},
  {&__pyx_n_s_B, __pyx_k_B, sizeof(__pyx_k_B), 0, 0, 1, 1},
  {&__pyx_n_s_Case, __pyx_k_Case, sizeof(__pyx_k_Case), 0, 0, 1, 1},
  {&__pyx_n_s_ConjType, __pyx_k_ConjType, sizeof(__pyx_k_ConjType), 0, 0, 1, 1},
  {&__pyx_n_s_DEP, __pyx_k_DEP, sizeof(__pyx_k_DEP), 0, 0, 1, 1},
  {&__pyx_n_s_Degree, __pyx_k_Degree, sizeof(__pyx_k_Degree), 0, 0, 1, 1},
  {&__pyx_n_s_Derivation, __pyx_k_Derivation, sizeof(__pyx_k_Derivation), 0, 0, 1, 1},
  {&__pyx_n_s_E1025, __pyx_k_E1025, sizeof(__pyx_k_E1025), 0, 0, 1, 1},
  {&__pyx_n_s_ENT_ID, __pyx_k_ENT_ID, sizeof(__pyx_k_ENT_ID), 0, 0, 1, 1},
  {&__pyx_n_s_ENT_IOB, __pyx_k_ENT_IOB, sizeof(__pyx_k_ENT_IOB), 0, 0, 1, 1},
  {&__pyx_n_s_ENT_KB_ID, __pyx_k_ENT_KB_ID, sizeof(__pyx_k_ENT_KB_ID), 0, 0, 1, 1},
  {&__pyx_n_s_ENT_TYPE, __pyx_k_ENT_TYPE, sizeof(__pyx_k_ENT_TYPE), 0, 0, 1, 1},
  {&__pyx_n_s_Echo, __pyx_k_Echo, sizeof(__pyx_k_Echo), 0, 0, 1, 1},
  {&__pyx_n_s_Errors, __pyx_k_Errors, sizeof(__pyx_k_Errors), 0, 0, 1, 1},
  {&__pyx_n_s_F, __pyx_k_F, sizeof(__pyx_k_F), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG19, __pyx_k_FLAG19, sizeof(__pyx_k_FLAG19), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG20, __pyx_k_FLAG20, sizeof(__pyx_k_FLAG20), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG21, __pyx_k_FLAG21, sizeof(__pyx_k_FLAG21), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG22, __pyx_k_FLAG22, sizeof(__pyx_k_FLAG22), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG23, __pyx_k_FLAG23, sizeof(__pyx_k_FLAG23), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG24, __pyx_k_FLAG24, sizeof(__pyx_k_FLAG24), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG25, __pyx_k_FLAG25, sizeof(__pyx_k_FLAG25), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG26, __pyx_k_FLAG26, sizeof(__pyx_k_FLAG26), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG27, __pyx_k_FLAG27, sizeof(__pyx_k_FLAG27), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG28, __pyx_k_FLAG28, sizeof(__pyx_k_FLAG28), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG29, __pyx_k_FLAG29, sizeof(__pyx_k_FLAG29), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG30, __pyx_k_FLAG30, sizeof(__pyx_k_FLAG30), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG31, __pyx_k_FLAG31, sizeof(__pyx_k_FLAG31), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG32, __pyx_k_FLAG32, sizeof(__pyx_k_FLAG32), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG33, __pyx_k_FLAG33, sizeof(__pyx_k_FLAG33), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG34, __pyx_k_FLAG34, sizeof(__pyx_k_FLAG34), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG35, __pyx_k_FLAG35, sizeof(__pyx_k_FLAG35), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG36, __pyx_k_FLAG36, sizeof(__pyx_k_FLAG36), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG37, __pyx_k_FLAG37, sizeof(__pyx_k_FLAG37), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG38, __pyx_k_FLAG38, sizeof(__pyx_k_FLAG38), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG39, __pyx_k_FLAG39, sizeof(__pyx_k_FLAG39), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG40, __pyx_k_FLAG40, sizeof(__pyx_k_FLAG40), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG41, __pyx_k_FLAG41, sizeof(__pyx_k_FLAG41), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG42, __pyx_k_FLAG42, sizeof(__pyx_k_FLAG42), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG43, __pyx_k_FLAG43, sizeof(__pyx_k_FLAG43), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG44, __pyx_k_FLAG44, sizeof(__pyx_k_FLAG44), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG45, __pyx_k_FLAG45, sizeof(__pyx_k_FLAG45), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG46, __pyx_k_FLAG46, sizeof(__pyx_k_FLAG46), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG47, __pyx_k_FLAG47, sizeof(__pyx_k_FLAG47), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG48, __pyx_k_FLAG48, sizeof(__pyx_k_FLAG48), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG49, __pyx_k_FLAG49, sizeof(__pyx_k_FLAG49), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG50, __pyx_k_FLAG50, sizeof(__pyx_k_FLAG50), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG51, __pyx_k_FLAG51, sizeof(__pyx_k_FLAG51), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG52, __pyx_k_FLAG52, sizeof(__pyx_k_FLAG52), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG53, __pyx_k_FLAG53, sizeof(__pyx_k_FLAG53), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG54, __pyx_k_FLAG54, sizeof(__pyx_k_FLAG54), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG55, __pyx_k_FLAG55, sizeof(__pyx_k_FLAG55), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG56, __pyx_k_FLAG56, sizeof(__pyx_k_FLAG56), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG57, __pyx_k_FLAG57, sizeof(__pyx_k_FLAG57), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG58, __pyx_k_FLAG58, sizeof(__pyx_k_FLAG58), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG59, __pyx_k_FLAG59, sizeof(__pyx_k_FLAG59), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG60, __pyx_k_FLAG60, sizeof(__pyx_k_FLAG60), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG61, __pyx_k_FLAG61, sizeof(__pyx_k_FLAG61), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG62, __pyx_k_FLAG62, sizeof(__pyx_k_FLAG62), 0, 0, 1, 1},
  {&__pyx_n_s_FLAG63, __pyx_k_FLAG63, sizeof(__pyx_k_FLAG63), 0, 0, 1, 1},
  {&__pyx_n_s_Foreign, __pyx_k_Foreign, sizeof(__pyx_k_Foreign), 0, 0, 1, 1},
  {&__pyx_n_s_Gender, __pyx_k_Gender, sizeof(__pyx_k_Gender), 0, 0, 1, 1},
  {&__pyx_n_s_HEAD, __pyx_k_HEAD, sizeof(__pyx_k_HEAD), 0, 0, 1, 1},
  {&__pyx_n_s_Hyph, __pyx_k_Hyph, sizeof(__pyx_k_Hyph), 0, 0, 1, 1},
  {&__pyx_n_s_I, __pyx_k_I, sizeof(__pyx_k_I), 0, 0, 1, 1},
  {&__pyx_n_s_ID, __pyx_k_ID, sizeof(__pyx_k_ID), 0, 0, 1, 1},
  {&__pyx_n_s_IDS, __pyx_k_IDS, sizeof(__pyx_k_IDS), 0, 0, 1, 1},
  {&__pyx_n_s_IDX, __pyx_k_IDX, sizeof(__pyx_k_IDX), 0, 0, 1, 1},
  {&__pyx_n_s_IOB_STRINGS, __pyx_k_IOB_STRINGS, sizeof(__pyx_k_IOB_STRINGS), 0, 0, 1, 1},
  {&__pyx_n_s_IS_ALPHA, __pyx_k_IS_ALPHA, sizeof(__pyx_k_IS_ALPHA), 0, 0, 1, 1},
  {&__pyx_n_s_IS_ASCII, __pyx_k_IS_ASCII, sizeof(__pyx_k_IS_ASCII), 0, 0, 1, 1},
  {&__pyx_n_s_IS_BRACKET, __pyx_k_IS_BRACKET, sizeof(__pyx_k_IS_BRACKET), 0, 0, 1, 1},
  {&__pyx_n_s_IS_CURRENCY, __pyx_k_IS_CURRENCY, sizeof(__pyx_k_IS_CURRENCY), 0, 0, 1, 1},
  {&__pyx_n_s_IS_DIGIT, __pyx_k_IS_DIGIT, sizeof(__pyx_k_IS_DIGIT), 0, 0, 1, 1},
  {&__pyx_n_s_IS_LEFT_PUNCT, __pyx_k_IS_LEFT_PUNCT, sizeof(__pyx_k_IS_LEFT_PUNCT), 0, 0, 1, 1},
  {&__pyx_n_s_IS_LOWER, __pyx_k_IS_LOWER, sizeof(__pyx_k_IS_LOWER), 0, 0, 1, 1},
  {&__pyx_n_s_IS_OOV_DEPRECATED, __pyx_k_IS_OOV_DEPRECATED, sizeof(__pyx_k_IS_OOV_DEPRECATED), 0, 0, 1, 1},
  {&__pyx_n_s_IS_PUNCT, __pyx_k_IS_PUNCT, sizeof(__pyx_k_IS_PUNCT), 0, 0, 1, 1},
  {&__pyx_n_s_IS_QUOTE, __pyx_k_IS_QUOTE, sizeof(__pyx_k_IS_QUOTE), 0, 0, 1, 1},
  {&__pyx_n_s_IS_RIGHT_PUNCT, __pyx_k_IS_RIGHT_PUNCT, sizeof(__pyx_k_IS_RIGHT_PUNCT), 0, 0, 1, 1},
  {&__pyx_n_s_IS_SPACE, __pyx_k_IS_SPACE, sizeof(__pyx_k_IS_SPACE), 0, 0, 1, 1},
  {&__pyx_n_s_IS_STOP, __pyx_k_IS_STOP, sizeof(__pyx_k_IS_STOP), 0, 0, 1, 1},
  {&__pyx_n_s_IS_TITLE, __pyx_k_IS_TITLE, sizeof(__pyx_k_IS_TITLE), 0, 0, 1, 1},
  {&__pyx_n_s_IS_UPPER, __pyx_k_IS_UPPER, sizeof(__pyx_k_IS_UPPER), 0, 0, 1, 1},
  {&__pyx_n_s_L, __pyx_k_L, sizeof(__pyx_k_L), 0, 0, 1, 1},
  {&__pyx_n_s_LANG, __pyx_k_LANG, sizeof(__pyx_k_LANG), 0, 0, 1, 1},
  {&__pyx_n_s_LEMMA, __pyx_k_LEMMA, sizeof(__pyx_k_LEMMA), 0, 0, 1, 1},
  {&__pyx_n_s_LENGTH, __pyx_k_LENGTH, sizeof(__pyx_k_LENGTH), 0, 0, 1, 1},
  {&__pyx_n_s_LIKE_EMAIL, __pyx_k_LIKE_EMAIL, sizeof(__pyx_k_LIKE_EMAIL), 0, 0, 1, 1},
  {&__pyx_n_s_LIKE_NUM, __pyx_k_LIKE_NUM, sizeof(__pyx_k_LIKE_NUM), 0, 0, 1, 1},
  {&__pyx_n_s_LIKE_URL, __pyx_k_LIKE_URL, sizeof(__pyx_k_LIKE_URL), 0, 0, 1, 1},
  {&__pyx_n_s_LOWER, __pyx_k_LOWER, sizeof(__pyx_k_LOWER), 0, 0, 1, 1},
  {&__pyx_n_s_MORPH, __pyx_k_MORPH, sizeof(__pyx_k_MORPH), 0, 0, 1, 1},
  {&__pyx_n_s_Mood, __pyx_k_Mood, sizeof(__pyx_k_Mood), 0, 0, 1, 1},
  {&__pyx_n_s_NAMES, __pyx_k_NAMES, sizeof(__pyx_k_NAMES), 0, 0, 1, 1},
  {&__pyx_n_s_NORM, __pyx_k_NORM, sizeof(__pyx_k_NORM), 0, 0, 1, 1},
  {&__pyx_n_s_NameType, __pyx_k_NameType, sizeof(__pyx_k_NameType), 0, 0, 1, 1},
  {&__pyx_n_s_Negative, __pyx_k_Negative, sizeof(__pyx_k_Negative), 0, 0, 1, 1},
  {&__pyx_n_s_NounType, __pyx_k_NounType, sizeof(__pyx_k_NounType), 0, 0, 1, 1},
  {&__pyx_n_s_NumForm, __pyx_k_NumForm, sizeof(__pyx_k_NumForm), 0, 0, 1, 1},
  {&__pyx_n_s_NumType, __pyx_k_NumType, sizeof(__pyx_k_NumType), 0, 0, 1, 1},
  {&__pyx_n_s_NumValue, __pyx_k_NumValue, sizeof(__pyx_k_NumValue), 0, 0, 1, 1},
  {&__pyx_n_s_Number, __pyx_k_Number, sizeof(__pyx_k_Number), 0, 0, 1, 1},
  {&__pyx_n_s_O, __pyx_k_O, sizeof(__pyx_k_O), 0, 0, 1, 1},
  {&__pyx_n_s_ORTH, __pyx_k_ORTH, sizeof(__pyx_k_ORTH), 0, 0, 1, 1},
  {&__pyx_n_s_Other, __pyx_k_Other, sizeof(__pyx_k_Other), 0, 0, 1, 1},
  {&__pyx_n_s_POS, __pyx_k_POS, sizeof(__pyx_k_POS), 0, 0, 1, 1},
  {&__pyx_n_s_PREFIX, __pyx_k_PREFIX, sizeof(__pyx_k_PREFIX), 0, 0, 1, 1},
  {&__pyx_n_s_PartType, __pyx_k_PartType, sizeof(__pyx_k_PartType), 0, 0, 1, 1},
  {&__pyx_n_s_Person, __pyx_k_Person, sizeof(__pyx_k_Person), 0, 0, 1, 1},
  {&__pyx_n_s_Polarity, __pyx_k_Polarity, sizeof(__pyx_k_Polarity), 0, 0, 1, 1},
  {&__pyx_n_s_Polite, __pyx_k_Polite, sizeof(__pyx_k_Polite), 0, 0, 1, 1},
  {&__pyx_n_s_Poss, __pyx_k_Poss, sizeof(__pyx_k_Poss), 0, 0, 1, 1},
  {&__pyx_n_s_PrepCase, __pyx_k_PrepCase, sizeof(__pyx_k_PrepCase), 0, 0, 1, 1},
  {&__pyx_n_s_PronType, __pyx_k_PronType, sizeof(__pyx_k_PronType), 0, 0, 1, 1},
  {&__pyx_n_s_PunctSide, __pyx_k_PunctSide, sizeof(__pyx_k_PunctSide), 0, 0, 1, 1},
  {&__pyx_n_s_PunctType, __pyx_k_PunctType, sizeof(__pyx_k_PunctType), 0, 0, 1, 1},
  {&__pyx_n_s_Reflex, __pyx_k_Reflex, sizeof(__pyx_k_Reflex), 0, 0, 1, 1},
  {&__pyx_n_s_SENT_START, __pyx_k_SENT_START, sizeof(__pyx_k_SENT_START), 0, 0, 1, 1},
  {&__pyx_n_s_SHAPE, __pyx_k_SHAPE, sizeof(__pyx_k_SHAPE), 0, 0, 1, 1},
  {&__pyx_n_s_SPACY, __pyx_k_SPACY, sizeof(__pyx_k_SPACY), 0, 0, 1, 1},
  {&__pyx_n_s_SUFFIX, __pyx_k_SUFFIX, sizeof(__pyx_k_SUFFIX), 0, 0, 1, 1},
  {&__pyx_n_s_StyleVariant, __pyx_k_StyleVariant, sizeof(__pyx_k_StyleVariant), 0, 0, 1, 1},
  {&__pyx_n_s_TAG, __pyx_k_TAG, sizeof(__pyx_k_TAG), 0, 0, 1, 1},
  {&__pyx_n_s_Tense, __pyx_k_Tense, sizeof(__pyx_k_Tense), 0, 0, 1, 1},
  {&__pyx_n_s_ValueError, __pyx_k_ValueError, sizeof(__pyx_k_ValueError), 0, 0, 1, 1},
  {&__pyx_n_s_Variant, __pyx_k_Variant, sizeof(__pyx_k_Variant), 0, 0, 1, 1},
  {&__pyx_n_s_VerbForm, __pyx_k_VerbForm, sizeof(__pyx_k_VerbForm), 0, 0, 1, 1},
  {&__pyx_n_s_VerbType, __pyx_k_VerbType, sizeof(__pyx_k_VerbType), 0, 0, 1, 1},
  {&__pyx_n_s_Voice, __pyx_k_Voice, sizeof(__pyx_k_Voice), 0, 0, 1, 1},
  {&__pyx_n_s_add, __pyx_k_add, sizeof(__pyx_k_add), 0, 0, 1, 1},
  {&__pyx_n_s_cline_in_traceback, __pyx_k_cline_in_traceback, sizeof(__pyx_k_cline_in_traceback), 0, 0, 1, 1},
  {&__pyx_n_s_do_deprecated, __pyx_k_do_deprecated, sizeof(__pyx_k_do_deprecated), 0, 0, 1, 1},
  {&__pyx_n_s_errors, __pyx_k_errors, sizeof(__pyx_k_errors), 0, 0, 1, 1},
  {&__pyx_n_s_format, __pyx_k_format, sizeof(__pyx_k_format), 0, 0, 1, 1},
  {&__pyx_n_s_import, __pyx_k_import, sizeof(__pyx_k_import), 0, 0, 1, 1},
  {&__pyx_n_s_index, __pyx_k_index, sizeof(__pyx_k_index), 0, 0, 1, 1},
  {&__pyx_n_s_int_key, __pyx_k_int_key, sizeof(__pyx_k_int_key), 0, 0, 1, 1},
  {&__pyx_n_s_intify_attr, __pyx_k_intify_attr, sizeof(__pyx_k_intify_attr), 0, 0, 1, 1},
  {&__pyx_n_s_intify_attrs, __pyx_k_intify_attrs, sizeof(__pyx_k_intify_attrs), 0, 0, 1, 1},
  {&__pyx_n_s_inty_attrs, __pyx_k_inty_attrs, sizeof(__pyx_k_inty_attrs), 0, 0, 1, 1},
  {&__pyx_n_s_items, __pyx_k_items, sizeof(__pyx_k_items), 0, 0, 1, 1},
  {&__pyx_n_s_key, __pyx_k_key, sizeof(__pyx_k_key), 0, 0, 1, 1},
  {&__pyx_n_s_lambda, __pyx_k_lambda, sizeof(__pyx_k_lambda), 0, 0, 1, 1},
  {&__pyx_n_s_lower, __pyx_k_lower, sizeof(__pyx_k_lower), 0, 0, 1, 1},
  {&__pyx_n_s_main, __pyx_k_main, sizeof(__pyx_k_main), 0, 0, 1, 1},
  {&__pyx_n_s_morph, __pyx_k_morph, sizeof(__pyx_k_morph), 0, 0, 1, 1},
  {&__pyx_n_s_morph_keys, __pyx_k_morph_keys, sizeof(__pyx_k_morph_keys), 0, 0, 1, 1},
  {&__pyx_n_s_morphs, __pyx_k_morphs, sizeof(__pyx_k_morphs), 0, 0, 1, 1},
  {&__pyx_n_s_name, __pyx_k_name, sizeof(__pyx_k_name), 0, 0, 1, 1},
  {&__pyx_n_s_name_2, __pyx_k_name_2, sizeof(__pyx_k_name_2), 0, 0, 1, 1},
  {&__pyx_n_s_number, __pyx_k_number, sizeof(__pyx_k_number), 0, 0, 1, 1},
  {&__pyx_n_s_pop, __pyx_k_pop, sizeof(__pyx_k_pop), 0, 0, 1, 1},
  {&__pyx_n_s_pos, __pyx_k_pos, sizeof(__pyx_k_pos), 0, 0, 1, 1},
  {&__pyx_n_s_sorted, __pyx_k_sorted, sizeof(__pyx_k_sorted), 0, 0, 1, 1},
  {&__pyx_n_s_spacy_attrs, __pyx_k_spacy_attrs, sizeof(__pyx_k_spacy_attrs), 0, 0, 1, 1},
  {&__pyx_kp_s_spacy_attrs_pyx, __pyx_k_spacy_attrs_pyx, sizeof(__pyx_k_spacy_attrs_pyx), 0, 0, 1, 0},
  {&__pyx_n_s_strings_map, __pyx_k_strings_map, sizeof(__pyx_k_strings_map), 0, 0, 1, 1},
  {&__pyx_n_s_stringy_attrs, __pyx_k_stringy_attrs, sizeof(__pyx_k_stringy_attrs), 0, 0, 1, 1},
  {&__pyx_n_s_tenspect, __pyx_k_tenspect, sizeof(__pyx_k_tenspect), 0, 0, 1, 1},
  {&__pyx_n_s_test, __pyx_k_test, sizeof(__pyx_k_test), 0, 0, 1, 1},
  {&__pyx_n_s_update, __pyx_k_update, sizeof(__pyx_k_update), 0, 0, 1, 1},
  {&__pyx_n_s_upper, __pyx_k_upper, sizeof(__pyx_k_upper), 0, 0, 1, 1},
  {&__pyx_n_s_value, __pyx_k_value, sizeof(__pyx_k_value), 0, 0, 1, 1},
  {0, 0, 0, 0, 0, 0, 0}
};
static CYTHON_SMALL_CODE int __Pyx_InitCachedBuiltins(void) {
  __pyx_builtin_sorted = __Pyx_GetBuiltinName(__pyx_n_s_sorted); if (!__pyx_builtin_sorted) __PYX_ERR(0, 96, __pyx_L1_error)
  __pyx_builtin_ValueError = __Pyx_GetBuiltinName(__pyx_n_s_ValueError); if (!__pyx_builtin_ValueError) __PYX_ERR(0, 188, __pyx_L1_error)
  return 0;
  __pyx_L1_error:;
  return -1;
}

static CYTHON_SMALL_CODE int __Pyx_InitCachedConstants(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_InitCachedConstants", 0);

  /* "spacy/attrs.pyx":3
 * from .errors import Errors
 * 
 * IOB_STRINGS = ("", "I", "O", "B")             # <<<<<<<<<<<<<<
 * 
 * IDS = {
 */
  __pyx_tuple__2 = PyTuple_Pack(4, __pyx_kp_s_, __pyx_n_s_I, __pyx_n_s_O, __pyx_n_s_B); if (unlikely(!__pyx_tuple__2)) __PYX_ERR(0, 3, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_tuple__2);
  __Pyx_GIVEREF(__pyx_tuple__2);

  /* "spacy/attrs.pyx":100
 * 
 * 
 * def intify_attrs(stringy_attrs, strings_map=None, _do_deprecated=False):             # <<<<<<<<<<<<<<
 *     """
 *     Normalize a dictionary of attributes, converting them to ints.
 */
  __pyx_tuple__3 = PyTuple_Pack(10, __pyx_n_s_stringy_attrs, __pyx_n_s_strings_map, __pyx_n_s_do_deprecated, __pyx_n_s_inty_attrs, __pyx_n_s_morphs, __pyx_n_s_morph_keys, __pyx_n_s_key, __pyx_n_s_name_2, __pyx_n_s_value, __pyx_n_s_int_key); if (unlikely(!__pyx_tuple__3)) __PYX_ERR(0, 100, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_tuple__3);
  __Pyx_GIVEREF(__pyx_tuple__3);
  __pyx_codeobj__4 = (PyObject*)__Pyx_PyCode_New(3, 0, 10, 0, CO_OPTIMIZED|CO_NEWLOCALS, __pyx_empty_bytes, __pyx_empty_tuple, __pyx_empty_tuple, __pyx_tuple__3, __pyx_empty_tuple, __pyx_empty_tuple, __pyx_kp_s_spacy_attrs_pyx, __pyx_n_s_intify_attrs, 100, __pyx_empty_bytes); if (unlikely(!__pyx_codeobj__4)) __PYX_ERR(0, 100, __pyx_L1_error)

  /* "spacy/attrs.pyx":198
 * 
 * 
 * def intify_attr(name):             # <<<<<<<<<<<<<<
 *     """
 *     Normalize an attribute name, converting it to int.
 */
  __pyx_tuple__5 = PyTuple_Pack(1, __pyx_n_s_name_2); if (unlikely(!__pyx_tuple__5)) __PYX_ERR(0, 198, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_tuple__5);
  __Pyx_GIVEREF(__pyx_tuple__5);
  __pyx_codeobj__6 = (PyObject*)__Pyx_PyCode_New(1, 0, 1, 0, CO_OPTIMIZED|CO_NEWLOCALS, __pyx_empty_bytes, __pyx_empty_tuple, __pyx_empty_tuple, __pyx_tuple__5, __pyx_empty_tuple, __pyx_empty_tuple, __pyx_kp_s_spacy_attrs_pyx, __pyx_n_s_intify_attr, 198, __pyx_empty_bytes); if (unlikely(!__pyx_codeobj__6)) __PYX_ERR(0, 198, __pyx_L1_error)
  __Pyx_RefNannyFinishContext();
  return 0;
  __pyx_L1_error:;
  __Pyx_RefNannyFinishContext();
  return -1;
}

static CYTHON_SMALL_CODE int __Pyx_InitGlobals(void) {
  if (__Pyx_InitStrings(__pyx_string_tab) < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  return 0;
  __pyx_L1_error:;
  return -1;
}

static CYTHON_SMALL_CODE int __Pyx_modinit_global_init_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_variable_export_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_function_export_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_type_init_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_type_import_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_variable_import_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_function_import_code(void); /*proto*/

static int __Pyx_modinit_global_init_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_global_init_code", 0);
  /*--- Global init code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}

static int __Pyx_modinit_variable_export_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_variable_export_code", 0);
  /*--- Variable export code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}

static int __Pyx_modinit_function_export_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_function_export_code", 0);
  /*--- Function export code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}

static int __Pyx_modinit_type_init_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_type_init_code", 0);
  /*--- Type init code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}

static int __Pyx_modinit_type_import_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_type_import_code", 0);
  /*--- Type import code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}

static int __Pyx_modinit_variable_import_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_variable_import_code", 0);
  /*--- Variable import code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}

static int __Pyx_modinit_function_import_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_function_import_code", 0);
  /*--- Function import code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}


#ifndef CYTHON_NO_PYINIT_EXPORT
#define __Pyx_PyMODINIT_FUNC PyMODINIT_FUNC
#elif PY_MAJOR_VERSION < 3
#ifdef __cplusplus
#define __Pyx_PyMODINIT_FUNC extern "C" void
#else
#define __Pyx_PyMODINIT_FUNC void
#endif
#else
#ifdef __cplusplus
#define __Pyx_PyMODINIT_FUNC extern "C" PyObject *
#else
#define __Pyx_PyMODINIT_FUNC PyObject *
#endif
#endif


#if PY_MAJOR_VERSION < 3
__Pyx_PyMODINIT_FUNC initattrs(void) CYTHON_SMALL_CODE; /*proto*/
__Pyx_PyMODINIT_FUNC initattrs(void)
#else
__Pyx_PyMODINIT_FUNC PyInit_attrs(void) CYTHON_SMALL_CODE; /*proto*/
__Pyx_PyMODINIT_FUNC PyInit_attrs(void)
#if CYTHON_PEP489_MULTI_PHASE_INIT
{
  return PyModuleDef_Init(&__pyx_moduledef);
}
static CYTHON_SMALL_CODE int __Pyx_check_single_interpreter(void) {
    #if PY_VERSION_HEX >= 0x030700A1
    static PY_INT64_T main_interpreter_id = -1;
    PY_INT64_T current_id = PyInterpreterState_GetID(PyThreadState_Get()->interp);
    if (main_interpreter_id == -1) {
        main_interpreter_id = current_id;
        return (unlikely(current_id == -1)) ? -1 : 0;
    } else if (unlikely(main_interpreter_id != current_id))
    #else
    static PyInterpreterState *main_interpreter = NULL;
    PyInterpreterState *current_interpreter = PyThreadState_Get()->interp;
    if (!main_interpreter) {
        main_interpreter = current_interpreter;
    } else if (unlikely(main_interpreter != current_interpreter))
    #endif
    {
        PyErr_SetString(
            PyExc_ImportError,
            "Interpreter change detected - this module can only be loaded into one interpreter per process.");
        return -1;
    }
    return 0;
}
static CYTHON_SMALL_CODE int __Pyx_copy_spec_to_module(PyObject *spec, PyObject *moddict, const char* from_name, const char* to_name, int allow_none) {
    PyObject *value = PyObject_GetAttrString(spec, from_name);
    int result = 0;
    if (likely(value)) {
        if (allow_none || value != Py_None) {
            result = PyDict_SetItemString(moddict, to_name, value);
        }
        Py_DECREF(value);
    } else if (PyErr_ExceptionMatches(PyExc_AttributeError)) {
        PyErr_Clear();
    } else {
        result = -1;
    }
    return result;
}
static CYTHON_SMALL_CODE PyObject* __pyx_pymod_create(PyObject *spec, CYTHON_UNUSED PyModuleDef *def) {
    PyObject *module = NULL, *moddict, *modname;
    if (__Pyx_check_single_interpreter())
        return NULL;
    if (__pyx_m)
        return __Pyx_NewRef(__pyx_m);
    modname = PyObject_GetAttrString(spec, "name");
    if (unlikely(!modname)) goto bad;
    module = PyModule_NewObject(modname);
    Py_DECREF(modname);
    if (unlikely(!module)) goto bad;
    moddict = PyModule_GetDict(module);
    if (unlikely(!moddict)) goto bad;
    if (unlikely(__Pyx_copy_spec_to_module(spec, moddict, "loader", "__loader__", 1) < 0)) goto bad;
    if (unlikely(__Pyx_copy_spec_to_module(spec, moddict, "origin", "__file__", 1) < 0)) goto bad;
    if (unlikely(__Pyx_copy_spec_to_module(spec, moddict, "parent", "__package__", 1) < 0)) goto bad;
    if (unlikely(__Pyx_copy_spec_to_module(spec, moddict, "submodule_search_locations", "__path__", 0) < 0)) goto bad;
    return module;
bad:
    Py_XDECREF(module);
    return NULL;
}


static CYTHON_SMALL_CODE int __pyx_pymod_exec_attrs(PyObject *__pyx_pyinit_module)
#endif
#endif
{
  PyObject *__pyx_t_1 = NULL;
  PyObject *__pyx_t_2 = NULL;
  PyObject *__pyx_t_3 = NULL;
  PyObject *__pyx_t_4 = NULL;
  Py_ssize_t __pyx_t_5;
  PyObject *(*__pyx_t_6)(PyObject *);
  PyObject *__pyx_t_7 = NULL;
  PyObject *__pyx_t_8 = NULL;
  PyObject *(*__pyx_t_9)(PyObject *);
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;
  __Pyx_RefNannyDeclarations
  #if CYTHON_PEP489_MULTI_PHASE_INIT
  if (__pyx_m) {
    if (__pyx_m == __pyx_pyinit_module) return 0;
    PyErr_SetString(PyExc_RuntimeError, "Module 'attrs' has already been imported. Re-initialisation is not supported.");
    return -1;
  }
  #elif PY_MAJOR_VERSION >= 3
  if (__pyx_m) return __Pyx_NewRef(__pyx_m);
  #endif
  #if CYTHON_REFNANNY
__Pyx_RefNanny = __Pyx_RefNannyImportAPI("refnanny");
if (!__Pyx_RefNanny) {
  PyErr_Clear();
  __Pyx_RefNanny = __Pyx_RefNannyImportAPI("Cython.Runtime.refnanny");
  if (!__Pyx_RefNanny)
      Py_FatalError("failed to import 'refnanny' module");
}
#endif
  __Pyx_RefNannySetupContext("__Pyx_PyMODINIT_FUNC PyInit_attrs(void)", 0);
  if (__Pyx_check_binary_version() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #ifdef __Pxy_PyFrame_Initialize_Offsets
  __Pxy_PyFrame_Initialize_Offsets();
  #endif
  __pyx_empty_tuple = PyTuple_New(0); if (unlikely(!__pyx_empty_tuple)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_empty_bytes = PyBytes_FromStringAndSize("", 0); if (unlikely(!__pyx_empty_bytes)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_empty_unicode = PyUnicode_FromStringAndSize("", 0); if (unlikely(!__pyx_empty_unicode)) __PYX_ERR(0, 1, __pyx_L1_error)
  #ifdef __Pyx_CyFunction_USED
  if (__pyx_CyFunction_init() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  #ifdef __Pyx_FusedFunction_USED
  if (__pyx_FusedFunction_init() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  #ifdef __Pyx_Coroutine_USED
  if (__pyx_Coroutine_init() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  #ifdef __Pyx_Generator_USED
  if (__pyx_Generator_init() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  #ifdef __Pyx_AsyncGen_USED
  if (__pyx_AsyncGen_init() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  #ifdef __Pyx_StopAsyncIteration_USED
  if (__pyx_StopAsyncIteration_init() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  /*--- Library function declarations ---*/
  /*--- Threads initialization code ---*/
  #if defined(WITH_THREAD) && PY_VERSION_HEX < 0x030700F0 && defined(__PYX_FORCE_INIT_THREADS) && __PYX_FORCE_INIT_THREADS
  PyEval_InitThreads();
  #endif
  /*--- Module creation code ---*/
  #if CYTHON_PEP489_MULTI_PHASE_INIT
  __pyx_m = __pyx_pyinit_module;
  Py_INCREF(__pyx_m);
  #else
  #if PY_MAJOR_VERSION < 3
  __pyx_m = Py_InitModule4("attrs", __pyx_methods, 0, 0, PYTHON_API_VERSION); Py_XINCREF(__pyx_m);
  #else
  __pyx_m = PyModule_Create(&__pyx_moduledef);
  #endif
  if (unlikely(!__pyx_m)) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  __pyx_d = PyModule_GetDict(__pyx_m); if (unlikely(!__pyx_d)) __PYX_ERR(0, 1, __pyx_L1_error)
  Py_INCREF(__pyx_d);
  __pyx_b = PyImport_AddModule(__Pyx_BUILTIN_MODULE_NAME); if (unlikely(!__pyx_b)) __PYX_ERR(0, 1, __pyx_L1_error)
  Py_INCREF(__pyx_b);
  __pyx_cython_runtime = PyImport_AddModule((char *) "cython_runtime"); if (unlikely(!__pyx_cython_runtime)) __PYX_ERR(0, 1, __pyx_L1_error)
  Py_INCREF(__pyx_cython_runtime);
  if (PyObject_SetAttrString(__pyx_m, "__builtins__", __pyx_b) < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  /*--- Initialize various global constants etc. ---*/
  if (__Pyx_InitGlobals() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #if PY_MAJOR_VERSION < 3 && (__PYX_DEFAULT_STRING_ENCODING_IS_ASCII || __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT)
  if (__Pyx_init_sys_getdefaultencoding_params() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  if (__pyx_module_is_main_spacy__attrs) {
    if (PyObject_SetAttr(__pyx_m, __pyx_n_s_name, __pyx_n_s_main) < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  }
  #if PY_MAJOR_VERSION >= 3
  {
    PyObject *modules = PyImport_GetModuleDict(); if (unlikely(!modules)) __PYX_ERR(0, 1, __pyx_L1_error)
    if (!PyDict_GetItemString(modules, "spacy.attrs")) {
      if (unlikely(PyDict_SetItemString(modules, "spacy.attrs", __pyx_m) < 0)) __PYX_ERR(0, 1, __pyx_L1_error)
    }
  }
  #endif
  /*--- Builtin init code ---*/
  if (__Pyx_InitCachedBuiltins() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  /*--- Constants init code ---*/
  if (__Pyx_InitCachedConstants() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  /*--- Global type/function init code ---*/
  (void)__Pyx_modinit_global_init_code();
  (void)__Pyx_modinit_variable_export_code();
  (void)__Pyx_modinit_function_export_code();
  (void)__Pyx_modinit_type_init_code();
  (void)__Pyx_modinit_type_import_code();
  (void)__Pyx_modinit_variable_import_code();
  (void)__Pyx_modinit_function_import_code();
  /*--- Execution code ---*/
  #if defined(__Pyx_Generator_USED) || defined(__Pyx_Coroutine_USED)
  if (__Pyx_patch_abc() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif

  /* "spacy/attrs.pyx":1
 * from .errors import Errors             # <<<<<<<<<<<<<<
 * 
 * IOB_STRINGS = ("", "I", "O", "B")
 */
  __pyx_t_1 = PyList_New(1); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 1, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __Pyx_INCREF(__pyx_n_s_Errors);
  __Pyx_GIVEREF(__pyx_n_s_Errors);
  PyList_SET_ITEM(__pyx_t_1, 0, __pyx_n_s_Errors);
  __pyx_t_2 = __Pyx_Import(__pyx_n_s_errors, __pyx_t_1, 1); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 1, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_1 = __Pyx_ImportFrom(__pyx_t_2, __pyx_n_s_Errors); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 1, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_Errors, __pyx_t_1) < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/attrs.pyx":3
 * from .errors import Errors
 * 
 * IOB_STRINGS = ("", "I", "O", "B")             # <<<<<<<<<<<<<<
 * 
 * IDS = {
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_IOB_STRINGS, __pyx_tuple__2) < 0) __PYX_ERR(0, 3, __pyx_L1_error)

  /* "spacy/attrs.pyx":6
 * 
 * IDS = {
 *     "": NULL_ATTR,             # <<<<<<<<<<<<<<
 *     "IS_ALPHA": IS_ALPHA,
 *     "IS_ASCII": IS_ASCII,
 */
  __pyx_t_2 = __Pyx_PyDict_NewPresized(86); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_NULL_ATTR); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_s_, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":7
 * IDS = {
 *     "": NULL_ATTR,
 *     "IS_ALPHA": IS_ALPHA,             # <<<<<<<<<<<<<<
 *     "IS_ASCII": IS_ASCII,
 *     "IS_DIGIT": IS_DIGIT,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_IS_ALPHA); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 7, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_IS_ALPHA, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":8
 *     "": NULL_ATTR,
 *     "IS_ALPHA": IS_ALPHA,
 *     "IS_ASCII": IS_ASCII,             # <<<<<<<<<<<<<<
 *     "IS_DIGIT": IS_DIGIT,
 *     "IS_LOWER": IS_LOWER,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_IS_ASCII); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 8, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_IS_ASCII, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":9
 *     "IS_ALPHA": IS_ALPHA,
 *     "IS_ASCII": IS_ASCII,
 *     "IS_DIGIT": IS_DIGIT,             # <<<<<<<<<<<<<<
 *     "IS_LOWER": IS_LOWER,
 *     "IS_PUNCT": IS_PUNCT,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_IS_DIGIT); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 9, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_IS_DIGIT, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":10
 *     "IS_ASCII": IS_ASCII,
 *     "IS_DIGIT": IS_DIGIT,
 *     "IS_LOWER": IS_LOWER,             # <<<<<<<<<<<<<<
 *     "IS_PUNCT": IS_PUNCT,
 *     "IS_SPACE": IS_SPACE,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_IS_LOWER); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 10, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_IS_LOWER, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":11
 *     "IS_DIGIT": IS_DIGIT,
 *     "IS_LOWER": IS_LOWER,
 *     "IS_PUNCT": IS_PUNCT,             # <<<<<<<<<<<<<<
 *     "IS_SPACE": IS_SPACE,
 *     "IS_TITLE": IS_TITLE,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_IS_PUNCT); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 11, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_IS_PUNCT, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":12
 *     "IS_LOWER": IS_LOWER,
 *     "IS_PUNCT": IS_PUNCT,
 *     "IS_SPACE": IS_SPACE,             # <<<<<<<<<<<<<<
 *     "IS_TITLE": IS_TITLE,
 *     "IS_UPPER": IS_UPPER,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_IS_SPACE); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 12, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_IS_SPACE, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":13
 *     "IS_PUNCT": IS_PUNCT,
 *     "IS_SPACE": IS_SPACE,
 *     "IS_TITLE": IS_TITLE,             # <<<<<<<<<<<<<<
 *     "IS_UPPER": IS_UPPER,
 *     "LIKE_URL": LIKE_URL,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_IS_TITLE); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 13, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_IS_TITLE, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":14
 *     "IS_SPACE": IS_SPACE,
 *     "IS_TITLE": IS_TITLE,
 *     "IS_UPPER": IS_UPPER,             # <<<<<<<<<<<<<<
 *     "LIKE_URL": LIKE_URL,
 *     "LIKE_NUM": LIKE_NUM,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_IS_UPPER); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 14, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_IS_UPPER, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":15
 *     "IS_TITLE": IS_TITLE,
 *     "IS_UPPER": IS_UPPER,
 *     "LIKE_URL": LIKE_URL,             # <<<<<<<<<<<<<<
 *     "LIKE_NUM": LIKE_NUM,
 *     "LIKE_EMAIL": LIKE_EMAIL,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_LIKE_URL); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 15, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_LIKE_URL, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":16
 *     "IS_UPPER": IS_UPPER,
 *     "LIKE_URL": LIKE_URL,
 *     "LIKE_NUM": LIKE_NUM,             # <<<<<<<<<<<<<<
 *     "LIKE_EMAIL": LIKE_EMAIL,
 *     "IS_STOP": IS_STOP,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_LIKE_NUM); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 16, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_LIKE_NUM, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":17
 *     "LIKE_URL": LIKE_URL,
 *     "LIKE_NUM": LIKE_NUM,
 *     "LIKE_EMAIL": LIKE_EMAIL,             # <<<<<<<<<<<<<<
 *     "IS_STOP": IS_STOP,
 *     "IS_OOV_DEPRECATED": IS_OOV_DEPRECATED,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_LIKE_EMAIL); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 17, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_LIKE_EMAIL, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":18
 *     "LIKE_NUM": LIKE_NUM,
 *     "LIKE_EMAIL": LIKE_EMAIL,
 *     "IS_STOP": IS_STOP,             # <<<<<<<<<<<<<<
 *     "IS_OOV_DEPRECATED": IS_OOV_DEPRECATED,
 *     "IS_BRACKET": IS_BRACKET,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_IS_STOP); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 18, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_IS_STOP, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":19
 *     "LIKE_EMAIL": LIKE_EMAIL,
 *     "IS_STOP": IS_STOP,
 *     "IS_OOV_DEPRECATED": IS_OOV_DEPRECATED,             # <<<<<<<<<<<<<<
 *     "IS_BRACKET": IS_BRACKET,
 *     "IS_QUOTE": IS_QUOTE,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_IS_OOV_DEPRECATED); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 19, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_IS_OOV_DEPRECATED, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":20
 *     "IS_STOP": IS_STOP,
 *     "IS_OOV_DEPRECATED": IS_OOV_DEPRECATED,
 *     "IS_BRACKET": IS_BRACKET,             # <<<<<<<<<<<<<<
 *     "IS_QUOTE": IS_QUOTE,
 *     "IS_LEFT_PUNCT": IS_LEFT_PUNCT,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_IS_BRACKET); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 20, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_IS_BRACKET, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":21
 *     "IS_OOV_DEPRECATED": IS_OOV_DEPRECATED,
 *     "IS_BRACKET": IS_BRACKET,
 *     "IS_QUOTE": IS_QUOTE,             # <<<<<<<<<<<<<<
 *     "IS_LEFT_PUNCT": IS_LEFT_PUNCT,
 *     "IS_RIGHT_PUNCT": IS_RIGHT_PUNCT,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_IS_QUOTE); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 21, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_IS_QUOTE, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":22
 *     "IS_BRACKET": IS_BRACKET,
 *     "IS_QUOTE": IS_QUOTE,
 *     "IS_LEFT_PUNCT": IS_LEFT_PUNCT,             # <<<<<<<<<<<<<<
 *     "IS_RIGHT_PUNCT": IS_RIGHT_PUNCT,
 *     "IS_CURRENCY": IS_CURRENCY,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_IS_LEFT_PUNCT); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 22, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_IS_LEFT_PUNCT, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":23
 *     "IS_QUOTE": IS_QUOTE,
 *     "IS_LEFT_PUNCT": IS_LEFT_PUNCT,
 *     "IS_RIGHT_PUNCT": IS_RIGHT_PUNCT,             # <<<<<<<<<<<<<<
 *     "IS_CURRENCY": IS_CURRENCY,
 *     "FLAG19": FLAG19,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_IS_RIGHT_PUNCT); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 23, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_IS_RIGHT_PUNCT, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":24
 *     "IS_LEFT_PUNCT": IS_LEFT_PUNCT,
 *     "IS_RIGHT_PUNCT": IS_RIGHT_PUNCT,
 *     "IS_CURRENCY": IS_CURRENCY,             # <<<<<<<<<<<<<<
 *     "FLAG19": FLAG19,
 *     "FLAG20": FLAG20,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_IS_CURRENCY); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 24, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_IS_CURRENCY, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":25
 *     "IS_RIGHT_PUNCT": IS_RIGHT_PUNCT,
 *     "IS_CURRENCY": IS_CURRENCY,
 *     "FLAG19": FLAG19,             # <<<<<<<<<<<<<<
 *     "FLAG20": FLAG20,
 *     "FLAG21": FLAG21,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG19); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 25, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG19, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":26
 *     "IS_CURRENCY": IS_CURRENCY,
 *     "FLAG19": FLAG19,
 *     "FLAG20": FLAG20,             # <<<<<<<<<<<<<<
 *     "FLAG21": FLAG21,
 *     "FLAG22": FLAG22,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG20); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 26, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG20, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":27
 *     "FLAG19": FLAG19,
 *     "FLAG20": FLAG20,
 *     "FLAG21": FLAG21,             # <<<<<<<<<<<<<<
 *     "FLAG22": FLAG22,
 *     "FLAG23": FLAG23,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG21); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 27, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG21, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":28
 *     "FLAG20": FLAG20,
 *     "FLAG21": FLAG21,
 *     "FLAG22": FLAG22,             # <<<<<<<<<<<<<<
 *     "FLAG23": FLAG23,
 *     "FLAG24": FLAG24,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG22); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 28, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG22, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":29
 *     "FLAG21": FLAG21,
 *     "FLAG22": FLAG22,
 *     "FLAG23": FLAG23,             # <<<<<<<<<<<<<<
 *     "FLAG24": FLAG24,
 *     "FLAG25": FLAG25,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG23); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 29, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG23, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":30
 *     "FLAG22": FLAG22,
 *     "FLAG23": FLAG23,
 *     "FLAG24": FLAG24,             # <<<<<<<<<<<<<<
 *     "FLAG25": FLAG25,
 *     "FLAG26": FLAG26,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG24); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 30, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG24, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":31
 *     "FLAG23": FLAG23,
 *     "FLAG24": FLAG24,
 *     "FLAG25": FLAG25,             # <<<<<<<<<<<<<<
 *     "FLAG26": FLAG26,
 *     "FLAG27": FLAG27,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG25); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 31, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG25, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":32
 *     "FLAG24": FLAG24,
 *     "FLAG25": FLAG25,
 *     "FLAG26": FLAG26,             # <<<<<<<<<<<<<<
 *     "FLAG27": FLAG27,
 *     "FLAG28": FLAG28,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG26); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 32, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG26, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":33
 *     "FLAG25": FLAG25,
 *     "FLAG26": FLAG26,
 *     "FLAG27": FLAG27,             # <<<<<<<<<<<<<<
 *     "FLAG28": FLAG28,
 *     "FLAG29": FLAG29,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG27); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 33, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG27, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":34
 *     "FLAG26": FLAG26,
 *     "FLAG27": FLAG27,
 *     "FLAG28": FLAG28,             # <<<<<<<<<<<<<<
 *     "FLAG29": FLAG29,
 *     "FLAG30": FLAG30,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG28); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 34, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG28, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":35
 *     "FLAG27": FLAG27,
 *     "FLAG28": FLAG28,
 *     "FLAG29": FLAG29,             # <<<<<<<<<<<<<<
 *     "FLAG30": FLAG30,
 *     "FLAG31": FLAG31,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG29); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 35, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG29, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":36
 *     "FLAG28": FLAG28,
 *     "FLAG29": FLAG29,
 *     "FLAG30": FLAG30,             # <<<<<<<<<<<<<<
 *     "FLAG31": FLAG31,
 *     "FLAG32": FLAG32,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG30); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 36, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG30, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":37
 *     "FLAG29": FLAG29,
 *     "FLAG30": FLAG30,
 *     "FLAG31": FLAG31,             # <<<<<<<<<<<<<<
 *     "FLAG32": FLAG32,
 *     "FLAG33": FLAG33,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG31); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 37, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG31, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":38
 *     "FLAG30": FLAG30,
 *     "FLAG31": FLAG31,
 *     "FLAG32": FLAG32,             # <<<<<<<<<<<<<<
 *     "FLAG33": FLAG33,
 *     "FLAG34": FLAG34,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG32); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 38, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG32, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":39
 *     "FLAG31": FLAG31,
 *     "FLAG32": FLAG32,
 *     "FLAG33": FLAG33,             # <<<<<<<<<<<<<<
 *     "FLAG34": FLAG34,
 *     "FLAG35": FLAG35,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG33); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 39, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG33, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":40
 *     "FLAG32": FLAG32,
 *     "FLAG33": FLAG33,
 *     "FLAG34": FLAG34,             # <<<<<<<<<<<<<<
 *     "FLAG35": FLAG35,
 *     "FLAG36": FLAG36,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG34); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 40, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG34, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":41
 *     "FLAG33": FLAG33,
 *     "FLAG34": FLAG34,
 *     "FLAG35": FLAG35,             # <<<<<<<<<<<<<<
 *     "FLAG36": FLAG36,
 *     "FLAG37": FLAG37,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG35); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 41, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG35, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":42
 *     "FLAG34": FLAG34,
 *     "FLAG35": FLAG35,
 *     "FLAG36": FLAG36,             # <<<<<<<<<<<<<<
 *     "FLAG37": FLAG37,
 *     "FLAG38": FLAG38,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG36); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 42, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG36, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":43
 *     "FLAG35": FLAG35,
 *     "FLAG36": FLAG36,
 *     "FLAG37": FLAG37,             # <<<<<<<<<<<<<<
 *     "FLAG38": FLAG38,
 *     "FLAG39": FLAG39,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG37); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 43, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG37, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":44
 *     "FLAG36": FLAG36,
 *     "FLAG37": FLAG37,
 *     "FLAG38": FLAG38,             # <<<<<<<<<<<<<<
 *     "FLAG39": FLAG39,
 *     "FLAG40": FLAG40,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG38); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 44, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG38, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":45
 *     "FLAG37": FLAG37,
 *     "FLAG38": FLAG38,
 *     "FLAG39": FLAG39,             # <<<<<<<<<<<<<<
 *     "FLAG40": FLAG40,
 *     "FLAG41": FLAG41,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG39); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 45, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG39, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":46
 *     "FLAG38": FLAG38,
 *     "FLAG39": FLAG39,
 *     "FLAG40": FLAG40,             # <<<<<<<<<<<<<<
 *     "FLAG41": FLAG41,
 *     "FLAG42": FLAG42,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG40); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 46, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG40, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":47
 *     "FLAG39": FLAG39,
 *     "FLAG40": FLAG40,
 *     "FLAG41": FLAG41,             # <<<<<<<<<<<<<<
 *     "FLAG42": FLAG42,
 *     "FLAG43": FLAG43,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG41); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 47, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG41, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":48
 *     "FLAG40": FLAG40,
 *     "FLAG41": FLAG41,
 *     "FLAG42": FLAG42,             # <<<<<<<<<<<<<<
 *     "FLAG43": FLAG43,
 *     "FLAG44": FLAG44,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG42); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 48, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG42, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":49
 *     "FLAG41": FLAG41,
 *     "FLAG42": FLAG42,
 *     "FLAG43": FLAG43,             # <<<<<<<<<<<<<<
 *     "FLAG44": FLAG44,
 *     "FLAG45": FLAG45,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG43); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 49, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG43, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":50
 *     "FLAG42": FLAG42,
 *     "FLAG43": FLAG43,
 *     "FLAG44": FLAG44,             # <<<<<<<<<<<<<<
 *     "FLAG45": FLAG45,
 *     "FLAG46": FLAG46,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG44); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 50, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG44, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":51
 *     "FLAG43": FLAG43,
 *     "FLAG44": FLAG44,
 *     "FLAG45": FLAG45,             # <<<<<<<<<<<<<<
 *     "FLAG46": FLAG46,
 *     "FLAG47": FLAG47,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG45); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 51, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG45, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":52
 *     "FLAG44": FLAG44,
 *     "FLAG45": FLAG45,
 *     "FLAG46": FLAG46,             # <<<<<<<<<<<<<<
 *     "FLAG47": FLAG47,
 *     "FLAG48": FLAG48,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG46); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 52, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG46, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":53
 *     "FLAG45": FLAG45,
 *     "FLAG46": FLAG46,
 *     "FLAG47": FLAG47,             # <<<<<<<<<<<<<<
 *     "FLAG48": FLAG48,
 *     "FLAG49": FLAG49,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG47); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 53, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG47, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":54
 *     "FLAG46": FLAG46,
 *     "FLAG47": FLAG47,
 *     "FLAG48": FLAG48,             # <<<<<<<<<<<<<<
 *     "FLAG49": FLAG49,
 *     "FLAG50": FLAG50,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG48); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 54, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG48, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":55
 *     "FLAG47": FLAG47,
 *     "FLAG48": FLAG48,
 *     "FLAG49": FLAG49,             # <<<<<<<<<<<<<<
 *     "FLAG50": FLAG50,
 *     "FLAG51": FLAG51,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG49); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 55, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG49, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":56
 *     "FLAG48": FLAG48,
 *     "FLAG49": FLAG49,
 *     "FLAG50": FLAG50,             # <<<<<<<<<<<<<<
 *     "FLAG51": FLAG51,
 *     "FLAG52": FLAG52,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG50); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 56, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG50, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":57
 *     "FLAG49": FLAG49,
 *     "FLAG50": FLAG50,
 *     "FLAG51": FLAG51,             # <<<<<<<<<<<<<<
 *     "FLAG52": FLAG52,
 *     "FLAG53": FLAG53,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG51); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 57, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG51, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":58
 *     "FLAG50": FLAG50,
 *     "FLAG51": FLAG51,
 *     "FLAG52": FLAG52,             # <<<<<<<<<<<<<<
 *     "FLAG53": FLAG53,
 *     "FLAG54": FLAG54,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG52); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 58, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG52, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":59
 *     "FLAG51": FLAG51,
 *     "FLAG52": FLAG52,
 *     "FLAG53": FLAG53,             # <<<<<<<<<<<<<<
 *     "FLAG54": FLAG54,
 *     "FLAG55": FLAG55,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG53); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 59, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG53, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":60
 *     "FLAG52": FLAG52,
 *     "FLAG53": FLAG53,
 *     "FLAG54": FLAG54,             # <<<<<<<<<<<<<<
 *     "FLAG55": FLAG55,
 *     "FLAG56": FLAG56,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG54); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 60, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG54, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":61
 *     "FLAG53": FLAG53,
 *     "FLAG54": FLAG54,
 *     "FLAG55": FLAG55,             # <<<<<<<<<<<<<<
 *     "FLAG56": FLAG56,
 *     "FLAG57": FLAG57,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG55); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 61, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG55, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":62
 *     "FLAG54": FLAG54,
 *     "FLAG55": FLAG55,
 *     "FLAG56": FLAG56,             # <<<<<<<<<<<<<<
 *     "FLAG57": FLAG57,
 *     "FLAG58": FLAG58,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG56); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 62, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG56, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":63
 *     "FLAG55": FLAG55,
 *     "FLAG56": FLAG56,
 *     "FLAG57": FLAG57,             # <<<<<<<<<<<<<<
 *     "FLAG58": FLAG58,
 *     "FLAG59": FLAG59,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG57); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 63, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG57, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":64
 *     "FLAG56": FLAG56,
 *     "FLAG57": FLAG57,
 *     "FLAG58": FLAG58,             # <<<<<<<<<<<<<<
 *     "FLAG59": FLAG59,
 *     "FLAG60": FLAG60,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG58); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 64, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG58, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":65
 *     "FLAG57": FLAG57,
 *     "FLAG58": FLAG58,
 *     "FLAG59": FLAG59,             # <<<<<<<<<<<<<<
 *     "FLAG60": FLAG60,
 *     "FLAG61": FLAG61,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG59); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 65, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG59, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":66
 *     "FLAG58": FLAG58,
 *     "FLAG59": FLAG59,
 *     "FLAG60": FLAG60,             # <<<<<<<<<<<<<<
 *     "FLAG61": FLAG61,
 *     "FLAG62": FLAG62,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG60); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 66, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG60, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":67
 *     "FLAG59": FLAG59,
 *     "FLAG60": FLAG60,
 *     "FLAG61": FLAG61,             # <<<<<<<<<<<<<<
 *     "FLAG62": FLAG62,
 *     "FLAG63": FLAG63,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG61); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 67, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG61, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":68
 *     "FLAG60": FLAG60,
 *     "FLAG61": FLAG61,
 *     "FLAG62": FLAG62,             # <<<<<<<<<<<<<<
 *     "FLAG63": FLAG63,
 *     "ID": ID,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG62); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 68, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG62, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":69
 *     "FLAG61": FLAG61,
 *     "FLAG62": FLAG62,
 *     "FLAG63": FLAG63,             # <<<<<<<<<<<<<<
 *     "ID": ID,
 *     "ORTH": ORTH,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_FLAG63); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 69, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_FLAG63, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":70
 *     "FLAG62": FLAG62,
 *     "FLAG63": FLAG63,
 *     "ID": ID,             # <<<<<<<<<<<<<<
 *     "ORTH": ORTH,
 *     "LOWER": LOWER,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_ID); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 70, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_ID, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":71
 *     "FLAG63": FLAG63,
 *     "ID": ID,
 *     "ORTH": ORTH,             # <<<<<<<<<<<<<<
 *     "LOWER": LOWER,
 *     "NORM": NORM,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_ORTH); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 71, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_ORTH, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":72
 *     "ID": ID,
 *     "ORTH": ORTH,
 *     "LOWER": LOWER,             # <<<<<<<<<<<<<<
 *     "NORM": NORM,
 *     "SHAPE": SHAPE,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_LOWER); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 72, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_LOWER, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":73
 *     "ORTH": ORTH,
 *     "LOWER": LOWER,
 *     "NORM": NORM,             # <<<<<<<<<<<<<<
 *     "SHAPE": SHAPE,
 *     "PREFIX": PREFIX,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_NORM); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 73, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_NORM, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":74
 *     "LOWER": LOWER,
 *     "NORM": NORM,
 *     "SHAPE": SHAPE,             # <<<<<<<<<<<<<<
 *     "PREFIX": PREFIX,
 *     "SUFFIX": SUFFIX,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_SHAPE); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 74, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_SHAPE, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":75
 *     "NORM": NORM,
 *     "SHAPE": SHAPE,
 *     "PREFIX": PREFIX,             # <<<<<<<<<<<<<<
 *     "SUFFIX": SUFFIX,
 *     "LENGTH": LENGTH,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_PREFIX); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 75, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_PREFIX, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":76
 *     "SHAPE": SHAPE,
 *     "PREFIX": PREFIX,
 *     "SUFFIX": SUFFIX,             # <<<<<<<<<<<<<<
 *     "LENGTH": LENGTH,
 *     "LEMMA": LEMMA,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_SUFFIX); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 76, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_SUFFIX, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":77
 *     "PREFIX": PREFIX,
 *     "SUFFIX": SUFFIX,
 *     "LENGTH": LENGTH,             # <<<<<<<<<<<<<<
 *     "LEMMA": LEMMA,
 *     "POS": POS,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_LENGTH); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 77, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_LENGTH, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":78
 *     "SUFFIX": SUFFIX,
 *     "LENGTH": LENGTH,
 *     "LEMMA": LEMMA,             # <<<<<<<<<<<<<<
 *     "POS": POS,
 *     "TAG": TAG,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_LEMMA); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 78, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_LEMMA, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":79
 *     "LENGTH": LENGTH,
 *     "LEMMA": LEMMA,
 *     "POS": POS,             # <<<<<<<<<<<<<<
 *     "TAG": TAG,
 *     "DEP": DEP,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_POS); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 79, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_POS, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":80
 *     "LEMMA": LEMMA,
 *     "POS": POS,
 *     "TAG": TAG,             # <<<<<<<<<<<<<<
 *     "DEP": DEP,
 *     "ENT_IOB": ENT_IOB,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_TAG); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 80, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_TAG, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":81
 *     "POS": POS,
 *     "TAG": TAG,
 *     "DEP": DEP,             # <<<<<<<<<<<<<<
 *     "ENT_IOB": ENT_IOB,
 *     "ENT_TYPE": ENT_TYPE,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_DEP); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 81, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_DEP, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":82
 *     "TAG": TAG,
 *     "DEP": DEP,
 *     "ENT_IOB": ENT_IOB,             # <<<<<<<<<<<<<<
 *     "ENT_TYPE": ENT_TYPE,
 *     "ENT_ID": ENT_ID,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_ENT_IOB); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 82, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_ENT_IOB, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":83
 *     "DEP": DEP,
 *     "ENT_IOB": ENT_IOB,
 *     "ENT_TYPE": ENT_TYPE,             # <<<<<<<<<<<<<<
 *     "ENT_ID": ENT_ID,
 *     "ENT_KB_ID": ENT_KB_ID,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_ENT_TYPE); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 83, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_ENT_TYPE, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":84
 *     "ENT_IOB": ENT_IOB,
 *     "ENT_TYPE": ENT_TYPE,
 *     "ENT_ID": ENT_ID,             # <<<<<<<<<<<<<<
 *     "ENT_KB_ID": ENT_KB_ID,
 *     "HEAD": HEAD,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_ENT_ID); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 84, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_ENT_ID, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":85
 *     "ENT_TYPE": ENT_TYPE,
 *     "ENT_ID": ENT_ID,
 *     "ENT_KB_ID": ENT_KB_ID,             # <<<<<<<<<<<<<<
 *     "HEAD": HEAD,
 *     "SENT_START": SENT_START,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_ENT_KB_ID); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 85, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_ENT_KB_ID, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":86
 *     "ENT_ID": ENT_ID,
 *     "ENT_KB_ID": ENT_KB_ID,
 *     "HEAD": HEAD,             # <<<<<<<<<<<<<<
 *     "SENT_START": SENT_START,
 *     "SPACY": SPACY,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_HEAD); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 86, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_HEAD, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":87
 *     "ENT_KB_ID": ENT_KB_ID,
 *     "HEAD": HEAD,
 *     "SENT_START": SENT_START,             # <<<<<<<<<<<<<<
 *     "SPACY": SPACY,
 *     "LANG": LANG,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_SENT_START); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 87, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_SENT_START, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":88
 *     "HEAD": HEAD,
 *     "SENT_START": SENT_START,
 *     "SPACY": SPACY,             # <<<<<<<<<<<<<<
 *     "LANG": LANG,
 *     "MORPH": MORPH,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_SPACY); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 88, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_SPACY, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":89
 *     "SENT_START": SENT_START,
 *     "SPACY": SPACY,
 *     "LANG": LANG,             # <<<<<<<<<<<<<<
 *     "MORPH": MORPH,
 *     "IDX": IDX,
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_LANG); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 89, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_LANG, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":90
 *     "SPACY": SPACY,
 *     "LANG": LANG,
 *     "MORPH": MORPH,             # <<<<<<<<<<<<<<
 *     "IDX": IDX,
 * }
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_MORPH); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 90, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_MORPH, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/attrs.pyx":91
 *     "LANG": LANG,
 *     "MORPH": MORPH,
 *     "IDX": IDX,             # <<<<<<<<<<<<<<
 * }
 * 
 */
  __pyx_t_1 = __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(__pyx_e_5spacy_5attrs_IDX); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 91, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_s_IDX, __pyx_t_1) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_IDS, __pyx_t_2) < 0) __PYX_ERR(0, 5, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/attrs.pyx":96
 * 
 * # ATTR IDs, in order of the symbol
 * NAMES = [key for key, value in sorted(IDS.items(), key=lambda item: item[1])]             # <<<<<<<<<<<<<<
 * locals().update(IDS)
 * 
 */
  __pyx_t_2 = PyList_New(0); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 96, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_IDS); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 96, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __pyx_t_4 = __Pyx_PyObject_GetAttrStr(__pyx_t_3, __pyx_n_s_items); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 96, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_4);
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_3 = NULL;
  if (CYTHON_UNPACK_METHODS && unlikely(PyMethod_Check(__pyx_t_4))) {
    __pyx_t_3 = PyMethod_GET_SELF(__pyx_t_4);
    if (likely(__pyx_t_3)) {
      PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_4);
      __Pyx_INCREF(__pyx_t_3);
      __Pyx_INCREF(function);
      __Pyx_DECREF_SET(__pyx_t_4, function);
    }
  }
  __pyx_t_1 = (__pyx_t_3) ? __Pyx_PyObject_CallOneArg(__pyx_t_4, __pyx_t_3) : __Pyx_PyObject_CallNoArg(__pyx_t_4);
  __Pyx_XDECREF(__pyx_t_3); __pyx_t_3 = 0;
  if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 96, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
  __pyx_t_4 = PyTuple_New(1); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 96, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_4);
  __Pyx_GIVEREF(__pyx_t_1);
  PyTuple_SET_ITEM(__pyx_t_4, 0, __pyx_t_1);
  __pyx_t_1 = 0;
  __pyx_t_1 = __Pyx_PyDict_NewPresized(1); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 96, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_t_3 = __Pyx_CyFunction_New(&__pyx_mdef_5spacy_5attrs_4lambda, 0, __pyx_n_s_lambda, NULL, __pyx_n_s_spacy_attrs, __pyx_d, NULL); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 96, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  if (PyDict_SetItem(__pyx_t_1, __pyx_n_s_key, __pyx_t_3) < 0) __PYX_ERR(0, 96, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_3 = __Pyx_PyObject_Call(__pyx_builtin_sorted, __pyx_t_4, __pyx_t_1); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 96, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  if (likely(PyList_CheckExact(__pyx_t_3)) || PyTuple_CheckExact(__pyx_t_3)) {
    __pyx_t_1 = __pyx_t_3; __Pyx_INCREF(__pyx_t_1); __pyx_t_5 = 0;
    __pyx_t_6 = NULL;
  } else {
    __pyx_t_5 = -1; __pyx_t_1 = PyObject_GetIter(__pyx_t_3); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 96, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __pyx_t_6 = Py_TYPE(__pyx_t_1)->tp_iternext; if (unlikely(!__pyx_t_6)) __PYX_ERR(0, 96, __pyx_L1_error)
  }
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  for (;;) {
    if (likely(!__pyx_t_6)) {
      if (likely(PyList_CheckExact(__pyx_t_1))) {
        if (__pyx_t_5 >= PyList_GET_SIZE(__pyx_t_1)) break;
        #if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS
        __pyx_t_3 = PyList_GET_ITEM(__pyx_t_1, __pyx_t_5); __Pyx_INCREF(__pyx_t_3); __pyx_t_5++; if (unlikely(0 < 0)) __PYX_ERR(0, 96, __pyx_L1_error)
        #else
        __pyx_t_3 = PySequence_ITEM(__pyx_t_1, __pyx_t_5); __pyx_t_5++; if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 96, __pyx_L1_error)
        __Pyx_GOTREF(__pyx_t_3);
        #endif
      } else {
        if (__pyx_t_5 >= PyTuple_GET_SIZE(__pyx_t_1)) break;
        #if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS
        __pyx_t_3 = PyTuple_GET_ITEM(__pyx_t_1, __pyx_t_5); __Pyx_INCREF(__pyx_t_3); __pyx_t_5++; if (unlikely(0 < 0)) __PYX_ERR(0, 96, __pyx_L1_error)
        #else
        __pyx_t_3 = PySequence_ITEM(__pyx_t_1, __pyx_t_5); __pyx_t_5++; if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 96, __pyx_L1_error)
        __Pyx_GOTREF(__pyx_t_3);
        #endif
      }
    } else {
      __pyx_t_3 = __pyx_t_6(__pyx_t_1);
      if (unlikely(!__pyx_t_3)) {
        PyObject* exc_type = PyErr_Occurred();
        if (exc_type) {
          if (likely(__Pyx_PyErr_GivenExceptionMatches(exc_type, PyExc_StopIteration))) PyErr_Clear();
          else __PYX_ERR(0, 96, __pyx_L1_error)
        }
        break;
      }
      __Pyx_GOTREF(__pyx_t_3);
    }
    if ((likely(PyTuple_CheckExact(__pyx_t_3))) || (PyList_CheckExact(__pyx_t_3))) {
      PyObject* sequence = __pyx_t_3;
      Py_ssize_t size = __Pyx_PySequence_SIZE(sequence);
      if (unlikely(size != 2)) {
        if (size > 2) __Pyx_RaiseTooManyValuesError(2);
        else if (size >= 0) __Pyx_RaiseNeedMoreValuesError(size);
        __PYX_ERR(0, 96, __pyx_L1_error)
      }
      #if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS
      if (likely(PyTuple_CheckExact(sequence))) {
        __pyx_t_4 = PyTuple_GET_ITEM(sequence, 0); 
        __pyx_t_7 = PyTuple_GET_ITEM(sequence, 1); 
      } else {
        __pyx_t_4 = PyList_GET_ITEM(sequence, 0); 
        __pyx_t_7 = PyList_GET_ITEM(sequence, 1); 
      }
      __Pyx_INCREF(__pyx_t_4);
      __Pyx_INCREF(__pyx_t_7);
      #else
      __pyx_t_4 = PySequence_ITEM(sequence, 0); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 96, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_4);
      __pyx_t_7 = PySequence_ITEM(sequence, 1); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 96, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_7);
      #endif
      __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
    } else {
      Py_ssize_t index = -1;
      __pyx_t_8 = PyObject_GetIter(__pyx_t_3); if (unlikely(!__pyx_t_8)) __PYX_ERR(0, 96, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_8);
      __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
      __pyx_t_9 = Py_TYPE(__pyx_t_8)->tp_iternext;
      index = 0; __pyx_t_4 = __pyx_t_9(__pyx_t_8); if (unlikely(!__pyx_t_4)) goto __pyx_L4_unpacking_failed;
      __Pyx_GOTREF(__pyx_t_4);
      index = 1; __pyx_t_7 = __pyx_t_9(__pyx_t_8); if (unlikely(!__pyx_t_7)) goto __pyx_L4_unpacking_failed;
      __Pyx_GOTREF(__pyx_t_7);
      if (__Pyx_IternextUnpackEndCheck(__pyx_t_9(__pyx_t_8), 2) < 0) __PYX_ERR(0, 96, __pyx_L1_error)
      __pyx_t_9 = NULL;
      __Pyx_DECREF(__pyx_t_8); __pyx_t_8 = 0;
      goto __pyx_L5_unpacking_done;
      __pyx_L4_unpacking_failed:;
      __Pyx_DECREF(__pyx_t_8); __pyx_t_8 = 0;
      __pyx_t_9 = NULL;
      if (__Pyx_IterFinish() == 0) __Pyx_RaiseNeedMoreValuesError(index);
      __PYX_ERR(0, 96, __pyx_L1_error)
      __pyx_L5_unpacking_done:;
    }
    if (PyDict_SetItem(__pyx_d, __pyx_n_s_key, __pyx_t_4) < 0) __PYX_ERR(0, 96, __pyx_L1_error)
    __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
    if (PyDict_SetItem(__pyx_d, __pyx_n_s_value, __pyx_t_7) < 0) __PYX_ERR(0, 96, __pyx_L1_error)
    __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;
    __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_key); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 96, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_3);
    if (unlikely(__Pyx_ListComp_Append(__pyx_t_2, (PyObject*)__pyx_t_3))) __PYX_ERR(0, 96, __pyx_L1_error)
    __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  }
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_NAMES, __pyx_t_2) < 0) __PYX_ERR(0, 96, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "spacy/attrs.pyx":97
 * # ATTR IDs, in order of the symbol
 * NAMES = [key for key, value in sorted(IDS.items(), key=lambda item: item[1])]
 * locals().update(IDS)             # <<<<<<<<<<<<<<
 * 
 * 
 */
  __pyx_t_2 = __Pyx_Globals(); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 97, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_1 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_update); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 97, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_IDS); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 97, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_3 = __Pyx_PyObject_CallOneArg(__pyx_t_1, __pyx_t_2); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 97, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;

  /* "spacy/attrs.pyx":100
 * 
 * 
 * def intify_attrs(stringy_attrs, strings_map=None, _do_deprecated=False):             # <<<<<<<<<<<<<<
 *     """
 *     Normalize a dictionary of attributes, converting them to ints.
 */
  __pyx_t_3 = PyCFunction_NewEx(&__pyx_mdef_5spacy_5attrs_1intify_attrs, NULL, __pyx_n_s_spacy_attrs); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 100, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_intify_attrs, __pyx_t_3) < 0) __PYX_ERR(0, 100, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;

  /* "spacy/attrs.pyx":198
 * 
 * 
 * def intify_attr(name):             # <<<<<<<<<<<<<<
 *     """
 *     Normalize an attribute name, converting it to int.
 */
  __pyx_t_3 = PyCFunction_NewEx(&__pyx_mdef_5spacy_5attrs_3intify_attr, NULL, __pyx_n_s_spacy_attrs); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 198, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_intify_attr, __pyx_t_3) < 0) __PYX_ERR(0, 198, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;

  /* "spacy/attrs.pyx":1
 * from .errors import Errors             # <<<<<<<<<<<<<<
 * 
 * IOB_STRINGS = ("", "I", "O", "B")
 */
  __pyx_t_3 = __Pyx_PyDict_NewPresized(0); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 1, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_test, __pyx_t_3) < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;

  /*--- Wrapped vars code ---*/

  goto __pyx_L0;
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_XDECREF(__pyx_t_2);
  __Pyx_XDECREF(__pyx_t_3);
  __Pyx_XDECREF(__pyx_t_4);
  __Pyx_XDECREF(__pyx_t_7);
  __Pyx_XDECREF(__pyx_t_8);
  if (__pyx_m) {
    if (__pyx_d) {
      __Pyx_AddTraceback("init spacy.attrs", __pyx_clineno, __pyx_lineno, __pyx_filename);
    }
    Py_CLEAR(__pyx_m);
  } else if (!PyErr_Occurred()) {
    PyErr_SetString(PyExc_ImportError, "init spacy.attrs");
  }
  __pyx_L0:;
  __Pyx_RefNannyFinishContext();
  #if CYTHON_PEP489_MULTI_PHASE_INIT
  return (__pyx_m != NULL) ? 0 : -1;
  #elif PY_MAJOR_VERSION >= 3
  return __pyx_m;
  #else
  return;
  #endif
}

/* --- Runtime support code --- */
/* Refnanny */
#if CYTHON_REFNANNY
static __Pyx_RefNannyAPIStruct *__Pyx_RefNannyImportAPI(const char *modname) {
    PyObject *m = NULL, *p = NULL;
    void *r = NULL;
    m = PyImport_ImportModule(modname);
    if (!m) goto end;
    p = PyObject_GetAttrString(m, "RefNannyAPI");
    if (!p) goto end;
    r = PyLong_AsVoidPtr(p);
end:
    Py_XDECREF(p);
    Py_XDECREF(m);
    return (__Pyx_RefNannyAPIStruct *)r;
}
#endif

/* PyObjectGetAttrStr */
#if CYTHON_USE_TYPE_SLOTS
static CYTHON_INLINE PyObject* __Pyx_PyObject_GetAttrStr(PyObject* obj, PyObject* attr_name) {
    PyTypeObject* tp = Py_TYPE(obj);
    if (likely(tp->tp_getattro))
        return tp->tp_getattro(obj, attr_name);
#if PY_MAJOR_VERSION < 3
    if (likely(tp->tp_getattr))
        return tp->tp_getattr(obj, PyString_AS_STRING(attr_name));
#endif
    return PyObject_GetAttr(obj, attr_name);
}
#endif

/* GetBuiltinName */
static PyObject *__Pyx_GetBuiltinName(PyObject *name) {
    PyObject* result = __Pyx_PyObject_GetAttrStr(__pyx_b, name);
    if (unlikely(!result)) {
        PyErr_Format(PyExc_NameError,
#if PY_MAJOR_VERSION >= 3
            "name '%U' is not defined", name);
#else
            "name '%.200s' is not defined", PyString_AS_STRING(name));
#endif
    }
    return result;
}

/* GetItemInt */
static PyObject *__Pyx_GetItemInt_Generic(PyObject *o, PyObject* j) {
    PyObject *r;
    if (!j) return NULL;
    r = PyObject_GetItem(o, j);
    Py_DECREF(j);
    return r;
}
static CYTHON_INLINE PyObject *__Pyx_GetItemInt_List_Fast(PyObject *o, Py_ssize_t i,
                                                              CYTHON_NCP_UNUSED int wraparound,
                                                              CYTHON_NCP_UNUSED int boundscheck) {
#if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS
    Py_ssize_t wrapped_i = i;
    if (wraparound & unlikely(i < 0)) {
        wrapped_i += PyList_GET_SIZE(o);
    }
    if ((!boundscheck) || likely(__Pyx_is_valid_index(wrapped_i, PyList_GET_SIZE(o)))) {
        PyObject *r = PyList_GET_ITEM(o, wrapped_i);
        Py_INCREF(r);
        return r;
    }
    return __Pyx_GetItemInt_Generic(o, PyInt_FromSsize_t(i));
#else
    return PySequence_GetItem(o, i);
#endif
}
static CYTHON_INLINE PyObject *__Pyx_GetItemInt_Tuple_Fast(PyObject *o, Py_ssize_t i,
                                                              CYTHON_NCP_UNUSED int wraparound,
                                                              CYTHON_NCP_UNUSED int boundscheck) {
#if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS
    Py_ssize_t wrapped_i = i;
    if (wraparound & unlikely(i < 0)) {
        wrapped_i += PyTuple_GET_SIZE(o);
    }
    if ((!boundscheck) || likely(__Pyx_is_valid_index(wrapped_i, PyTuple_GET_SIZE(o)))) {
        PyObject *r = PyTuple_GET_ITEM(o, wrapped_i);
        Py_INCREF(r);
        return r;
    }
    return __Pyx_GetItemInt_Generic(o, PyInt_FromSsize_t(i));
#else
    return PySequence_GetItem(o, i);
#endif
}
static CYTHON_INLINE PyObject *__Pyx_GetItemInt_Fast(PyObject *o, Py_ssize_t i, int is_list,
                                                     CYTHON_NCP_UNUSED int wraparound,
                                                     CYTHON_NCP_UNUSED int boundscheck) {
#if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS && CYTHON_USE_TYPE_SLOTS
    if (is_list || PyList_CheckExact(o)) {
        Py_ssize_t n = ((!wraparound) | likely(i >= 0)) ? i : i + PyList_GET_SIZE(o);
        if ((!boundscheck) || (likely(__Pyx_is_valid_index(n, PyList_GET_SIZE(o))))) {
            PyObject *r = PyList_GET_ITEM(o, n);
            Py_INCREF(r);
            return r;
        }
    }
    else if (PyTuple_CheckExact(o)) {
        Py_ssize_t n = ((!wraparound) | likely(i >= 0)) ? i : i + PyTuple_GET_SIZE(o);
        if ((!boundscheck) || likely(__Pyx_is_valid_index(n, PyTuple_GET_SIZE(o)))) {
            PyObject *r = PyTuple_GET_ITEM(o, n);
            Py_INCREF(r);
            return r;
        }
    } else {
        PySequenceMethods *m = Py_TYPE(o)->tp_as_sequence;
        if (likely(m && m->sq_item)) {
            if (wraparound && unlikely(i < 0) && likely(m->sq_length)) {
                Py_ssize_t l = m->sq_length(o);
                if (likely(l >= 0)) {
                    i += l;
                } else {
                    if (!PyErr_ExceptionMatches(PyExc_OverflowError))
                        return NULL;
                    PyErr_Clear();
                }
            }
            return m->sq_item(o, i);
        }
    }
#else
    if (is_list || PySequence_Check(o)) {
        return PySequence_GetItem(o, i);
    }
#endif
    return __Pyx_GetItemInt_Generic(o, PyInt_FromSsize_t(i));
}

/* RaiseDoubleKeywords */
static void __Pyx_RaiseDoubleKeywordsError(
    const char* func_name,
    PyObject* kw_name)
{
    PyErr_Format(PyExc_TypeError,
        #if PY_MAJOR_VERSION >= 3
        "%s() got multiple values for keyword argument '%U'", func_name, kw_name);
        #else
        "%s() got multiple values for keyword argument '%s'", func_name,
        PyString_AsString(kw_name));
        #endif
}

/* ParseKeywords */
static int __Pyx_ParseOptionalKeywords(
    PyObject *kwds,
    PyObject **argnames[],
    PyObject *kwds2,
    PyObject *values[],
    Py_ssize_t num_pos_args,
    const char* function_name)
{
    PyObject *key = 0, *value = 0;
    Py_ssize_t pos = 0;
    PyObject*** name;
    PyObject*** first_kw_arg = argnames + num_pos_args;
    while (PyDict_Next(kwds, &pos, &key, &value)) {
        name = first_kw_arg;
        while (*name && (**name != key)) name++;
        if (*name) {
            values[name-argnames] = value;
            continue;
        }
        name = first_kw_arg;
        #if PY_MAJOR_VERSION < 3
        if (likely(PyString_Check(key))) {
            while (*name) {
                if ((CYTHON_COMPILING_IN_PYPY || PyString_GET_SIZE(**name) == PyString_GET_SIZE(key))
                        && _PyString_Eq(**name, key)) {
                    values[name-argnames] = value;
                    break;
                }
                name++;
            }
            if (*name) continue;
            else {
                PyObject*** argname = argnames;
                while (argname != first_kw_arg) {
                    if ((**argname == key) || (
                            (CYTHON_COMPILING_IN_PYPY || PyString_GET_SIZE(**argname) == PyString_GET_SIZE(key))
                             && _PyString_Eq(**argname, key))) {
                        goto arg_passed_twice;
                    }
                    argname++;
                }
            }
        } else
        #endif
        if (likely(PyUnicode_Check(key))) {
            while (*name) {
                int cmp = (**name == key) ? 0 :
                #if !CYTHON_COMPILING_IN_PYPY && PY_MAJOR_VERSION >= 3
                    (__Pyx_PyUnicode_GET_LENGTH(**name) != __Pyx_PyUnicode_GET_LENGTH(key)) ? 1 :
                #endif
                    PyUnicode_Compare(**name, key);
                if (cmp < 0 && unlikely(PyErr_Occurred())) goto bad;
                if (cmp == 0) {
                    values[name-argnames] = value;
                    break;
                }
                name++;
            }
            if (*name) continue;
            else {
                PyObject*** argname = argnames;
                while (argname != first_kw_arg) {
                    int cmp = (**argname == key) ? 0 :
                    #if !CYTHON_COMPILING_IN_PYPY && PY_MAJOR_VERSION >= 3
                        (__Pyx_PyUnicode_GET_LENGTH(**argname) != __Pyx_PyUnicode_GET_LENGTH(key)) ? 1 :
                    #endif
                        PyUnicode_Compare(**argname, key);
                    if (cmp < 0 && unlikely(PyErr_Occurred())) goto bad;
                    if (cmp == 0) goto arg_passed_twice;
                    argname++;
                }
            }
        } else
            goto invalid_keyword_type;
        if (kwds2) {
            if (unlikely(PyDict_SetItem(kwds2, key, value))) goto bad;
        } else {
            goto invalid_keyword;
        }
    }
    return 0;
arg_passed_twice:
    __Pyx_RaiseDoubleKeywordsError(function_name, key);
    goto bad;
invalid_keyword_type:
    PyErr_Format(PyExc_TypeError,
        "%.200s() keywords must be strings", function_name);
    goto bad;
invalid_keyword:
    PyErr_Format(PyExc_TypeError,
    #if PY_MAJOR_VERSION < 3
        "%.200s() got an unexpected keyword argument '%.200s'",
        function_name, PyString_AsString(key));
    #else
        "%s() got an unexpected keyword argument '%U'",
        function_name, key);
    #endif
bad:
    return -1;
}

/* RaiseArgTupleInvalid */
static void __Pyx_RaiseArgtupleInvalid(
    const char* func_name,
    int exact,
    Py_ssize_t num_min,
    Py_ssize_t num_max,
    Py_ssize_t num_found)
{
    Py_ssize_t num_expected;
    const char *more_or_less;
    if (num_found < num_min) {
        num_expected = num_min;
        more_or_less = "at least";
    } else {
        num_expected = num_max;
        more_or_less = "at most";
    }
    if (exact) {
        more_or_less = "exactly";
    }
    PyErr_Format(PyExc_TypeError,
                 "%.200s() takes %.8s %" CYTHON_FORMAT_SSIZE_T "d positional argument%.1s (%" CYTHON_FORMAT_SSIZE_T "d given)",
                 func_name, more_or_less, num_expected,
                 (num_expected == 1) ? "" : "s", num_found);
}

/* PyCFunctionFastCall */
#if CYTHON_FAST_PYCCALL
static CYTHON_INLINE PyObject * __Pyx_PyCFunction_FastCall(PyObject *func_obj, PyObject **args, Py_ssize_t nargs) {
    PyCFunctionObject *func = (PyCFunctionObject*)func_obj;
    PyCFunction meth = PyCFunction_GET_FUNCTION(func);
    PyObject *self = PyCFunction_GET_SELF(func);
    int flags = PyCFunction_GET_FLAGS(func);
    assert(PyCFunction_Check(func));
    assert(METH_FASTCALL == (flags & ~(METH_CLASS | METH_STATIC | METH_COEXIST | METH_KEYWORDS | METH_STACKLESS)));
    assert(nargs >= 0);
    assert(nargs == 0 || args != NULL);
    /* _PyCFunction_FastCallDict() must not be called with an exception set,
       because it may clear it (directly or indirectly) and so the
       caller loses its exception */
    assert(!PyErr_Occurred());
    if ((PY_VERSION_HEX < 0x030700A0) || unlikely(flags & METH_KEYWORDS)) {
        return (*((__Pyx_PyCFunctionFastWithKeywords)(void*)meth)) (self, args, nargs, NULL);
    } else {
        return (*((__Pyx_PyCFunctionFast)(void*)meth)) (self, args, nargs);
    }
}
#endif

/* PyFunctionFastCall */
#if CYTHON_FAST_PYCALL
static PyObject* __Pyx_PyFunction_FastCallNoKw(PyCodeObject *co, PyObject **args, Py_ssize_t na,
                                               PyObject *globals) {
    PyFrameObject *f;
    PyThreadState *tstate = __Pyx_PyThreadState_Current;
    PyObject **fastlocals;
    Py_ssize_t i;
    PyObject *result;
    assert(globals != NULL);
    /* XXX Perhaps we should create a specialized
       PyFrame_New() that doesn't take locals, but does
       take builtins without sanity checking them.
       */
    assert(tstate != NULL);
    f = PyFrame_New(tstate, co, globals, NULL);
    if (f == NULL) {
        return NULL;
    }
    fastlocals = __Pyx_PyFrame_GetLocalsplus(f);
    for (i = 0; i < na; i++) {
        Py_INCREF(*args);
        fastlocals[i] = *args++;
    }
    result = PyEval_EvalFrameEx(f,0);
    ++tstate->recursion_depth;
    Py_DECREF(f);
    --tstate->recursion_depth;
    return result;
}
#if 1 || PY_VERSION_HEX < 0x030600B1
static PyObject *__Pyx_PyFunction_FastCallDict(PyObject *func, PyObject **args, Py_ssize_t nargs, PyObject *kwargs) {
    PyCodeObject *co = (PyCodeObject *)PyFunction_GET_CODE(func);
    PyObject *globals = PyFunction_GET_GLOBALS(func);
    PyObject *argdefs = PyFunction_GET_DEFAULTS(func);
    PyObject *closure;
#if PY_MAJOR_VERSION >= 3
    PyObject *kwdefs;
#endif
    PyObject *kwtuple, **k;
    PyObject **d;
    Py_ssize_t nd;
    Py_ssize_t nk;
    PyObject *result;
    assert(kwargs == NULL || PyDict_Check(kwargs));
    nk = kwargs ? PyDict_Size(kwargs) : 0;
    if (Py_EnterRecursiveCall((char*)" while calling a Python object")) {
        return NULL;
    }
    if (
#if PY_MAJOR_VERSION >= 3
            co->co_kwonlyargcount == 0 &&
#endif
            likely(kwargs == NULL || nk == 0) &&
            co->co_flags == (CO_OPTIMIZED | CO_NEWLOCALS | CO_NOFREE)) {
        if (argdefs == NULL && co->co_argcount == nargs) {
            result = __Pyx_PyFunction_FastCallNoKw(co, args, nargs, globals);
            goto done;
        }
        else if (nargs == 0 && argdefs != NULL
                 && co->co_argcount == Py_SIZE(argdefs)) {
            /* function called with no arguments, but all parameters have
               a default value: use default values as arguments .*/
            args = &PyTuple_GET_ITEM(argdefs, 0);
            result =__Pyx_PyFunction_FastCallNoKw(co, args, Py_SIZE(argdefs), globals);
            goto done;
        }
    }
    if (kwargs != NULL) {
        Py_ssize_t pos, i;
        kwtuple = PyTuple_New(2 * nk);
        if (kwtuple == NULL) {
            result = NULL;
            goto done;
        }
        k = &PyTuple_GET_ITEM(kwtuple, 0);
        pos = i = 0;
        while (PyDict_Next(kwargs, &pos, &k[i], &k[i+1])) {
            Py_INCREF(k[i]);
            Py_INCREF(k[i+1]);
            i += 2;
        }
        nk = i / 2;
    }
    else {
        kwtuple = NULL;
        k = NULL;
    }
    closure = PyFunction_GET_CLOSURE(func);
#if PY_MAJOR_VERSION >= 3
    kwdefs = PyFunction_GET_KW_DEFAULTS(func);
#endif
    if (argdefs != NULL) {
        d = &PyTuple_GET_ITEM(argdefs, 0);
        nd = Py_SIZE(argdefs);
    }
    else {
        d = NULL;
        nd = 0;
    }
#if PY_MAJOR_VERSION >= 3
    result = PyEval_EvalCodeEx((PyObject*)co, globals, (PyObject *)NULL,
                               args, (int)nargs,
                               k, (int)nk,
                               d, (int)nd, kwdefs, closure);
#else
    result = PyEval_EvalCodeEx(co, globals, (PyObject *)NULL,
                               args, (int)nargs,
                               k, (int)nk,
                               d, (int)nd, closure);
#endif
    Py_XDECREF(kwtuple);
done:
    Py_LeaveRecursiveCall();
    return result;
}
#endif
#endif

/* PyObjectCall */
#if CYTHON_COMPILING_IN_CPYTHON
static CYTHON_INLINE PyObject* __Pyx_PyObject_Call(PyObject *func, PyObject *arg, PyObject *kw) {
    PyObject *result;
    ternaryfunc call = Py_TYPE(func)->tp_call;
    if (unlikely(!call))
        return PyObject_Call(func, arg, kw);
    if (unlikely(Py_EnterRecursiveCall((char*)" while calling a Python object")))
        return NULL;
    result = (*call)(func, arg, kw);
    Py_LeaveRecursiveCall();
    if (unlikely(!result) && unlikely(!PyErr_Occurred())) {
        PyErr_SetString(
            PyExc_SystemError,
            "NULL result without error in PyObject_Call");
    }
    return result;
}
#endif

/* PyObjectCall2Args */
static CYTHON_UNUSED PyObject* __Pyx_PyObject_Call2Args(PyObject* function, PyObject* arg1, PyObject* arg2) {
    PyObject *args, *result = NULL;
    #if CYTHON_FAST_PYCALL
    if (PyFunction_Check(function)) {
        PyObject *args[2] = {arg1, arg2};
        return __Pyx_PyFunction_FastCall(function, args, 2);
    }
    #endif
    #if CYTHON_FAST_PYCCALL
    if (__Pyx_PyFastCFunction_Check(function)) {
        PyObject *args[2] = {arg1, arg2};
        return __Pyx_PyCFunction_FastCall(function, args, 2);
    }
    #endif
    args = PyTuple_New(2);
    if (unlikely(!args)) goto done;
    Py_INCREF(arg1);
    PyTuple_SET_ITEM(args, 0, arg1);
    Py_INCREF(arg2);
    PyTuple_SET_ITEM(args, 1, arg2);
    Py_INCREF(function);
    result = __Pyx_PyObject_Call(function, args, NULL);
    Py_DECREF(args);
    Py_DECREF(function);
done:
    return result;
}

/* PyObjectCallMethO */
#if CYTHON_COMPILING_IN_CPYTHON
static CYTHON_INLINE PyObject* __Pyx_PyObject_CallMethO(PyObject *func, PyObject *arg) {
    PyObject *self, *result;
    PyCFunction cfunc;
    cfunc = PyCFunction_GET_FUNCTION(func);
    self = PyCFunction_GET_SELF(func);
    if (unlikely(Py_EnterRecursiveCall((char*)" while calling a Python object")))
        return NULL;
    result = cfunc(self, arg);
    Py_LeaveRecursiveCall();
    if (unlikely(!result) && unlikely(!PyErr_Occurred())) {
        PyErr_SetString(
            PyExc_SystemError,
            "NULL result without error in PyObject_Call");
    }
    return result;
}
#endif

/* PyObjectCallOneArg */
#if CYTHON_COMPILING_IN_CPYTHON
static PyObject* __Pyx__PyObject_CallOneArg(PyObject *func, PyObject *arg) {
    PyObject *result;
    PyObject *args = PyTuple_New(1);
    if (unlikely(!args)) return NULL;
    Py_INCREF(arg);
    PyTuple_SET_ITEM(args, 0, arg);
    result = __Pyx_PyObject_Call(func, args, NULL);
    Py_DECREF(args);
    return result;
}
static CYTHON_INLINE PyObject* __Pyx_PyObject_CallOneArg(PyObject *func, PyObject *arg) {
#if CYTHON_FAST_PYCALL
    if (PyFunction_Check(func)) {
        return __Pyx_PyFunction_FastCall(func, &arg, 1);
    }
#endif
    if (likely(PyCFunction_Check(func))) {
        if (likely(PyCFunction_GET_FLAGS(func) & METH_O)) {
            return __Pyx_PyObject_CallMethO(func, arg);
#if CYTHON_FAST_PYCCALL
        } else if (__Pyx_PyFastCFunction_Check(func)) {
            return __Pyx_PyCFunction_FastCall(func, &arg, 1);
#endif
        }
    }
    return __Pyx__PyObject_CallOneArg(func, arg);
}
#else
static CYTHON_INLINE PyObject* __Pyx_PyObject_CallOneArg(PyObject *func, PyObject *arg) {
    PyObject *result;
    PyObject *args = PyTuple_Pack(1, arg);
    if (unlikely(!args)) return NULL;
    result = __Pyx_PyObject_Call(func, args, NULL);
    Py_DECREF(args);
    return result;
}
#endif

/* PyObjectCallNoArg */
#if CYTHON_COMPILING_IN_CPYTHON
static CYTHON_INLINE PyObject* __Pyx_PyObject_CallNoArg(PyObject *func) {
#if CYTHON_FAST_PYCALL
    if (PyFunction_Check(func)) {
        return __Pyx_PyFunction_FastCall(func, NULL, 0);
    }
#endif
#if defined(__Pyx_CyFunction_USED) && defined(NDEBUG)
    if (likely(PyCFunction_Check(func) || __Pyx_CyFunction_Check(func)))
#else
    if (likely(PyCFunction_Check(func)))
#endif
    {
        if (likely(PyCFunction_GET_FLAGS(func) & METH_NOARGS)) {
            return __Pyx_PyObject_CallMethO(func, NULL);
        }
    }
    return __Pyx_PyObject_Call(func, __pyx_empty_tuple, NULL);
}
#endif

/* RaiseTooManyValuesToUnpack */
static CYTHON_INLINE void __Pyx_RaiseTooManyValuesError(Py_ssize_t expected) {
    PyErr_Format(PyExc_ValueError,
                 "too many values to unpack (expected %" CYTHON_FORMAT_SSIZE_T "d)", expected);
}

/* RaiseNeedMoreValuesToUnpack */
static CYTHON_INLINE void __Pyx_RaiseNeedMoreValuesError(Py_ssize_t index) {
    PyErr_Format(PyExc_ValueError,
                 "need more than %" CYTHON_FORMAT_SSIZE_T "d value%.1s to unpack",
                 index, (index == 1) ? "" : "s");
}

/* IterFinish */
static CYTHON_INLINE int __Pyx_IterFinish(void) {
#if CYTHON_FAST_THREAD_STATE
    PyThreadState *tstate = __Pyx_PyThreadState_Current;
    PyObject* exc_type = tstate->curexc_type;
    if (unlikely(exc_type)) {
        if (likely(__Pyx_PyErr_GivenExceptionMatches(exc_type, PyExc_StopIteration))) {
            PyObject *exc_value, *exc_tb;
            exc_value = tstate->curexc_value;
            exc_tb = tstate->curexc_traceback;
            tstate->curexc_type = 0;
            tstate->curexc_value = 0;
            tstate->curexc_traceback = 0;
            Py_DECREF(exc_type);
            Py_XDECREF(exc_value);
            Py_XDECREF(exc_tb);
            return 0;
        } else {
            return -1;
        }
    }
    return 0;
#else
    if (unlikely(PyErr_Occurred())) {
        if (likely(PyErr_ExceptionMatches(PyExc_StopIteration))) {
            PyErr_Clear();
            return 0;
        } else {
            return -1;
        }
    }
    return 0;
#endif
}

/* UnpackItemEndCheck */
static int __Pyx_IternextUnpackEndCheck(PyObject *retval, Py_ssize_t expected) {
    if (unlikely(retval)) {
        Py_DECREF(retval);
        __Pyx_RaiseTooManyValuesError(expected);
        return -1;
    }
    return __Pyx_IterFinish();
}

/* PyDictVersioning */
#if CYTHON_USE_DICT_VERSIONS && CYTHON_USE_TYPE_SLOTS
static CYTHON_INLINE PY_UINT64_T __Pyx_get_tp_dict_version(PyObject *obj) {
    PyObject *dict = Py_TYPE(obj)->tp_dict;
    return likely(dict) ? __PYX_GET_DICT_VERSION(dict) : 0;
}
static CYTHON_INLINE PY_UINT64_T __Pyx_get_object_dict_version(PyObject *obj) {
    PyObject **dictptr = NULL;
    Py_ssize_t offset = Py_TYPE(obj)->tp_dictoffset;
    if (offset) {
#if CYTHON_COMPILING_IN_CPYTHON
        dictptr = (likely(offset > 0)) ? (PyObject **) ((char *)obj + offset) : _PyObject_GetDictPtr(obj);
#else
        dictptr = _PyObject_GetDictPtr(obj);
#endif
    }
    return (dictptr && *dictptr) ? __PYX_GET_DICT_VERSION(*dictptr) : 0;
}
static CYTHON_INLINE int __Pyx_object_dict_version_matches(PyObject* obj, PY_UINT64_T tp_dict_version, PY_UINT64_T obj_dict_version) {
    PyObject *dict = Py_TYPE(obj)->tp_dict;
    if (unlikely(!dict) || unlikely(tp_dict_version != __PYX_GET_DICT_VERSION(dict)))
        return 0;
    return obj_dict_version == __Pyx_get_object_dict_version(obj);
}
#endif

/* GetModuleGlobalName */
#if CYTHON_USE_DICT_VERSIONS
static PyObject *__Pyx__GetModuleGlobalName(PyObject *name, PY_UINT64_T *dict_version, PyObject **dict_cached_value)
#else
static CYTHON_INLINE PyObject *__Pyx__GetModuleGlobalName(PyObject *name)
#endif
{
    PyObject *result;
#if !CYTHON_AVOID_BORROWED_REFS
#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX >= 0x030500A1
    result = _PyDict_GetItem_KnownHash(__pyx_d, name, ((PyASCIIObject *) name)->hash);
    __PYX_UPDATE_DICT_CACHE(__pyx_d, result, *dict_cached_value, *dict_version)
    if (likely(result)) {
        return __Pyx_NewRef(result);
    } else if (unlikely(PyErr_Occurred())) {
        return NULL;
    }
#else
    result = PyDict_GetItem(__pyx_d, name);
    __PYX_UPDATE_DICT_CACHE(__pyx_d, result, *dict_cached_value, *dict_version)
    if (likely(result)) {
        return __Pyx_NewRef(result);
    }
#endif
#else
    result = PyObject_GetItem(__pyx_d, name);
    __PYX_UPDATE_DICT_CACHE(__pyx_d, result, *dict_cached_value, *dict_version)
    if (likely(result)) {
        return __Pyx_NewRef(result);
    }
    PyErr_Clear();
#endif
    return __Pyx_GetBuiltinName(name);
}

/* PyErrFetchRestore */
#if CYTHON_FAST_THREAD_STATE
static CYTHON_INLINE void __Pyx_ErrRestoreInState(PyThreadState *tstate, PyObject *type, PyObject *value, PyObject *tb) {
    PyObject *tmp_type, *tmp_value, *tmp_tb;
    tmp_type = tstate->curexc_type;
    tmp_value = tstate->curexc_value;
    tmp_tb = tstate->curexc_traceback;
    tstate->curexc_type = type;
    tstate->curexc_value = value;
    tstate->curexc_traceback = tb;
    Py_XDECREF(tmp_type);
    Py_XDECREF(tmp_value);
    Py_XDECREF(tmp_tb);
}
static CYTHON_INLINE void __Pyx_ErrFetchInState(PyThreadState *tstate, PyObject **type, PyObject **value, PyObject **tb) {
    *type = tstate->curexc_type;
    *value = tstate->curexc_value;
    *tb = tstate->curexc_traceback;
    tstate->curexc_type = 0;
    tstate->curexc_value = 0;
    tstate->curexc_traceback = 0;
}
#endif

/* RaiseException */
#if PY_MAJOR_VERSION < 3
static void __Pyx_Raise(PyObject *type, PyObject *value, PyObject *tb,
                        CYTHON_UNUSED PyObject *cause) {
    __Pyx_PyThreadState_declare
    Py_XINCREF(type);
    if (!value || value == Py_None)
        value = NULL;
    else
        Py_INCREF(value);
    if (!tb || tb == Py_None)
        tb = NULL;
    else {
        Py_INCREF(tb);
        if (!PyTraceBack_Check(tb)) {
            PyErr_SetString(PyExc_TypeError,
                "raise: arg 3 must be a traceback or None");
            goto raise_error;
        }
    }
    if (PyType_Check(type)) {
#if CYTHON_COMPILING_IN_PYPY
        if (!value) {
            Py_INCREF(Py_None);
            value = Py_None;
        }
#endif
        PyErr_NormalizeException(&type, &value, &tb);
    } else {
        if (value) {
            PyErr_SetString(PyExc_TypeError,
                "instance exception may not have a separate value");
            goto raise_error;
        }
        value = type;
        type = (PyObject*) Py_TYPE(type);
        Py_INCREF(type);
        if (!PyType_IsSubtype((PyTypeObject *)type, (PyTypeObject *)PyExc_BaseException)) {
            PyErr_SetString(PyExc_TypeError,
                "raise: exception class must be a subclass of BaseException");
            goto raise_error;
        }
    }
    __Pyx_PyThreadState_assign
    __Pyx_ErrRestore(type, value, tb);
    return;
raise_error:
    Py_XDECREF(value);
    Py_XDECREF(type);
    Py_XDECREF(tb);
    return;
}
#else
static void __Pyx_Raise(PyObject *type, PyObject *value, PyObject *tb, PyObject *cause) {
    PyObject* owned_instance = NULL;
    if (tb == Py_None) {
        tb = 0;
    } else if (tb && !PyTraceBack_Check(tb)) {
        PyErr_SetString(PyExc_TypeError,
            "raise: arg 3 must be a traceback or None");
        goto bad;
    }
    if (value == Py_None)
        value = 0;
    if (PyExceptionInstance_Check(type)) {
        if (value) {
            PyErr_SetString(PyExc_TypeError,
                "instance exception may not have a separate value");
            goto bad;
        }
        value = type;
        type = (PyObject*) Py_TYPE(value);
    } else if (PyExceptionClass_Check(type)) {
        PyObject *instance_class = NULL;
        if (value && PyExceptionInstance_Check(value)) {
            instance_class = (PyObject*) Py_TYPE(value);
            if (instance_class != type) {
                int is_subclass = PyObject_IsSubclass(instance_class, type);
                if (!is_subclass) {
                    instance_class = NULL;
                } else if (unlikely(is_subclass == -1)) {
                    goto bad;
                } else {
                    type = instance_class;
                }
            }
        }
        if (!instance_class) {
            PyObject *args;
            if (!value)
                args = PyTuple_New(0);
            else if (PyTuple_Check(value)) {
                Py_INCREF(value);
                args = value;
            } else
                args = PyTuple_Pack(1, value);
            if (!args)
                goto bad;
            owned_instance = PyObject_Call(type, args, NULL);
            Py_DECREF(args);
            if (!owned_instance)
                goto bad;
            value = owned_instance;
            if (!PyExceptionInstance_Check(value)) {
                PyErr_Format(PyExc_TypeError,
                             "calling %R should have returned an instance of "
                             "BaseException, not %R",
                             type, Py_TYPE(value));
                goto bad;
            }
        }
    } else {
        PyErr_SetString(PyExc_TypeError,
            "raise: exception class must be a subclass of BaseException");
        goto bad;
    }
    if (cause) {
        PyObject *fixed_cause;
        if (cause == Py_None) {
            fixed_cause = NULL;
        } else if (PyExceptionClass_Check(cause)) {
            fixed_cause = PyObject_CallObject(cause, NULL);
            if (fixed_cause == NULL)
                goto bad;
        } else if (PyExceptionInstance_Check(cause)) {
            fixed_cause = cause;
            Py_INCREF(fixed_cause);
        } else {
            PyErr_SetString(PyExc_TypeError,
                            "exception causes must derive from "
                            "BaseException");
            goto bad;
        }
        PyException_SetCause(value, fixed_cause);
    }
    PyErr_SetObject(type, value);
    if (tb) {
#if CYTHON_FAST_THREAD_STATE
        PyThreadState *tstate = __Pyx_PyThreadState_Current;
        PyObject* tmp_tb = tstate->curexc_traceback;
        if (tb != tmp_tb) {
            Py_INCREF(tb);
            tstate->curexc_traceback = tb;
            Py_XDECREF(tmp_tb);
        }
#else
        PyObject *tmp_type, *tmp_value, *tmp_tb;
        PyErr_Fetch(&tmp_type, &tmp_value, &tmp_tb);
        Py_INCREF(tb);
        PyErr_Restore(tmp_type, tmp_value, tb);
        Py_XDECREF(tmp_tb);
#endif
    }
bad:
    Py_XDECREF(owned_instance);
    return;
}
#endif

/* GetAttr */
static CYTHON_INLINE PyObject *__Pyx_GetAttr(PyObject *o, PyObject *n) {
#if CYTHON_USE_TYPE_SLOTS
#if PY_MAJOR_VERSION >= 3
    if (likely(PyUnicode_Check(n)))
#else
    if (likely(PyString_Check(n)))
#endif
        return __Pyx_PyObject_GetAttrStr(o, n);
#endif
    return PyObject_GetAttr(o, n);
}

/* HasAttr */
static CYTHON_INLINE int __Pyx_HasAttr(PyObject *o, PyObject *n) {
    PyObject *r;
    if (unlikely(!__Pyx_PyBaseString_Check(n))) {
        PyErr_SetString(PyExc_TypeError,
                        "hasattr(): attribute name must be string");
        return -1;
    }
    r = __Pyx_GetAttr(o, n);
    if (unlikely(!r)) {
        PyErr_Clear();
        return 0;
    } else {
        Py_DECREF(r);
        return 1;
    }
}

/* ObjectGetItem */
#if CYTHON_USE_TYPE_SLOTS
static PyObject *__Pyx_PyObject_GetIndex(PyObject *obj, PyObject* index) {
    PyObject *runerr = NULL;
    Py_ssize_t key_value;
    PySequenceMethods *m = Py_TYPE(obj)->tp_as_sequence;
    if (unlikely(!(m && m->sq_item))) {
        PyErr_Format(PyExc_TypeError, "'%.200s' object is not subscriptable", Py_TYPE(obj)->tp_name);
        return NULL;
    }
    key_value = __Pyx_PyIndex_AsSsize_t(index);
    if (likely(key_value != -1 || !(runerr = PyErr_Occurred()))) {
        return __Pyx_GetItemInt_Fast(obj, key_value, 0, 1, 1);
    }
    if (PyErr_GivenExceptionMatches(runerr, PyExc_OverflowError)) {
        PyErr_Clear();
        PyErr_Format(PyExc_IndexError, "cannot fit '%.200s' into an index-sized integer", Py_TYPE(index)->tp_name);
    }
    return NULL;
}
static PyObject *__Pyx_PyObject_GetItem(PyObject *obj, PyObject* key) {
    PyMappingMethods *m = Py_TYPE(obj)->tp_as_mapping;
    if (likely(m && m->mp_subscript)) {
        return m->mp_subscript(obj, key);
    }
    return __Pyx_PyObject_GetIndex(obj, key);
}
#endif

/* Import */
static PyObject *__Pyx_Import(PyObject *name, PyObject *from_list, int level) {
    PyObject *empty_list = 0;
    PyObject *module = 0;
    PyObject *global_dict = 0;
    PyObject *empty_dict = 0;
    PyObject *list;
    #if PY_MAJOR_VERSION < 3
    PyObject *py_import;
    py_import = __Pyx_PyObject_GetAttrStr(__pyx_b, __pyx_n_s_import);
    if (!py_import)
        goto bad;
    #endif
    if (from_list)
        list = from_list;
    else {
        empty_list = PyList_New(0);
        if (!empty_list)
            goto bad;
        list = empty_list;
    }
    global_dict = PyModule_GetDict(__pyx_m);
    if (!global_dict)
        goto bad;
    empty_dict = PyDict_New();
    if (!empty_dict)
        goto bad;
    {
        #if PY_MAJOR_VERSION >= 3
        if (level == -1) {
            if ((1) && (strchr(__Pyx_MODULE_NAME, '.'))) {
                module = PyImport_ImportModuleLevelObject(
                    name, global_dict, empty_dict, list, 1);
                if (!module) {
                    if (!PyErr_ExceptionMatches(PyExc_ImportError))
                        goto bad;
                    PyErr_Clear();
                }
            }
            level = 0;
        }
        #endif
        if (!module) {
            #if PY_MAJOR_VERSION < 3
            PyObject *py_level = PyInt_FromLong(level);
            if (!py_level)
                goto bad;
            module = PyObject_CallFunctionObjArgs(py_import,
                name, global_dict, empty_dict, list, py_level, (PyObject *)NULL);
            Py_DECREF(py_level);
            #else
            module = PyImport_ImportModuleLevelObject(
                name, global_dict, empty_dict, list, level);
            #endif
        }
    }
bad:
    #if PY_MAJOR_VERSION < 3
    Py_XDECREF(py_import);
    #endif
    Py_XDECREF(empty_list);
    Py_XDECREF(empty_dict);
    return module;
}

/* ImportFrom */
static PyObject* __Pyx_ImportFrom(PyObject* module, PyObject* name) {
    PyObject* value = __Pyx_PyObject_GetAttrStr(module, name);
    if (unlikely(!value) && PyErr_ExceptionMatches(PyExc_AttributeError)) {
        PyErr_Format(PyExc_ImportError,
        #if PY_MAJOR_VERSION < 3
            "cannot import name %.230s", PyString_AS_STRING(name));
        #else
            "cannot import name %S", name);
        #endif
    }
    return value;
}

/* FetchCommonType */
static PyTypeObject* __Pyx_FetchCommonType(PyTypeObject* type) {
    PyObject* fake_module;
    PyTypeObject* cached_type = NULL;
    fake_module = PyImport_AddModule((char*) "_cython_" CYTHON_ABI);
    if (!fake_module) return NULL;
    Py_INCREF(fake_module);
    cached_type = (PyTypeObject*) PyObject_GetAttrString(fake_module, type->tp_name);
    if (cached_type) {
        if (!PyType_Check((PyObject*)cached_type)) {
            PyErr_Format(PyExc_TypeError,
                "Shared Cython type %.200s is not a type object",
                type->tp_name);
            goto bad;
        }
        if (cached_type->tp_basicsize != type->tp_basicsize) {
            PyErr_Format(PyExc_TypeError,
                "Shared Cython type %.200s has the wrong size, try recompiling",
                type->tp_name);
            goto bad;
        }
    } else {
        if (!PyErr_ExceptionMatches(PyExc_AttributeError)) goto bad;
        PyErr_Clear();
        if (PyType_Ready(type) < 0) goto bad;
        if (PyObject_SetAttrString(fake_module, type->tp_name, (PyObject*) type) < 0)
            goto bad;
        Py_INCREF(type);
        cached_type = type;
    }
done:
    Py_DECREF(fake_module);
    return cached_type;
bad:
    Py_XDECREF(cached_type);
    cached_type = NULL;
    goto done;
}

/* CythonFunctionShared */
#include <structmember.h>
static PyObject *
__Pyx_CyFunction_get_doc(__pyx_CyFunctionObject *op, CYTHON_UNUSED void *closure)
{
    if (unlikely(op->func_doc == NULL)) {
        if (op->func.m_ml->ml_doc) {
#if PY_MAJOR_VERSION >= 3
            op->func_doc = PyUnicode_FromString(op->func.m_ml->ml_doc);
#else
            op->func_doc = PyString_FromString(op->func.m_ml->ml_doc);
#endif
            if (unlikely(op->func_doc == NULL))
                return NULL;
        } else {
            Py_INCREF(Py_None);
            return Py_None;
        }
    }
    Py_INCREF(op->func_doc);
    return op->func_doc;
}
static int
__Pyx_CyFunction_set_doc(__pyx_CyFunctionObject *op, PyObject *value, CYTHON_UNUSED void *context)
{
    PyObject *tmp = op->func_doc;
    if (value == NULL) {
        value = Py_None;
    }
    Py_INCREF(value);
    op->func_doc = value;
    Py_XDECREF(tmp);
    return 0;
}
static PyObject *
__Pyx_CyFunction_get_name(__pyx_CyFunctionObject *op, CYTHON_UNUSED void *context)
{
    if (unlikely(op->func_name == NULL)) {
#if PY_MAJOR_VERSION >= 3
        op->func_name = PyUnicode_InternFromString(op->func.m_ml->ml_name);
#else
        op->func_name = PyString_InternFromString(op->func.m_ml->ml_name);
#endif
        if (unlikely(op->func_name == NULL))
            return NULL;
    }
    Py_INCREF(op->func_name);
    return op->func_name;
}
static int
__Pyx_CyFunction_set_name(__pyx_CyFunctionObject *op, PyObject *value, CYTHON_UNUSED void *context)
{
    PyObject *tmp;
#if PY_MAJOR_VERSION >= 3
    if (unlikely(value == NULL || !PyUnicode_Check(value)))
#else
    if (unlikely(value == NULL || !PyString_Check(value)))
#endif
    {
        PyErr_SetString(PyExc_TypeError,
                        "__name__ must be set to a string object");
        return -1;
    }
    tmp = op->func_name;
    Py_INCREF(value);
    op->func_name = value;
    Py_XDECREF(tmp);
    return 0;
}
static PyObject *
__Pyx_CyFunction_get_qualname(__pyx_CyFunctionObject *op, CYTHON_UNUSED void *context)
{
    Py_INCREF(op->func_qualname);
    return op->func_qualname;
}
static int
__Pyx_CyFunction_set_qualname(__pyx_CyFunctionObject *op, PyObject *value, CYTHON_UNUSED void *context)
{
    PyObject *tmp;
#if PY_MAJOR_VERSION >= 3
    if (unlikely(value == NULL || !PyUnicode_Check(value)))
#else
    if (unlikely(value == NULL || !PyString_Check(value)))
#endif
    {
        PyErr_SetString(PyExc_TypeError,
                        "__qualname__ must be set to a string object");
        return -1;
    }
    tmp = op->func_qualname;
    Py_INCREF(value);
    op->func_qualname = value;
    Py_XDECREF(tmp);
    return 0;
}
static PyObject *
__Pyx_CyFunction_get_self(__pyx_CyFunctionObject *m, CYTHON_UNUSED void *closure)
{
    PyObject *self;
    self = m->func_closure;
    if (self == NULL)
        self = Py_None;
    Py_INCREF(self);
    return self;
}
static PyObject *
__Pyx_CyFunction_get_dict(__pyx_CyFunctionObject *op, CYTHON_UNUSED void *context)
{
    if (unlikely(op->func_dict == NULL)) {
        op->func_dict = PyDict_New();
        if (unlikely(op->func_dict == NULL))
            return NULL;
    }
    Py_INCREF(op->func_dict);
    return op->func_dict;
}
static int
__Pyx_CyFunction_set_dict(__pyx_CyFunctionObject *op, PyObject *value, CYTHON_UNUSED void *context)
{
    PyObject *tmp;
    if (unlikely(value == NULL)) {
        PyErr_SetString(PyExc_TypeError,
               "function's dictionary may not be deleted");
        return -1;
    }
    if (unlikely(!PyDict_Check(value))) {
        PyErr_SetString(PyExc_TypeError,
               "setting function's dictionary to a non-dict");
        return -1;
    }
    tmp = op->func_dict;
    Py_INCREF(value);
    op->func_dict = value;
    Py_XDECREF(tmp);
    return 0;
}
static PyObject *
__Pyx_CyFunction_get_globals(__pyx_CyFunctionObject *op, CYTHON_UNUSED void *context)
{
    Py_INCREF(op->func_globals);
    return op->func_globals;
}
static PyObject *
__Pyx_CyFunction_get_closure(CYTHON_UNUSED __pyx_CyFunctionObject *op, CYTHON_UNUSED void *context)
{
    Py_INCREF(Py_None);
    return Py_None;
}
static PyObject *
__Pyx_CyFunction_get_code(__pyx_CyFunctionObject *op, CYTHON_UNUSED void *context)
{
    PyObject* result = (op->func_code) ? op->func_code : Py_None;
    Py_INCREF(result);
    return result;
}
static int
__Pyx_CyFunction_init_defaults(__pyx_CyFunctionObject *op) {
    int result = 0;
    PyObject *res = op->defaults_getter((PyObject *) op);
    if (unlikely(!res))
        return -1;
    #if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS
    op->defaults_tuple = PyTuple_GET_ITEM(res, 0);
    Py_INCREF(op->defaults_tuple);
    op->defaults_kwdict = PyTuple_GET_ITEM(res, 1);
    Py_INCREF(op->defaults_kwdict);
    #else
    op->defaults_tuple = PySequence_ITEM(res, 0);
    if (unlikely(!op->defaults_tuple)) result = -1;
    else {
        op->defaults_kwdict = PySequence_ITEM(res, 1);
        if (unlikely(!op->defaults_kwdict)) result = -1;
    }
    #endif
    Py_DECREF(res);
    return result;
}
static int
__Pyx_CyFunction_set_defaults(__pyx_CyFunctionObject *op, PyObject* value, CYTHON_UNUSED void *context) {
    PyObject* tmp;
    if (!value) {
        value = Py_None;
    } else if (value != Py_None && !PyTuple_Check(value)) {
        PyErr_SetString(PyExc_TypeError,
                        "__defaults__ must be set to a tuple object");
        return -1;
    }
    Py_INCREF(value);
    tmp = op->defaults_tuple;
    op->defaults_tuple = value;
    Py_XDECREF(tmp);
    return 0;
}
static PyObject *
__Pyx_CyFunction_get_defaults(__pyx_CyFunctionObject *op, CYTHON_UNUSED void *context) {
    PyObject* result = op->defaults_tuple;
    if (unlikely(!result)) {
        if (op->defaults_getter) {
            if (__Pyx_CyFunction_init_defaults(op) < 0) return NULL;
            result = op->defaults_tuple;
        } else {
            result = Py_None;
        }
    }
    Py_INCREF(result);
    return result;
}
static int
__Pyx_CyFunction_set_kwdefaults(__pyx_CyFunctionObject *op, PyObject* value, CYTHON_UNUSED void *context) {
    PyObject* tmp;
    if (!value) {
        value = Py_None;
    } else if (value != Py_None && !PyDict_Check(value)) {
        PyErr_SetString(PyExc_TypeError,
                        "__kwdefaults__ must be set to a dict object");
        return -1;
    }
    Py_INCREF(value);
    tmp = op->defaults_kwdict;
    op->defaults_kwdict = value;
    Py_XDECREF(tmp);
    return 0;
}
static PyObject *
__Pyx_CyFunction_get_kwdefaults(__pyx_CyFunctionObject *op, CYTHON_UNUSED void *context) {
    PyObject* result = op->defaults_kwdict;
    if (unlikely(!result)) {
        if (op->defaults_getter) {
            if (__Pyx_CyFunction_init_defaults(op) < 0) return NULL;
            result = op->defaults_kwdict;
        } else {
            result = Py_None;
        }
    }
    Py_INCREF(result);
    return result;
}
static int
__Pyx_CyFunction_set_annotations(__pyx_CyFunctionObject *op, PyObject* value, CYTHON_UNUSED void *context) {
    PyObject* tmp;
    if (!value || value == Py_None) {
        value = NULL;
    } else if (!PyDict_Check(value)) {
        PyErr_SetString(PyExc_TypeError,
                        "__annotations__ must be set to a dict object");
        return -1;
    }
    Py_XINCREF(value);
    tmp = op->func_annotations;
    op->func_annotations = value;
    Py_XDECREF(tmp);
    return 0;
}
static PyObject *
__Pyx_CyFunction_get_annotations(__pyx_CyFunctionObject *op, CYTHON_UNUSED void *context) {
    PyObject* result = op->func_annotations;
    if (unlikely(!result)) {
        result = PyDict_New();
        if (unlikely(!result)) return NULL;
        op->func_annotations = result;
    }
    Py_INCREF(result);
    return result;
}
static PyGetSetDef __pyx_CyFunction_getsets[] = {
    {(char *) "func_doc", (getter)__Pyx_CyFunction_get_doc, (setter)__Pyx_CyFunction_set_doc, 0, 0},
    {(char *) "__doc__",  (getter)__Pyx_CyFunction_get_doc, (setter)__Pyx_CyFunction_set_doc, 0, 0},
    {(char *) "func_name", (getter)__Pyx_CyFunction_get_name, (setter)__Pyx_CyFunction_set_name, 0, 0},
    {(char *) "__name__", (getter)__Pyx_CyFunction_get_name, (setter)__Pyx_CyFunction_set_name, 0, 0},
    {(char *) "__qualname__", (getter)__Pyx_CyFunction_get_qualname, (setter)__Pyx_CyFunction_set_qualname, 0, 0},
    {(char *) "__self__", (getter)__Pyx_CyFunction_get_self, 0, 0, 0},
    {(char *) "func_dict", (getter)__Pyx_CyFunction_get_dict, (setter)__Pyx_CyFunction_set_dict, 0, 0},
    {(char *) "__dict__", (getter)__Pyx_CyFunction_get_dict, (setter)__Pyx_CyFunction_set_dict, 0, 0},
    {(char *) "func_globals", (getter)__Pyx_CyFunction_get_globals, 0, 0, 0},
    {(char *) "__globals__", (getter)__Pyx_CyFunction_get_globals, 0, 0, 0},
    {(char *) "func_closure", (getter)__Pyx_CyFunction_get_closure, 0, 0, 0},
    {(char *) "__closure__", (getter)__Pyx_CyFunction_get_closure, 0, 0, 0},
    {(char *) "func_code", (getter)__Pyx_CyFunction_get_code, 0, 0, 0},
    {(char *) "__code__", (getter)__Pyx_CyFunction_get_code, 0, 0, 0},
    {(char *) "func_defaults", (getter)__Pyx_CyFunction_get_defaults, (setter)__Pyx_CyFunction_set_defaults, 0, 0},
    {(char *) "__defaults__", (getter)__Pyx_CyFunction_get_defaults, (setter)__Pyx_CyFunction_set_defaults, 0, 0},
    {(char *) "__kwdefaults__", (getter)__Pyx_CyFunction_get_kwdefaults, (setter)__Pyx_CyFunction_set_kwdefaults, 0, 0},
    {(char *) "__annotations__", (getter)__Pyx_CyFunction_get_annotations, (setter)__Pyx_CyFunction_set_annotations, 0, 0},
    {0, 0, 0, 0, 0}
};
static PyMemberDef __pyx_CyFunction_members[] = {
    {(char *) "__module__", T_OBJECT, offsetof(PyCFunctionObject, m_module), PY_WRITE_RESTRICTED, 0},
    {0, 0, 0,  0, 0}
};
static PyObject *
__Pyx_CyFunction_reduce(__pyx_CyFunctionObject *m, CYTHON_UNUSED PyObject *args)
{
#if PY_MAJOR_VERSION >= 3
    Py_INCREF(m->func_qualname);
    return m->func_qualname;
#else
    return PyString_FromString(m->func.m_ml->ml_name);
#endif
}
static PyMethodDef __pyx_CyFunction_methods[] = {
    {"__reduce__", (PyCFunction)__Pyx_CyFunction_reduce, METH_VARARGS, 0},
    {0, 0, 0, 0}
};
#if PY_VERSION_HEX < 0x030500A0
#define __Pyx_CyFunction_weakreflist(cyfunc) ((cyfunc)->func_weakreflist)
#else
#define __Pyx_CyFunction_weakreflist(cyfunc) ((cyfunc)->func.m_weakreflist)
#endif
static PyObject *__Pyx_CyFunction_Init(__pyx_CyFunctionObject *op, PyMethodDef *ml, int flags, PyObject* qualname,
                                       PyObject *closure, PyObject *module, PyObject* globals, PyObject* code) {
    if (unlikely(op == NULL))
        return NULL;
    op->flags = flags;
    __Pyx_CyFunction_weakreflist(op) = NULL;
    op->func.m_ml = ml;
    op->func.m_self = (PyObject *) op;
    Py_XINCREF(closure);
    op->func_closure = closure;
    Py_XINCREF(module);
    op->func.m_module = module;
    op->func_dict = NULL;
    op->func_name = NULL;
    Py_INCREF(qualname);
    op->func_qualname = qualname;
    op->func_doc = NULL;
    op->func_classobj = NULL;
    op->func_globals = globals;
    Py_INCREF(op->func_globals);
    Py_XINCREF(code);
    op->func_code = code;
    op->defaults_pyobjects = 0;
    op->defaults_size = 0;
    op->defaults = NULL;
    op->defaults_tuple = NULL;
    op->defaults_kwdict = NULL;
    op->defaults_getter = NULL;
    op->func_annotations = NULL;
    return (PyObject *) op;
}
static int
__Pyx_CyFunction_clear(__pyx_CyFunctionObject *m)
{
    Py_CLEAR(m->func_closure);
    Py_CLEAR(m->func.m_module);
    Py_CLEAR(m->func_dict);
    Py_CLEAR(m->func_name);
    Py_CLEAR(m->func_qualname);
    Py_CLEAR(m->func_doc);
    Py_CLEAR(m->func_globals);
    Py_CLEAR(m->func_code);
    Py_CLEAR(m->func_classobj);
    Py_CLEAR(m->defaults_tuple);
    Py_CLEAR(m->defaults_kwdict);
    Py_CLEAR(m->func_annotations);
    if (m->defaults) {
        PyObject **pydefaults = __Pyx_CyFunction_Defaults(PyObject *, m);
        int i;
        for (i = 0; i < m->defaults_pyobjects; i++)
            Py_XDECREF(pydefaults[i]);
        PyObject_Free(m->defaults);
        m->defaults = NULL;
    }
    return 0;
}
static void __Pyx__CyFunction_dealloc(__pyx_CyFunctionObject *m)
{
    if (__Pyx_CyFunction_weakreflist(m) != NULL)
        PyObject_ClearWeakRefs((PyObject *) m);
    __Pyx_CyFunction_clear(m);
    PyObject_GC_Del(m);
}
static void __Pyx_CyFunction_dealloc(__pyx_CyFunctionObject *m)
{
    PyObject_GC_UnTrack(m);
    __Pyx__CyFunction_dealloc(m);
}
static int __Pyx_CyFunction_traverse(__pyx_CyFunctionObject *m, visitproc visit, void *arg)
{
    Py_VISIT(m->func_closure);
    Py_VISIT(m->func.m_module);
    Py_VISIT(m->func_dict);
    Py_VISIT(m->func_name);
    Py_VISIT(m->func_qualname);
    Py_VISIT(m->func_doc);
    Py_VISIT(m->func_globals);
    Py_VISIT(m->func_code);
    Py_VISIT(m->func_classobj);
    Py_VISIT(m->defaults_tuple);
    Py_VISIT(m->defaults_kwdict);
    if (m->defaults) {
        PyObject **pydefaults = __Pyx_CyFunction_Defaults(PyObject *, m);
        int i;
        for (i = 0; i < m->defaults_pyobjects; i++)
            Py_VISIT(pydefaults[i]);
    }
    return 0;
}
static PyObject *__Pyx_CyFunction_descr_get(PyObject *func, PyObject *obj, PyObject *type)
{
#if PY_MAJOR_VERSION < 3
    __pyx_CyFunctionObject *m = (__pyx_CyFunctionObject *) func;
    if (m->flags & __Pyx_CYFUNCTION_STATICMETHOD) {
        Py_INCREF(func);
        return func;
    }
    if (m->flags & __Pyx_CYFUNCTION_CLASSMETHOD) {
        if (type == NULL)
            type = (PyObject *)(Py_TYPE(obj));
        return __Pyx_PyMethod_New(func, type, (PyObject *)(Py_TYPE(type)));
    }
    if (obj == Py_None)
        obj = NULL;
#endif
    return __Pyx_PyMethod_New(func, obj, type);
}
static PyObject*
__Pyx_CyFunction_repr(__pyx_CyFunctionObject *op)
{
#if PY_MAJOR_VERSION >= 3
    return PyUnicode_FromFormat("<cyfunction %U at %p>",
                                op->func_qualname, (void *)op);
#else
    return PyString_FromFormat("<cyfunction %s at %p>",
                               PyString_AsString(op->func_qualname), (void *)op);
#endif
}
static PyObject * __Pyx_CyFunction_CallMethod(PyObject *func, PyObject *self, PyObject *arg, PyObject *kw) {
    PyCFunctionObject* f = (PyCFunctionObject*)func;
    PyCFunction meth = f->m_ml->ml_meth;
    Py_ssize_t size;
    switch (f->m_ml->ml_flags & (METH_VARARGS | METH_KEYWORDS | METH_NOARGS | METH_O)) {
    case METH_VARARGS:
        if (likely(kw == NULL || PyDict_Size(kw) == 0))
            return (*meth)(self, arg);
        break;
    case METH_VARARGS | METH_KEYWORDS:
        return (*(PyCFunctionWithKeywords)(void*)meth)(self, arg, kw);
    case METH_NOARGS:
        if (likely(kw == NULL || PyDict_Size(kw) == 0)) {
            size = PyTuple_GET_SIZE(arg);
            if (likely(size == 0))
                return (*meth)(self, NULL);
            PyErr_Format(PyExc_TypeError,
                "%.200s() takes no arguments (%" CYTHON_FORMAT_SSIZE_T "d given)",
                f->m_ml->ml_name, size);
            return NULL;
        }
        break;
    case METH_O:
        if (likely(kw == NULL || PyDict_Size(kw) == 0)) {
            size = PyTuple_GET_SIZE(arg);
            if (likely(size == 1)) {
                PyObject *result, *arg0;
                #if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS
                arg0 = PyTuple_GET_ITEM(arg, 0);
                #else
                arg0 = PySequence_ITEM(arg, 0); if (unlikely(!arg0)) return NULL;
                #endif
                result = (*meth)(self, arg0);
                #if !(CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS)
                Py_DECREF(arg0);
                #endif
                return result;
            }
            PyErr_Format(PyExc_TypeError,
                "%.200s() takes exactly one argument (%" CYTHON_FORMAT_SSIZE_T "d given)",
                f->m_ml->ml_name, size);
            return NULL;
        }
        break;
    default:
        PyErr_SetString(PyExc_SystemError, "Bad call flags in "
                        "__Pyx_CyFunction_Call. METH_OLDARGS is no "
                        "longer supported!");
        return NULL;
    }
    PyErr_Format(PyExc_TypeError, "%.200s() takes no keyword arguments",
                 f->m_ml->ml_name);
    return NULL;
}
static CYTHON_INLINE PyObject *__Pyx_CyFunction_Call(PyObject *func, PyObject *arg, PyObject *kw) {
    return __Pyx_CyFunction_CallMethod(func, ((PyCFunctionObject*)func)->m_self, arg, kw);
}
static PyObject *__Pyx_CyFunction_CallAsMethod(PyObject *func, PyObject *args, PyObject *kw) {
    PyObject *result;
    __pyx_CyFunctionObject *cyfunc = (__pyx_CyFunctionObject *) func;
    if ((cyfunc->flags & __Pyx_CYFUNCTION_CCLASS) && !(cyfunc->flags & __Pyx_CYFUNCTION_STATICMETHOD)) {
        Py_ssize_t argc;
        PyObject *new_args;
        PyObject *self;
        argc = PyTuple_GET_SIZE(args);
        new_args = PyTuple_GetSlice(args, 1, argc);
        if (unlikely(!new_args))
            return NULL;
        self = PyTuple_GetItem(args, 0);
        if (unlikely(!self)) {
            Py_DECREF(new_args);
#if PY_MAJOR_VERSION > 2
            PyErr_Format(PyExc_TypeError,
                         "unbound method %.200S() needs an argument",
                         cyfunc->func_qualname);
#else
            PyErr_SetString(PyExc_TypeError,
                            "unbound method needs an argument");
#endif
            return NULL;
        }
        result = __Pyx_CyFunction_CallMethod(func, self, new_args, kw);
        Py_DECREF(new_args);
    } else {
        result = __Pyx_CyFunction_Call(func, args, kw);
    }
    return result;
}
static PyTypeObject __pyx_CyFunctionType_type = {
    PyVarObject_HEAD_INIT(0, 0)
    "cython_function_or_method",
    sizeof(__pyx_CyFunctionObject),
    0,
    (destructor) __Pyx_CyFunction_dealloc,
    0,
    0,
    0,
#if PY_MAJOR_VERSION < 3
    0,
#else
    0,
#endif
    (reprfunc) __Pyx_CyFunction_repr,
    0,
    0,
    0,
    0,
    __Pyx_CyFunction_CallAsMethod,
    0,
    0,
    0,
    0,
    Py_TPFLAGS_DEFAULT | Py_TPFLAGS_HAVE_GC,
    0,
    (traverseproc) __Pyx_CyFunction_traverse,
    (inquiry) __Pyx_CyFunction_clear,
    0,
#if PY_VERSION_HEX < 0x030500A0
    offsetof(__pyx_CyFunctionObject, func_weakreflist),
#else
    offsetof(PyCFunctionObject, m_weakreflist),
#endif
    0,
    0,
    __pyx_CyFunction_methods,
    __pyx_CyFunction_members,
    __pyx_CyFunction_getsets,
    0,
    0,
    __Pyx_CyFunction_descr_get,
    0,
    offsetof(__pyx_CyFunctionObject, func_dict),
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
#if PY_VERSION_HEX >= 0x030400a1
    0,
#endif
#if PY_VERSION_HEX >= 0x030800b1 && (!CYTHON_COMPILING_IN_PYPY || PYPY_VERSION_NUM >= 0x07030800)
    0,
#endif
#if PY_VERSION_HEX >= 0x030800b4 && PY_VERSION_HEX < 0x03090000
    0,
#endif
#if PY_VERSION_HEX >= 0x030C0000
    0,
#endif
#if CYTHON_COMPILING_IN_PYPY && PY_VERSION_HEX >= 0x03090000 && PY_VERSION_HEX < 0x030a0000
    0,
#endif
};
static int __pyx_CyFunction_init(void) {
    __pyx_CyFunctionType = __Pyx_FetchCommonType(&__pyx_CyFunctionType_type);
    if (unlikely(__pyx_CyFunctionType == NULL)) {
        return -1;
    }
    return 0;
}
static CYTHON_INLINE void *__Pyx_CyFunction_InitDefaults(PyObject *func, size_t size, int pyobjects) {
    __pyx_CyFunctionObject *m = (__pyx_CyFunctionObject *) func;
    m->defaults = PyObject_Malloc(size);
    if (unlikely(!m->defaults))
        return PyErr_NoMemory();
    memset(m->defaults, 0, size);
    m->defaults_pyobjects = pyobjects;
    m->defaults_size = size;
    return m->defaults;
}
static CYTHON_INLINE void __Pyx_CyFunction_SetDefaultsTuple(PyObject *func, PyObject *tuple) {
    __pyx_CyFunctionObject *m = (__pyx_CyFunctionObject *) func;
    m->defaults_tuple = tuple;
    Py_INCREF(tuple);
}
static CYTHON_INLINE void __Pyx_CyFunction_SetDefaultsKwDict(PyObject *func, PyObject *dict) {
    __pyx_CyFunctionObject *m = (__pyx_CyFunctionObject *) func;
    m->defaults_kwdict = dict;
    Py_INCREF(dict);
}
static CYTHON_INLINE void __Pyx_CyFunction_SetAnnotationsDict(PyObject *func, PyObject *dict) {
    __pyx_CyFunctionObject *m = (__pyx_CyFunctionObject *) func;
    m->func_annotations = dict;
    Py_INCREF(dict);
}

/* CythonFunction */
static PyObject *__Pyx_CyFunction_New(PyMethodDef *ml, int flags, PyObject* qualname,
                                      PyObject *closure, PyObject *module, PyObject* globals, PyObject* code) {
    PyObject *op = __Pyx_CyFunction_Init(
        PyObject_GC_New(__pyx_CyFunctionObject, __pyx_CyFunctionType),
        ml, flags, qualname, closure, module, globals, code
    );
    if (likely(op)) {
        PyObject_GC_Track(op);
    }
    return op;
}

/* CLineInTraceback */
#ifndef CYTHON_CLINE_IN_TRACEBACK
static int __Pyx_CLineForTraceback(CYTHON_UNUSED PyThreadState *tstate, int c_line) {
    PyObject *use_cline;
    PyObject *ptype, *pvalue, *ptraceback;
#if CYTHON_COMPILING_IN_CPYTHON
    PyObject **cython_runtime_dict;
#endif
    if (unlikely(!__pyx_cython_runtime)) {
        return c_line;
    }
    __Pyx_ErrFetchInState(tstate, &ptype, &pvalue, &ptraceback);
#if CYTHON_COMPILING_IN_CPYTHON
    cython_runtime_dict = _PyObject_GetDictPtr(__pyx_cython_runtime);
    if (likely(cython_runtime_dict)) {
        __PYX_PY_DICT_LOOKUP_IF_MODIFIED(
            use_cline, *cython_runtime_dict,
            __Pyx_PyDict_GetItemStr(*cython_runtime_dict, __pyx_n_s_cline_in_traceback))
    } else
#endif
    {
      PyObject *use_cline_obj = __Pyx_PyObject_GetAttrStr(__pyx_cython_runtime, __pyx_n_s_cline_in_traceback);
      if (use_cline_obj) {
        use_cline = PyObject_Not(use_cline_obj) ? Py_False : Py_True;
        Py_DECREF(use_cline_obj);
      } else {
        PyErr_Clear();
        use_cline = NULL;
      }
    }
    if (!use_cline) {
        c_line = 0;
        (void) PyObject_SetAttr(__pyx_cython_runtime, __pyx_n_s_cline_in_traceback, Py_False);
    }
    else if (use_cline == Py_False || (use_cline != Py_True && PyObject_Not(use_cline) != 0)) {
        c_line = 0;
    }
    __Pyx_ErrRestoreInState(tstate, ptype, pvalue, ptraceback);
    return c_line;
}
#endif

/* CodeObjectCache */
static int __pyx_bisect_code_objects(__Pyx_CodeObjectCacheEntry* entries, int count, int code_line) {
    int start = 0, mid = 0, end = count - 1;
    if (end >= 0 && code_line > entries[end].code_line) {
        return count;
    }
    while (start < end) {
        mid = start + (end - start) / 2;
        if (code_line < entries[mid].code_line) {
            end = mid;
        } else if (code_line > entries[mid].code_line) {
             start = mid + 1;
        } else {
            return mid;
        }
    }
    if (code_line <= entries[mid].code_line) {
        return mid;
    } else {
        return mid + 1;
    }
}
static PyCodeObject *__pyx_find_code_object(int code_line) {
    PyCodeObject* code_object;
    int pos;
    if (unlikely(!code_line) || unlikely(!__pyx_code_cache.entries)) {
        return NULL;
    }
    pos = __pyx_bisect_code_objects(__pyx_code_cache.entries, __pyx_code_cache.count, code_line);
    if (unlikely(pos >= __pyx_code_cache.count) || unlikely(__pyx_code_cache.entries[pos].code_line != code_line)) {
        return NULL;
    }
    code_object = __pyx_code_cache.entries[pos].code_object;
    Py_INCREF(code_object);
    return code_object;
}
static void __pyx_insert_code_object(int code_line, PyCodeObject* code_object) {
    int pos, i;
    __Pyx_CodeObjectCacheEntry* entries = __pyx_code_cache.entries;
    if (unlikely(!code_line)) {
        return;
    }
    if (unlikely(!entries)) {
        entries = (__Pyx_CodeObjectCacheEntry*)PyMem_Malloc(64*sizeof(__Pyx_CodeObjectCacheEntry));
        if (likely(entries)) {
            __pyx_code_cache.entries = entries;
            __pyx_code_cache.max_count = 64;
            __pyx_code_cache.count = 1;
            entries[0].code_line = code_line;
            entries[0].code_object = code_object;
            Py_INCREF(code_object);
        }
        return;
    }
    pos = __pyx_bisect_code_objects(__pyx_code_cache.entries, __pyx_code_cache.count, code_line);
    if ((pos < __pyx_code_cache.count) && unlikely(__pyx_code_cache.entries[pos].code_line == code_line)) {
        PyCodeObject* tmp = entries[pos].code_object;
        entries[pos].code_object = code_object;
        Py_DECREF(tmp);
        return;
    }
    if (__pyx_code_cache.count == __pyx_code_cache.max_count) {
        int new_max = __pyx_code_cache.max_count + 64;
        entries = (__Pyx_CodeObjectCacheEntry*)PyMem_Realloc(
            __pyx_code_cache.entries, ((size_t)new_max) * sizeof(__Pyx_CodeObjectCacheEntry));
        if (unlikely(!entries)) {
            return;
        }
        __pyx_code_cache.entries = entries;
        __pyx_code_cache.max_count = new_max;
    }
    for (i=__pyx_code_cache.count; i>pos; i--) {
        entries[i] = entries[i-1];
    }
    entries[pos].code_line = code_line;
    entries[pos].code_object = code_object;
    __pyx_code_cache.count++;
    Py_INCREF(code_object);
}

/* AddTraceback */
#include "compile.h"
#include "frameobject.h"
#include "traceback.h"
#if PY_VERSION_HEX >= 0x030b00a6
  #ifndef Py_BUILD_CORE
    #define Py_BUILD_CORE 1
  #endif
  #include "internal/pycore_frame.h"
#endif
static PyCodeObject* __Pyx_CreateCodeObjectForTraceback(
            const char *funcname, int c_line,
            int py_line, const char *filename) {
    PyCodeObject *py_code = NULL;
    PyObject *py_funcname = NULL;
    #if PY_MAJOR_VERSION < 3
    PyObject *py_srcfile = NULL;
    py_srcfile = PyString_FromString(filename);
    if (!py_srcfile) goto bad;
    #endif
    if (c_line) {
        #if PY_MAJOR_VERSION < 3
        py_funcname = PyString_FromFormat( "%s (%s:%d)", funcname, __pyx_cfilenm, c_line);
        if (!py_funcname) goto bad;
        #else
        py_funcname = PyUnicode_FromFormat( "%s (%s:%d)", funcname, __pyx_cfilenm, c_line);
        if (!py_funcname) goto bad;
        funcname = PyUnicode_AsUTF8(py_funcname);
        if (!funcname) goto bad;
        #endif
    }
    else {
        #if PY_MAJOR_VERSION < 3
        py_funcname = PyString_FromString(funcname);
        if (!py_funcname) goto bad;
        #endif
    }
    #if PY_MAJOR_VERSION < 3
    py_code = __Pyx_PyCode_New(
        0,
        0,
        0,
        0,
        0,
        __pyx_empty_bytes, /*PyObject *code,*/
        __pyx_empty_tuple, /*PyObject *consts,*/
        __pyx_empty_tuple, /*PyObject *names,*/
        __pyx_empty_tuple, /*PyObject *varnames,*/
        __pyx_empty_tuple, /*PyObject *freevars,*/
        __pyx_empty_tuple, /*PyObject *cellvars,*/
        py_srcfile,   /*PyObject *filename,*/
        py_funcname,  /*PyObject *name,*/
        py_line,
        __pyx_empty_bytes  /*PyObject *lnotab*/
    );
    Py_DECREF(py_srcfile);
    #else
    py_code = PyCode_NewEmpty(filename, funcname, py_line);
    #endif
    Py_XDECREF(py_funcname);  // XDECREF since it's only set on Py3 if cline
    return py_code;
bad:
    Py_XDECREF(py_funcname);
    #if PY_MAJOR_VERSION < 3
    Py_XDECREF(py_srcfile);
    #endif
    return NULL;
}
static void __Pyx_AddTraceback(const char *funcname, int c_line,
                               int py_line, const char *filename) {
    PyCodeObject *py_code = 0;
    PyFrameObject *py_frame = 0;
    PyThreadState *tstate = __Pyx_PyThreadState_Current;
    PyObject *ptype, *pvalue, *ptraceback;
    if (c_line) {
        c_line = __Pyx_CLineForTraceback(tstate, c_line);
    }
    py_code = __pyx_find_code_object(c_line ? -c_line : py_line);
    if (!py_code) {
        __Pyx_ErrFetchInState(tstate, &ptype, &pvalue, &ptraceback);
        py_code = __Pyx_CreateCodeObjectForTraceback(
            funcname, c_line, py_line, filename);
        if (!py_code) {
            /* If the code object creation fails, then we should clear the
               fetched exception references and propagate the new exception */
            Py_XDECREF(ptype);
            Py_XDECREF(pvalue);
            Py_XDECREF(ptraceback);
            goto bad;
        }
        __Pyx_ErrRestoreInState(tstate, ptype, pvalue, ptraceback);
        __pyx_insert_code_object(c_line ? -c_line : py_line, py_code);
    }
    py_frame = PyFrame_New(
        tstate,            /*PyThreadState *tstate,*/
        py_code,           /*PyCodeObject *code,*/
        __pyx_d,    /*PyObject *globals,*/
        0                  /*PyObject *locals*/
    );
    if (!py_frame) goto bad;
    __Pyx_PyFrame_SetLineNumber(py_frame, py_line);
    PyTraceBack_Here(py_frame);
bad:
    Py_XDECREF(py_code);
    Py_XDECREF(py_frame);
}

/* CIntToPy */
static CYTHON_INLINE PyObject* __Pyx_PyInt_From_enum____pyx_t_5spacy_5attrs_attr_id_t(enum __pyx_t_5spacy_5attrs_attr_id_t value) {
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wconversion"
#endif
    const enum __pyx_t_5spacy_5attrs_attr_id_t neg_one = (enum __pyx_t_5spacy_5attrs_attr_id_t) -1, const_zero = (enum __pyx_t_5spacy_5attrs_attr_id_t) 0;
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic pop
#endif
    const int is_unsigned = neg_one > const_zero;
    if (is_unsigned) {
        if (sizeof(enum __pyx_t_5spacy_5attrs_attr_id_t) < sizeof(long)) {
            return PyInt_FromLong((long) value);
        } else if (sizeof(enum __pyx_t_5spacy_5attrs_attr_id_t) <= sizeof(unsigned long)) {
            return PyLong_FromUnsignedLong((unsigned long) value);
#ifdef HAVE_LONG_LONG
        } else if (sizeof(enum __pyx_t_5spacy_5attrs_attr_id_t) <= sizeof(unsigned PY_LONG_LONG)) {
            return PyLong_FromUnsignedLongLong((unsigned PY_LONG_LONG) value);
#endif
        }
    } else {
        if (sizeof(enum __pyx_t_5spacy_5attrs_attr_id_t) <= sizeof(long)) {
            return PyInt_FromLong((long) value);
#ifdef HAVE_LONG_LONG
        } else if (sizeof(enum __pyx_t_5spacy_5attrs_attr_id_t) <= sizeof(PY_LONG_LONG)) {
            return PyLong_FromLongLong((PY_LONG_LONG) value);
#endif
        }
    }
    {
        int one = 1; int little = (int)*(unsigned char *)&one;
        unsigned char *bytes = (unsigned char *)&value;
        return _PyLong_FromByteArray(bytes, sizeof(enum __pyx_t_5spacy_5attrs_attr_id_t),
                                     little, !is_unsigned);
    }
}

/* CIntToPy */
static CYTHON_INLINE PyObject* __Pyx_PyInt_From_long(long value) {
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wconversion"
#endif
    const long neg_one = (long) -1, const_zero = (long) 0;
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic pop
#endif
    const int is_unsigned = neg_one > const_zero;
    if (is_unsigned) {
        if (sizeof(long) < sizeof(long)) {
            return PyInt_FromLong((long) value);
        } else if (sizeof(long) <= sizeof(unsigned long)) {
            return PyLong_FromUnsignedLong((unsigned long) value);
#ifdef HAVE_LONG_LONG
        } else if (sizeof(long) <= sizeof(unsigned PY_LONG_LONG)) {
            return PyLong_FromUnsignedLongLong((unsigned PY_LONG_LONG) value);
#endif
        }
    } else {
        if (sizeof(long) <= sizeof(long)) {
            return PyInt_FromLong((long) value);
#ifdef HAVE_LONG_LONG
        } else if (sizeof(long) <= sizeof(PY_LONG_LONG)) {
            return PyLong_FromLongLong((PY_LONG_LONG) value);
#endif
        }
    }
    {
        int one = 1; int little = (int)*(unsigned char *)&one;
        unsigned char *bytes = (unsigned char *)&value;
        return _PyLong_FromByteArray(bytes, sizeof(long),
                                     little, !is_unsigned);
    }
}

/* Globals */
static PyObject* __Pyx_Globals(void) {
    Py_ssize_t i;
    PyObject *names;
    PyObject *globals = __pyx_d;
    Py_INCREF(globals);
    names = PyObject_Dir(__pyx_m);
    if (!names)
        goto bad;
    for (i = PyList_GET_SIZE(names)-1; i >= 0; i--) {
#if CYTHON_COMPILING_IN_PYPY
        PyObject* name = PySequence_ITEM(names, i);
        if (!name)
            goto bad;
#else
        PyObject* name = PyList_GET_ITEM(names, i);
#endif
        if (!PyDict_Contains(globals, name)) {
            PyObject* value = __Pyx_GetAttr(__pyx_m, name);
            if (!value) {
#if CYTHON_COMPILING_IN_PYPY
                Py_DECREF(name);
#endif
                goto bad;
            }
            if (PyDict_SetItem(globals, name, value) < 0) {
#if CYTHON_COMPILING_IN_PYPY
                Py_DECREF(name);
#endif
                Py_DECREF(value);
                goto bad;
            }
        }
#if CYTHON_COMPILING_IN_PYPY
        Py_DECREF(name);
#endif
    }
    Py_DECREF(names);
    return globals;
bad:
    Py_XDECREF(names);
    Py_XDECREF(globals);
    return NULL;
}

/* CIntFromPyVerify */
#define __PYX_VERIFY_RETURN_INT(target_type, func_type, func_value)\
    __PYX__VERIFY_RETURN_INT(target_type, func_type, func_value, 0)
#define __PYX_VERIFY_RETURN_INT_EXC(target_type, func_type, func_value)\
    __PYX__VERIFY_RETURN_INT(target_type, func_type, func_value, 1)
#define __PYX__VERIFY_RETURN_INT(target_type, func_type, func_value, exc)\
    {\
        func_type value = func_value;\
        if (sizeof(target_type) < sizeof(func_type)) {\
            if (unlikely(value != (func_type) (target_type) value)) {\
                func_type zero = 0;\
                if (exc && unlikely(value == (func_type)-1 && PyErr_Occurred()))\
                    return (target_type) -1;\
                if (is_unsigned && unlikely(value < zero))\
                    goto raise_neg_overflow;\
                else\
                    goto raise_overflow;\
            }\
        }\
        return (target_type) value;\
    }

/* CIntFromPy */
static CYTHON_INLINE long __Pyx_PyInt_As_long(PyObject *x) {
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wconversion"
#endif
    const long neg_one = (long) -1, const_zero = (long) 0;
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic pop
#endif
    const int is_unsigned = neg_one > const_zero;
#if PY_MAJOR_VERSION < 3
    if (likely(PyInt_Check(x))) {
        if (sizeof(long) < sizeof(long)) {
            __PYX_VERIFY_RETURN_INT(long, long, PyInt_AS_LONG(x))
        } else {
            long val = PyInt_AS_LONG(x);
            if (is_unsigned && unlikely(val < 0)) {
                goto raise_neg_overflow;
            }
            return (long) val;
        }
    } else
#endif
    if (likely(PyLong_Check(x))) {
        if (is_unsigned) {
#if CYTHON_USE_PYLONG_INTERNALS
            const digit* digits = ((PyLongObject*)x)->ob_digit;
            switch (Py_SIZE(x)) {
                case  0: return (long) 0;
                case  1: __PYX_VERIFY_RETURN_INT(long, digit, digits[0])
                case 2:
                    if (8 * sizeof(long) > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) >= 2 * PyLong_SHIFT) {
                            return (long) (((((long)digits[1]) << PyLong_SHIFT) | (long)digits[0]));
                        }
                    }
                    break;
                case 3:
                    if (8 * sizeof(long) > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) >= 3 * PyLong_SHIFT) {
                            return (long) (((((((long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0]));
                        }
                    }
                    break;
                case 4:
                    if (8 * sizeof(long) > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) >= 4 * PyLong_SHIFT) {
                            return (long) (((((((((long)digits[3]) << PyLong_SHIFT) | (long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0]));
                        }
                    }
                    break;
            }
#endif
#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX < 0x030C00A7
            if (unlikely(Py_SIZE(x) < 0)) {
                goto raise_neg_overflow;
            }
#else
            {
                int result = PyObject_RichCompareBool(x, Py_False, Py_LT);
                if (unlikely(result < 0))
                    return (long) -1;
                if (unlikely(result == 1))
                    goto raise_neg_overflow;
            }
#endif
            if (sizeof(long) <= sizeof(unsigned long)) {
                __PYX_VERIFY_RETURN_INT_EXC(long, unsigned long, PyLong_AsUnsignedLong(x))
#ifdef HAVE_LONG_LONG
            } else if (sizeof(long) <= sizeof(unsigned PY_LONG_LONG)) {
                __PYX_VERIFY_RETURN_INT_EXC(long, unsigned PY_LONG_LONG, PyLong_AsUnsignedLongLong(x))
#endif
            }
        } else {
#if CYTHON_USE_PYLONG_INTERNALS
            const digit* digits = ((PyLongObject*)x)->ob_digit;
            switch (Py_SIZE(x)) {
                case  0: return (long) 0;
                case -1: __PYX_VERIFY_RETURN_INT(long, sdigit, (sdigit) (-(sdigit)digits[0]))
                case  1: __PYX_VERIFY_RETURN_INT(long,  digit, +digits[0])
                case -2:
                    if (8 * sizeof(long) - 1 > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, long, -(long) (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) - 1 > 2 * PyLong_SHIFT) {
                            return (long) (((long)-1)*(((((long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
                case 2:
                    if (8 * sizeof(long) > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) - 1 > 2 * PyLong_SHIFT) {
                            return (long) ((((((long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
                case -3:
                    if (8 * sizeof(long) - 1 > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, long, -(long) (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) - 1 > 3 * PyLong_SHIFT) {
                            return (long) (((long)-1)*(((((((long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
                case 3:
                    if (8 * sizeof(long) > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) - 1 > 3 * PyLong_SHIFT) {
                            return (long) ((((((((long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
                case -4:
                    if (8 * sizeof(long) - 1 > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, long, -(long) (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) - 1 > 4 * PyLong_SHIFT) {
                            return (long) (((long)-1)*(((((((((long)digits[3]) << PyLong_SHIFT) | (long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
                case 4:
                    if (8 * sizeof(long) > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) - 1 > 4 * PyLong_SHIFT) {
                            return (long) ((((((((((long)digits[3]) << PyLong_SHIFT) | (long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
            }
#endif
            if (sizeof(long) <= sizeof(long)) {
                __PYX_VERIFY_RETURN_INT_EXC(long, long, PyLong_AsLong(x))
#ifdef HAVE_LONG_LONG
            } else if (sizeof(long) <= sizeof(PY_LONG_LONG)) {
                __PYX_VERIFY_RETURN_INT_EXC(long, PY_LONG_LONG, PyLong_AsLongLong(x))
#endif
            }
        }
        {
#if CYTHON_COMPILING_IN_PYPY && !defined(_PyLong_AsByteArray)
            PyErr_SetString(PyExc_RuntimeError,
                            "_PyLong_AsByteArray() not available in PyPy, cannot convert large numbers");
#else
            long val;
            PyObject *v = __Pyx_PyNumber_IntOrLong(x);
 #if PY_MAJOR_VERSION < 3
            if (likely(v) && !PyLong_Check(v)) {
                PyObject *tmp = v;
                v = PyNumber_Long(tmp);
                Py_DECREF(tmp);
            }
 #endif
            if (likely(v)) {
                int one = 1; int is_little = (int)*(unsigned char *)&one;
                unsigned char *bytes = (unsigned char *)&val;
                int ret = _PyLong_AsByteArray((PyLongObject *)v,
                                              bytes, sizeof(val),
                                              is_little, !is_unsigned);
                Py_DECREF(v);
                if (likely(!ret))
                    return val;
            }
#endif
            return (long) -1;
        }
    } else {
        long val;
        PyObject *tmp = __Pyx_PyNumber_IntOrLong(x);
        if (!tmp) return (long) -1;
        val = __Pyx_PyInt_As_long(tmp);
        Py_DECREF(tmp);
        return val;
    }
raise_overflow:
    PyErr_SetString(PyExc_OverflowError,
        "value too large to convert to long");
    return (long) -1;
raise_neg_overflow:
    PyErr_SetString(PyExc_OverflowError,
        "can't convert negative value to long");
    return (long) -1;
}

/* CIntFromPy */
static CYTHON_INLINE int __Pyx_PyInt_As_int(PyObject *x) {
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wconversion"
#endif
    const int neg_one = (int) -1, const_zero = (int) 0;
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic pop
#endif
    const int is_unsigned = neg_one > const_zero;
#if PY_MAJOR_VERSION < 3
    if (likely(PyInt_Check(x))) {
        if (sizeof(int) < sizeof(long)) {
            __PYX_VERIFY_RETURN_INT(int, long, PyInt_AS_LONG(x))
        } else {
            long val = PyInt_AS_LONG(x);
            if (is_unsigned && unlikely(val < 0)) {
                goto raise_neg_overflow;
            }
            return (int) val;
        }
    } else
#endif
    if (likely(PyLong_Check(x))) {
        if (is_unsigned) {
#if CYTHON_USE_PYLONG_INTERNALS
            const digit* digits = ((PyLongObject*)x)->ob_digit;
            switch (Py_SIZE(x)) {
                case  0: return (int) 0;
                case  1: __PYX_VERIFY_RETURN_INT(int, digit, digits[0])
                case 2:
                    if (8 * sizeof(int) > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) >= 2 * PyLong_SHIFT) {
                            return (int) (((((int)digits[1]) << PyLong_SHIFT) | (int)digits[0]));
                        }
                    }
                    break;
                case 3:
                    if (8 * sizeof(int) > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) >= 3 * PyLong_SHIFT) {
                            return (int) (((((((int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0]));
                        }
                    }
                    break;
                case 4:
                    if (8 * sizeof(int) > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) >= 4 * PyLong_SHIFT) {
                            return (int) (((((((((int)digits[3]) << PyLong_SHIFT) | (int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0]));
                        }
                    }
                    break;
            }
#endif
#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX < 0x030C00A7
            if (unlikely(Py_SIZE(x) < 0)) {
                goto raise_neg_overflow;
            }
#else
            {
                int result = PyObject_RichCompareBool(x, Py_False, Py_LT);
                if (unlikely(result < 0))
                    return (int) -1;
                if (unlikely(result == 1))
                    goto raise_neg_overflow;
            }
#endif
            if (sizeof(int) <= sizeof(unsigned long)) {
                __PYX_VERIFY_RETURN_INT_EXC(int, unsigned long, PyLong_AsUnsignedLong(x))
#ifdef HAVE_LONG_LONG
            } else if (sizeof(int) <= sizeof(unsigned PY_LONG_LONG)) {
                __PYX_VERIFY_RETURN_INT_EXC(int, unsigned PY_LONG_LONG, PyLong_AsUnsignedLongLong(x))
#endif
            }
        } else {
#if CYTHON_USE_PYLONG_INTERNALS
            const digit* digits = ((PyLongObject*)x)->ob_digit;
            switch (Py_SIZE(x)) {
                case  0: return (int) 0;
                case -1: __PYX_VERIFY_RETURN_INT(int, sdigit, (sdigit) (-(sdigit)digits[0]))
                case  1: __PYX_VERIFY_RETURN_INT(int,  digit, +digits[0])
                case -2:
                    if (8 * sizeof(int) - 1 > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, long, -(long) (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) - 1 > 2 * PyLong_SHIFT) {
                            return (int) (((int)-1)*(((((int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
                case 2:
                    if (8 * sizeof(int) > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) - 1 > 2 * PyLong_SHIFT) {
                            return (int) ((((((int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
                case -3:
                    if (8 * sizeof(int) - 1 > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, long, -(long) (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) - 1 > 3 * PyLong_SHIFT) {
                            return (int) (((int)-1)*(((((((int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
                case 3:
                    if (8 * sizeof(int) > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) - 1 > 3 * PyLong_SHIFT) {
                            return (int) ((((((((int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
                case -4:
                    if (8 * sizeof(int) - 1 > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, long, -(long) (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) - 1 > 4 * PyLong_SHIFT) {
                            return (int) (((int)-1)*(((((((((int)digits[3]) << PyLong_SHIFT) | (int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
                case 4:
                    if (8 * sizeof(int) > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) - 1 > 4 * PyLong_SHIFT) {
                            return (int) ((((((((((int)digits[3]) << PyLong_SHIFT) | (int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
            }
#endif
            if (sizeof(int) <= sizeof(long)) {
                __PYX_VERIFY_RETURN_INT_EXC(int, long, PyLong_AsLong(x))
#ifdef HAVE_LONG_LONG
            } else if (sizeof(int) <= sizeof(PY_LONG_LONG)) {
                __PYX_VERIFY_RETURN_INT_EXC(int, PY_LONG_LONG, PyLong_AsLongLong(x))
#endif
            }
        }
        {
#if CYTHON_COMPILING_IN_PYPY && !defined(_PyLong_AsByteArray)
            PyErr_SetString(PyExc_RuntimeError,
                            "_PyLong_AsByteArray() not available in PyPy, cannot convert large numbers");
#else
            int val;
            PyObject *v = __Pyx_PyNumber_IntOrLong(x);
 #if PY_MAJOR_VERSION < 3
            if (likely(v) && !PyLong_Check(v)) {
                PyObject *tmp = v;
                v = PyNumber_Long(tmp);
                Py_DECREF(tmp);
            }
 #endif
            if (likely(v)) {
                int one = 1; int is_little = (int)*(unsigned char *)&one;
                unsigned char *bytes = (unsigned char *)&val;
                int ret = _PyLong_AsByteArray((PyLongObject *)v,
                                              bytes, sizeof(val),
                                              is_little, !is_unsigned);
                Py_DECREF(v);
                if (likely(!ret))
                    return val;
            }
#endif
            return (int) -1;
        }
    } else {
        int val;
        PyObject *tmp = __Pyx_PyNumber_IntOrLong(x);
        if (!tmp) return (int) -1;
        val = __Pyx_PyInt_As_int(tmp);
        Py_DECREF(tmp);
        return val;
    }
raise_overflow:
    PyErr_SetString(PyExc_OverflowError,
        "value too large to convert to int");
    return (int) -1;
raise_neg_overflow:
    PyErr_SetString(PyExc_OverflowError,
        "can't convert negative value to int");
    return (int) -1;
}

/* FastTypeChecks */
#if CYTHON_COMPILING_IN_CPYTHON
static int __Pyx_InBases(PyTypeObject *a, PyTypeObject *b) {
    while (a) {
        a = a->tp_base;
        if (a == b)
            return 1;
    }
    return b == &PyBaseObject_Type;
}
static CYTHON_INLINE int __Pyx_IsSubtype(PyTypeObject *a, PyTypeObject *b) {
    PyObject *mro;
    if (a == b) return 1;
    mro = a->tp_mro;
    if (likely(mro)) {
        Py_ssize_t i, n;
        n = PyTuple_GET_SIZE(mro);
        for (i = 0; i < n; i++) {
            if (PyTuple_GET_ITEM(mro, i) == (PyObject *)b)
                return 1;
        }
        return 0;
    }
    return __Pyx_InBases(a, b);
}
#if PY_MAJOR_VERSION == 2
static int __Pyx_inner_PyErr_GivenExceptionMatches2(PyObject *err, PyObject* exc_type1, PyObject* exc_type2) {
    PyObject *exception, *value, *tb;
    int res;
    __Pyx_PyThreadState_declare
    __Pyx_PyThreadState_assign
    __Pyx_ErrFetch(&exception, &value, &tb);
    res = exc_type1 ? PyObject_IsSubclass(err, exc_type1) : 0;
    if (unlikely(res == -1)) {
        PyErr_WriteUnraisable(err);
        res = 0;
    }
    if (!res) {
        res = PyObject_IsSubclass(err, exc_type2);
        if (unlikely(res == -1)) {
            PyErr_WriteUnraisable(err);
            res = 0;
        }
    }
    __Pyx_ErrRestore(exception, value, tb);
    return res;
}
#else
static CYTHON_INLINE int __Pyx_inner_PyErr_GivenExceptionMatches2(PyObject *err, PyObject* exc_type1, PyObject *exc_type2) {
    int res = exc_type1 ? __Pyx_IsSubtype((PyTypeObject*)err, (PyTypeObject*)exc_type1) : 0;
    if (!res) {
        res = __Pyx_IsSubtype((PyTypeObject*)err, (PyTypeObject*)exc_type2);
    }
    return res;
}
#endif
static int __Pyx_PyErr_GivenExceptionMatchesTuple(PyObject *exc_type, PyObject *tuple) {
    Py_ssize_t i, n;
    assert(PyExceptionClass_Check(exc_type));
    n = PyTuple_GET_SIZE(tuple);
#if PY_MAJOR_VERSION >= 3
    for (i=0; i<n; i++) {
        if (exc_type == PyTuple_GET_ITEM(tuple, i)) return 1;
    }
#endif
    for (i=0; i<n; i++) {
        PyObject *t = PyTuple_GET_ITEM(tuple, i);
        #if PY_MAJOR_VERSION < 3
        if (likely(exc_type == t)) return 1;
        #endif
        if (likely(PyExceptionClass_Check(t))) {
            if (__Pyx_inner_PyErr_GivenExceptionMatches2(exc_type, NULL, t)) return 1;
        } else {
        }
    }
    return 0;
}
static CYTHON_INLINE int __Pyx_PyErr_GivenExceptionMatches(PyObject *err, PyObject* exc_type) {
    if (likely(err == exc_type)) return 1;
    if (likely(PyExceptionClass_Check(err))) {
        if (likely(PyExceptionClass_Check(exc_type))) {
            return __Pyx_inner_PyErr_GivenExceptionMatches2(err, NULL, exc_type);
        } else if (likely(PyTuple_Check(exc_type))) {
            return __Pyx_PyErr_GivenExceptionMatchesTuple(err, exc_type);
        } else {
        }
    }
    return PyErr_GivenExceptionMatches(err, exc_type);
}
static CYTHON_INLINE int __Pyx_PyErr_GivenExceptionMatches2(PyObject *err, PyObject *exc_type1, PyObject *exc_type2) {
    assert(PyExceptionClass_Check(exc_type1));
    assert(PyExceptionClass_Check(exc_type2));
    if (likely(err == exc_type1 || err == exc_type2)) return 1;
    if (likely(PyExceptionClass_Check(err))) {
        return __Pyx_inner_PyErr_GivenExceptionMatches2(err, exc_type1, exc_type2);
    }
    return (PyErr_GivenExceptionMatches(err, exc_type1) || PyErr_GivenExceptionMatches(err, exc_type2));
}
#endif

/* CheckBinaryVersion */
static int __Pyx_check_binary_version(void) {
    char ctversion[5];
    int same=1, i, found_dot;
    const char* rt_from_call = Py_GetVersion();
    PyOS_snprintf(ctversion, 5, "%d.%d", PY_MAJOR_VERSION, PY_MINOR_VERSION);
    found_dot = 0;
    for (i = 0; i < 4; i++) {
        if (!ctversion[i]) {
            same = (rt_from_call[i] < '0' || rt_from_call[i] > '9');
            break;
        }
        if (rt_from_call[i] != ctversion[i]) {
            same = 0;
            break;
        }
    }
    if (!same) {
        char rtversion[5] = {'\0'};
        char message[200];
        for (i=0; i<4; ++i) {
            if (rt_from_call[i] == '.') {
                if (found_dot) break;
                found_dot = 1;
            } else if (rt_from_call[i] < '0' || rt_from_call[i] > '9') {
                break;
            }
            rtversion[i] = rt_from_call[i];
        }
        PyOS_snprintf(message, sizeof(message),
                      "compiletime version %s of module '%.100s' "
                      "does not match runtime version %s",
                      ctversion, __Pyx_MODULE_NAME, rtversion);
        return PyErr_WarnEx(NULL, message, 1);
    }
    return 0;
}

/* InitStrings */
static int __Pyx_InitStrings(__Pyx_StringTabEntry *t) {
    while (t->p) {
        #if PY_MAJOR_VERSION < 3
        if (t->is_unicode) {
            *t->p = PyUnicode_DecodeUTF8(t->s, t->n - 1, NULL);
        } else if (t->intern) {
            *t->p = PyString_InternFromString(t->s);
        } else {
            *t->p = PyString_FromStringAndSize(t->s, t->n - 1);
        }
        #else
        if (t->is_unicode | t->is_str) {
            if (t->intern) {
                *t->p = PyUnicode_InternFromString(t->s);
            } else if (t->encoding) {
                *t->p = PyUnicode_Decode(t->s, t->n - 1, t->encoding, NULL);
            } else {
                *t->p = PyUnicode_FromStringAndSize(t->s, t->n - 1);
            }
        } else {
            *t->p = PyBytes_FromStringAndSize(t->s, t->n - 1);
        }
        #endif
        if (!*t->p)
            return -1;
        if (PyObject_Hash(*t->p) == -1)
            return -1;
        ++t;
    }
    return 0;
}

static CYTHON_INLINE PyObject* __Pyx_PyUnicode_FromString(const char* c_str) {
    return __Pyx_PyUnicode_FromStringAndSize(c_str, (Py_ssize_t)strlen(c_str));
}
static CYTHON_INLINE const char* __Pyx_PyObject_AsString(PyObject* o) {
    Py_ssize_t ignore;
    return __Pyx_PyObject_AsStringAndSize(o, &ignore);
}
#if __PYX_DEFAULT_STRING_ENCODING_IS_ASCII || __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT
#if !CYTHON_PEP393_ENABLED
static const char* __Pyx_PyUnicode_AsStringAndSize(PyObject* o, Py_ssize_t *length) {
    char* defenc_c;
    PyObject* defenc = _PyUnicode_AsDefaultEncodedString(o, NULL);
    if (!defenc) return NULL;
    defenc_c = PyBytes_AS_STRING(defenc);
#if __PYX_DEFAULT_STRING_ENCODING_IS_ASCII
    {
        char* end = defenc_c + PyBytes_GET_SIZE(defenc);
        char* c;
        for (c = defenc_c; c < end; c++) {
            if ((unsigned char) (*c) >= 128) {
                PyUnicode_AsASCIIString(o);
                return NULL;
            }
        }
    }
#endif
    *length = PyBytes_GET_SIZE(defenc);
    return defenc_c;
}
#else
static CYTHON_INLINE const char* __Pyx_PyUnicode_AsStringAndSize(PyObject* o, Py_ssize_t *length) {
    if (unlikely(__Pyx_PyUnicode_READY(o) == -1)) return NULL;
#if __PYX_DEFAULT_STRING_ENCODING_IS_ASCII
    if (likely(PyUnicode_IS_ASCII(o))) {
        *length = PyUnicode_GET_LENGTH(o);
        return PyUnicode_AsUTF8(o);
    } else {
        PyUnicode_AsASCIIString(o);
        return NULL;
    }
#else
    return PyUnicode_AsUTF8AndSize(o, length);
#endif
}
#endif
#endif
static CYTHON_INLINE const char* __Pyx_PyObject_AsStringAndSize(PyObject* o, Py_ssize_t *length) {
#if __PYX_DEFAULT_STRING_ENCODING_IS_ASCII || __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT
    if (
#if PY_MAJOR_VERSION < 3 && __PYX_DEFAULT_STRING_ENCODING_IS_ASCII
            __Pyx_sys_getdefaultencoding_not_ascii &&
#endif
            PyUnicode_Check(o)) {
        return __Pyx_PyUnicode_AsStringAndSize(o, length);
    } else
#endif
#if (!CYTHON_COMPILING_IN_PYPY) || (defined(PyByteArray_AS_STRING) && defined(PyByteArray_GET_SIZE))
    if (PyByteArray_Check(o)) {
        *length = PyByteArray_GET_SIZE(o);
        return PyByteArray_AS_STRING(o);
    } else
#endif
    {
        char* result;
        int r = PyBytes_AsStringAndSize(o, &result, length);
        if (unlikely(r < 0)) {
            return NULL;
        } else {
            return result;
        }
    }
}
static CYTHON_INLINE int __Pyx_PyObject_IsTrue(PyObject* x) {
   int is_true = x == Py_True;
   if (is_true | (x == Py_False) | (x == Py_None)) return is_true;
   else return PyObject_IsTrue(x);
}
static CYTHON_INLINE int __Pyx_PyObject_IsTrueAndDecref(PyObject* x) {
    int retval;
    if (unlikely(!x)) return -1;
    retval = __Pyx_PyObject_IsTrue(x);
    Py_DECREF(x);
    return retval;
}
static PyObject* __Pyx_PyNumber_IntOrLongWrongResultType(PyObject* result, const char* type_name) {
#if PY_MAJOR_VERSION >= 3
    if (PyLong_Check(result)) {
        if (PyErr_WarnFormat(PyExc_DeprecationWarning, 1,
                "__int__ returned non-int (type %.200s).  "
                "The ability to return an instance of a strict subclass of int "
                "is deprecated, and may be removed in a future version of Python.",
                Py_TYPE(result)->tp_name)) {
            Py_DECREF(result);
            return NULL;
        }
        return result;
    }
#endif
    PyErr_Format(PyExc_TypeError,
                 "__%.4s__ returned non-%.4s (type %.200s)",
                 type_name, type_name, Py_TYPE(result)->tp_name);
    Py_DECREF(result);
    return NULL;
}
static CYTHON_INLINE PyObject* __Pyx_PyNumber_IntOrLong(PyObject* x) {
#if CYTHON_USE_TYPE_SLOTS
  PyNumberMethods *m;
#endif
  const char *name = NULL;
  PyObject *res = NULL;
#if PY_MAJOR_VERSION < 3
  if (likely(PyInt_Check(x) || PyLong_Check(x)))
#else
  if (likely(PyLong_Check(x)))
#endif
    return __Pyx_NewRef(x);
#if CYTHON_USE_TYPE_SLOTS
  m = Py_TYPE(x)->tp_as_number;
  #if PY_MAJOR_VERSION < 3
  if (m && m->nb_int) {
    name = "int";
    res = m->nb_int(x);
  }
  else if (m && m->nb_long) {
    name = "long";
    res = m->nb_long(x);
  }
  #else
  if (likely(m && m->nb_int)) {
    name = "int";
    res = m->nb_int(x);
  }
  #endif
#else
  if (!PyBytes_CheckExact(x) && !PyUnicode_CheckExact(x)) {
    res = PyNumber_Int(x);
  }
#endif
  if (likely(res)) {
#if PY_MAJOR_VERSION < 3
    if (unlikely(!PyInt_Check(res) && !PyLong_Check(res))) {
#else
    if (unlikely(!PyLong_CheckExact(res))) {
#endif
        return __Pyx_PyNumber_IntOrLongWrongResultType(res, name);
    }
  }
  else if (!PyErr_Occurred()) {
    PyErr_SetString(PyExc_TypeError,
                    "an integer is required");
  }
  return res;
}
static CYTHON_INLINE Py_ssize_t __Pyx_PyIndex_AsSsize_t(PyObject* b) {
  Py_ssize_t ival;
  PyObject *x;
#if PY_MAJOR_VERSION < 3
  if (likely(PyInt_CheckExact(b))) {
    if (sizeof(Py_ssize_t) >= sizeof(long))
        return PyInt_AS_LONG(b);
    else
        return PyInt_AsSsize_t(b);
  }
#endif
  if (likely(PyLong_CheckExact(b))) {
    #if CYTHON_USE_PYLONG_INTERNALS
    const digit* digits = ((PyLongObject*)b)->ob_digit;
    const Py_ssize_t size = Py_SIZE(b);
    if (likely(__Pyx_sst_abs(size) <= 1)) {
        ival = likely(size) ? digits[0] : 0;
        if (size == -1) ival = -ival;
        return ival;
    } else {
      switch (size) {
         case 2:
           if (8 * sizeof(Py_ssize_t) > 2 * PyLong_SHIFT) {
             return (Py_ssize_t) (((((size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
         case -2:
           if (8 * sizeof(Py_ssize_t) > 2 * PyLong_SHIFT) {
             return -(Py_ssize_t) (((((size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
         case 3:
           if (8 * sizeof(Py_ssize_t) > 3 * PyLong_SHIFT) {
             return (Py_ssize_t) (((((((size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
         case -3:
           if (8 * sizeof(Py_ssize_t) > 3 * PyLong_SHIFT) {
             return -(Py_ssize_t) (((((((size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
         case 4:
           if (8 * sizeof(Py_ssize_t) > 4 * PyLong_SHIFT) {
             return (Py_ssize_t) (((((((((size_t)digits[3]) << PyLong_SHIFT) | (size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
         case -4:
           if (8 * sizeof(Py_ssize_t) > 4 * PyLong_SHIFT) {
             return -(Py_ssize_t) (((((((((size_t)digits[3]) << PyLong_SHIFT) | (size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
      }
    }
    #endif
    return PyLong_AsSsize_t(b);
  }
  x = PyNumber_Index(b);
  if (!x) return -1;
  ival = PyInt_AsSsize_t(x);
  Py_DECREF(x);
  return ival;
}
static CYTHON_INLINE Py_hash_t __Pyx_PyIndex_AsHash_t(PyObject* o) {
  if (sizeof(Py_hash_t) == sizeof(Py_ssize_t)) {
    return (Py_hash_t) __Pyx_PyIndex_AsSsize_t(o);
#if PY_MAJOR_VERSION < 3
  } else if (likely(PyInt_CheckExact(o))) {
    return PyInt_AS_LONG(o);
#endif
  } else {
    Py_ssize_t ival;
    PyObject *x;
    x = PyNumber_Index(o);
    if (!x) return -1;
    ival = PyInt_AsLong(x);
    Py_DECREF(x);
    return ival;
  }
}
static CYTHON_INLINE PyObject * __Pyx_PyBool_FromLong(long b) {
  return b ? __Pyx_NewRef(Py_True) : __Pyx_NewRef(Py_False);
}
static CYTHON_INLINE PyObject * __Pyx_PyInt_FromSize_t(size_t ival) {
    return PyInt_FromSize_t(ival);
}


#endif /* Py_PYTHON_H */
