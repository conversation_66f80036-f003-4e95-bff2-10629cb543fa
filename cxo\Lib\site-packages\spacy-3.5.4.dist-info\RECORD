../../Scripts/spacy.exe,sha256=rnG8RSKsTMNSWDoBfsXKvxnUDcwozZ6FCnXiTuypnj4,108417
spacy-3.5.4.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
spacy-3.5.4.dist-info/LICENSE,sha256=vaxnpYvefYdU6LYBq4b1QhEXFsxilwVbOwTq6cPmZsY,1149
spacy-3.5.4.dist-info/METADATA,sha256=kCKPikMAZnMScsaD3110xefHmkWtB4ktfMaAQQRgWdo,25529
spacy-3.5.4.dist-info/RECORD,,
spacy-3.5.4.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy-3.5.4.dist-info/WHEEL,sha256=9wvhO-5NhjjD8YmmxAvXTPQXMDOZ50W5vklzeoqFtkM,102
spacy-3.5.4.dist-info/entry_points.txt,sha256=q6IIfdWIqV5G_nzLChejIJpbzXNEg4iqHL05cTBO888,46
spacy-3.5.4.dist-info/top_level.txt,sha256=oXTJOtzJdUdzkIbih8SAC-lK9JfD0jvZ_43H5fmTCHw,6
spacy/__init__.pxd,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/__init__.py,sha256=FtVtZkY42ATkucVFJnKKTHkmerlrKl-H54IxgVrCOYQ,3036
spacy/__main__.py,sha256=ipw4uuFXJo2_pifxy6Vg6Jyxzks2XD3mppBZyzGbsrE,84
spacy/__pycache__/__init__.cpython-311.pyc,,
spacy/__pycache__/__main__.cpython-311.pyc,,
spacy/__pycache__/about.cpython-311.pyc,,
spacy/__pycache__/compat.cpython-311.pyc,,
spacy/__pycache__/errors.cpython-311.pyc,,
spacy/__pycache__/git_info.cpython-311.pyc,,
spacy/__pycache__/glossary.cpython-311.pyc,,
spacy/__pycache__/language.cpython-311.pyc,,
spacy/__pycache__/lookups.cpython-311.pyc,,
spacy/__pycache__/pipe_analysis.cpython-311.pyc,,
spacy/__pycache__/schemas.cpython-311.pyc,,
spacy/__pycache__/scorer.cpython-311.pyc,,
spacy/__pycache__/ty.cpython-311.pyc,,
spacy/__pycache__/util.cpython-311.pyc,,
spacy/about.py,sha256=AdAATFv5g5hiQDE6L-Q29E-w9MAzL2dFNj4noVStAug,328
spacy/attrs.cp311-win_amd64.pyd,sha256=UblaiFl2py-ASrb0eAjnJNAN7DRvxZ50X17_zBG1cyI,66048
spacy/attrs.cpp,sha256=CWgjVm-v1qkU8noiqo3GZuZrNeaXbn7rgWFhg0jD2EA,344204
spacy/attrs.pxd,sha256=I_G-KVuLvqDQfliFxvR7a0BlIFlQ2Rd8BP1fGT7qPjk,1252
spacy/attrs.pyx,sha256=Wa3Q_9RSjxzYXEBPJlSP3SdjBfpdk_kRmQBIbAZ2qFc,5959
spacy/cli/__init__.py,sha256=WsNB1Kp5ktXt_PId5QlDuHPhLyIGnxp4kIIh-SzFEjc,2030
spacy/cli/__pycache__/__init__.cpython-311.pyc,,
spacy/cli/__pycache__/_util.cpython-311.pyc,,
spacy/cli/__pycache__/apply.cpython-311.pyc,,
spacy/cli/__pycache__/assemble.cpython-311.pyc,,
spacy/cli/__pycache__/benchmark_speed.cpython-311.pyc,,
spacy/cli/__pycache__/convert.cpython-311.pyc,,
spacy/cli/__pycache__/debug_config.cpython-311.pyc,,
spacy/cli/__pycache__/debug_data.cpython-311.pyc,,
spacy/cli/__pycache__/debug_diff.cpython-311.pyc,,
spacy/cli/__pycache__/debug_model.cpython-311.pyc,,
spacy/cli/__pycache__/download.cpython-311.pyc,,
spacy/cli/__pycache__/evaluate.cpython-311.pyc,,
spacy/cli/__pycache__/find_threshold.cpython-311.pyc,,
spacy/cli/__pycache__/info.cpython-311.pyc,,
spacy/cli/__pycache__/init_config.cpython-311.pyc,,
spacy/cli/__pycache__/init_pipeline.cpython-311.pyc,,
spacy/cli/__pycache__/package.cpython-311.pyc,,
spacy/cli/__pycache__/pretrain.cpython-311.pyc,,
spacy/cli/__pycache__/profile.cpython-311.pyc,,
spacy/cli/__pycache__/train.cpython-311.pyc,,
spacy/cli/__pycache__/validate.cpython-311.pyc,,
spacy/cli/_util.py,sha256=7vdHnDoieofUx3qxZ4ddYS9VHiXXc9czBiW3KoQZ1iM,24962
spacy/cli/apply.py,sha256=MU-LsE5vqku51bCj8WmyWL-St60QNTarGDsDW38k8n4,4988
spacy/cli/assemble.py,sha256=llvLdX7JzU344Q7YNMpw3ji6IsVQOTPqBh1Y1R3ZzWA,2631
spacy/cli/benchmark_speed.py,sha256=aM6YqhjYn6r8368GoG-2ZwzXqqY_tX9j2L-3qARhCtU,5391
spacy/cli/convert.py,sha256=DKYl_k_QT-_FZn2CQMKKMvah_PcCMlVs6GVDtuNQCJo,9651
spacy/cli/debug_config.py,sha256=PnpNHGTJmsYeTvkr7057Y-GSWEpx2OolAkcxkST8a6I,4652
spacy/cli/debug_data.py,sha256=h5TwjA4fUKNb87bSR2QFr3sVyNhDkW86kwhIy5O3Y2g,51107
spacy/cli/debug_diff.py,sha256=CnTfkAUa9BUxvJo6rrc5oaWutbhyBzOf3o_KHzAvx5E,3669
spacy/cli/debug_model.py,sha256=5tvIS_O6zv0fL8BIkis9vKx7tFVbRXFPEpQRTIvFOG4,9138
spacy/cli/download.py,sha256=uTAz7-je9rVaKjq_6NyobuLWm92vvchQ6xfAkroDUK0,4796
spacy/cli/evaluate.py,sha256=D9y9G6q_utPMElNmvxjzJq_JuuGILC9LpJ42Ya3LQOY,8796
spacy/cli/find_threshold.py,sha256=C7jumSt0d31c2KS2y7m1mAz7riHLVhZs0Gw8vvI5Pdg,9613
spacy/cli/info.py,sha256=HDpixd-x8HyvQcup--wC6oiBTGUMmP7FmOK6uTyfOM8,6533
spacy/cli/init_config.py,sha256=Yj2_7hFUF8hiF9xeT5Gu9QePNPNGlgZ3gVj78Ck1yYw,10622
spacy/cli/init_pipeline.py,sha256=PJtsa-QgcoB_e52RpYARsjHW8LxWK1YJQUA69aPfPq4,5773
spacy/cli/package.py,sha256=Qb7raI_SUgt8FWm5x-6jAxzYVobVbosucPm6QwuEUeE,21941
spacy/cli/pretrain.py,sha256=rETttttmblGXlpZ-T_iPJ9ZNCLLTrCaPYNeT381q040,5377
spacy/cli/profile.py,sha256=lo15dMNP-jz7JnslI4RWPjE8BDiOyrjLNvAPRZJ7IC0,3543
spacy/cli/project/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/cli/project/__pycache__/__init__.cpython-311.pyc,,
spacy/cli/project/__pycache__/assets.cpython-311.pyc,,
spacy/cli/project/__pycache__/clone.cpython-311.pyc,,
spacy/cli/project/__pycache__/document.cpython-311.pyc,,
spacy/cli/project/__pycache__/dvc.cpython-311.pyc,,
spacy/cli/project/__pycache__/pull.cpython-311.pyc,,
spacy/cli/project/__pycache__/push.cpython-311.pyc,,
spacy/cli/project/__pycache__/remote_storage.cpython-311.pyc,,
spacy/cli/project/__pycache__/run.cpython-311.pyc,,
spacy/cli/project/assets.py,sha256=fJag9J4JkOehT-Qt3MPxvCraHs5u5IMgwkERgFabT9Y,8414
spacy/cli/project/clone.py,sha256=V1kCitFJJsnmRUCdFEya_CBZt8iYksuaIRwaYKspANg,4869
spacy/cli/project/document.py,sha256=WdF-qug6mS6XZlS0pD2nx2pHrMkNBxQ4prVJ_HsXQRc,5253
spacy/cli/project/dvc.py,sha256=Gtl-4HWkKzJC0eVETkg_6Zgcr3MzM1qvq215urUXfMU,8750
spacy/cli/project/pull.py,sha256=WAWVyourxv0JNn49i491_UX9JvS66iCQWKYNK12uBmg,3009
spacy/cli/project/push.py,sha256=0xTOuWid5y0B3EcEqYLAWXxTzlJ1Q9ZPx45rixL9mLc,2843
spacy/cli/project/remote_storage.py,sha256=0pTTza4sn-pAljQM0rlnUFuykA8i4T08rX5ngivCDqU,8441
spacy/cli/project/run.py,sha256=nDMKApRxTo9t_gqZJOfmJBZw34OYsmaOZFmNeR0y9kM,16198
spacy/cli/templates/quickstart_training.jinja,sha256=-D3gcNqFvrlwi9s5t3Q3FsI2e-A0GE0uWF9E9PIBKLM,15708
spacy/cli/templates/quickstart_training_recommendations.yml,sha256=7lIrbsm8HSCbUNe4IfoodVLiah61V9166o2VZySisZA,6576
spacy/cli/train.py,sha256=gia-WDL3AIXWnYiCFcc08pAkjEraaVr9-nu_HC7AHTU,3474
spacy/cli/validate.py,sha256=Eged_fTYHAWRclnnp4OFs684G2UKAStQU4VCMhckSDs,4691
spacy/compat.py,sha256=qiU1x-e5s1-dzc6mhT0eoE-9Esa5kOpR4RLwgpg5OME,1394
spacy/default_config.cfg,sha256=Onjrjndgne1F3OkPROozGHYrqT2cG2nR0Pwk1DKd3QE,4487
spacy/default_config_pretraining.cfg,sha256=h41wUgQkX2KmUW_2Y09DnxeOb7Nc5v-ed_tPIYfiR1Q,782
spacy/displacy/__init__.py,sha256=mRABf840zFT86Ucm_hH3NHIlna8lSDumwHxO4fHryuQ,10635
spacy/displacy/__pycache__/__init__.cpython-311.pyc,,
spacy/displacy/__pycache__/render.cpython-311.pyc,,
spacy/displacy/__pycache__/templates.cpython-311.pyc,,
spacy/displacy/render.py,sha256=SurLNPe1tbCMBjAVA-Zf1wy69wZ77rJo4vyQ75g9df0,24387
spacy/displacy/templates.py,sha256=BNuAWXxAdocc5qa_AdeRsioffQ9ZcWXlAk6eWQOrrNY,4785
spacy/errors.py,sha256=KxrUe3yJT5bFn0hT3Nozte2rTZ_nnsMdoGjA4I-G3Lw,67898
spacy/git_info.py,sha256=DM43g58qbbXuKGAWIiVito6g-D3YhmTMXv98avzDDvA,76
spacy/glossary.py,sha256=PwFWkDfRK3QsiwRlWZl0OuhHyz3C-6j-EVX3dIVwOxM,13544
spacy/kb/__init__.py,sha256=8VvyYaSdBIOYw05UxLQboUuNbupb2pO4EskhUVQijWc,147
spacy/kb/__pycache__/__init__.cpython-311.pyc,,
spacy/kb/candidate.cp311-win_amd64.pyd,sha256=HeiiHxp-R_GAYEFfFjV5inGi7KbOK1kNHRjf8b93jy0,61952
spacy/kb/candidate.cpp,sha256=25jT3Mi5dFoXV5rHCnBDUB47TDbMKxxwOr6--bYHV5A,433488
spacy/kb/candidate.pxd,sha256=xoQs_Vsb0WvDsDmH2myEnpV-MOM0nSIrDyjH0lVhmKM,402
spacy/kb/candidate.pyx,sha256=UvjzWw4lmdCWzYz7nzREHWlC4gq8vqDui5TFV4oRFZU,2680
spacy/kb/kb.cp311-win_amd64.pyd,sha256=PUhwXc6WanO619r1rLz0aCkU9yHqN5pz1T8fitqoOMQ,65024
spacy/kb/kb.cpp,sha256=Xt5-AdUvOU97v6o6Y_F7tUIrlRwNdVlSS5k6FULmKbc,442616
spacy/kb/kb.pxd,sha256=qSNd0sUEnDMYFM6QI0w-AxYvYwrB-a__lpzDCu1rIcY,275
spacy/kb/kb.pyx,sha256=Wp91Z9WhUNP4RmZ-v_Guc82l40rd1_76uoElBY3reuM,4568
spacy/kb/kb_in_memory.cp311-win_amd64.pyd,sha256=imdArt3v4orcxBGlCwMguVaa3udJISphY1qmC44zgmw,247808
spacy/kb/kb_in_memory.cpp,sha256=U1PT0MPs-8_D1BHXyQfy701L7RIxW1xZCVrN9Z0h-_E,1248728
spacy/kb/kb_in_memory.pxd,sha256=zhZ-wl8j1Tf27D4lvnKJ-ptui8Xcr__-ZVL5V9gmAOA,6951
spacy/kb/kb_in_memory.pyx,sha256=sMWejLMd_oPZueG6_53acygLuP0oPb66QyQjvJv_xSw,27153
spacy/lang/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/lang/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/__pycache__/char_classes.cpython-311.pyc,,
spacy/lang/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/__pycache__/norm_exceptions.cpython-311.pyc,,
spacy/lang/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/af/__init__.py,sha256=pWerKdYA5PwlW3rbkKoV7RMvRP12tVrGPPwVUomy120,269
spacy/lang/af/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/af/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/af/stop_words.py,sha256=H_sR5txOztzBQH4p4xVnNxx0RkWhPlPPqvpYTl9gQJk,348
spacy/lang/am/__init__.py,sha256=jmKV0KOAM63HF5EmYE2RcESUPqIUHP9U-sUE4YDutLA,858
spacy/lang/am/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/am/__pycache__/examples.cpython-311.pyc,,
spacy/lang/am/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/am/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/am/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/am/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/am/examples.py,sha256=3bWKezTGh9ba49D7cg6VnBmYy54_iHjJjCDmXmRe4wM,810
spacy/lang/am/lex_attrs.py,sha256=j-SPYezEw3IEghMy9a4FDNV9HLe2b5_NjlGBVkeJMcc,2294
spacy/lang/am/punctuation.py,sha256=am--aGSQ3O8O0H2UkxK1yS5TFbGeUWq6mHSwyKAaonY,563
spacy/lang/am/stop_words.py,sha256=0JnkVNCLWSJyHCZTOE0fNY4_qMNaYN8myRoDIvJ-BVs,3235
spacy/lang/am/tokenizer_exceptions.py,sha256=bHburcdap_zADUepksi4_H7PesRHx-SPELF2lk1CoO4,312
spacy/lang/ar/__init__.py,sha256=rAOHno2XdG6fO4wzXxlgDpYpt7fs5P3O91u6ELE895g,593
spacy/lang/ar/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/ar/__pycache__/examples.cpython-311.pyc,,
spacy/lang/ar/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/ar/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/ar/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/ar/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/ar/examples.py,sha256=p0i1W-pzSKCODHF9vBKvvSYGZxX5FIqyypzAWQaMKo4,907
spacy/lang/ar/lex_attrs.py,sha256=OPmYFydoME6Fe_1bNPI1t-HpzTxI2tvBVFlI48GMYiM,1406
spacy/lang/ar/punctuation.py,sha256=t0xdPsTPj55IQT547cRCqevYUkw5KXaZTO0w4MvJ_aI,477
spacy/lang/ar/stop_words.py,sha256=3Qw5pKVhtDGQ3FlW0rRxm7vjfIbf60C6L6hbuzkfUS4,3570
spacy/lang/ar/tokenizer_exceptions.py,sha256=0HVOSDw0PDcI3pAiz0lVKe1ZNH-MK7QGKVkUcm9U9Sc,1550
spacy/lang/az/__init__.py,sha256=MpS8JZKHtrcIyvO30Dh8DWCft9eI32RyvPGB3C2xCAE,345
spacy/lang/az/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/az/__pycache__/examples.cpython-311.pyc,,
spacy/lang/az/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/az/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/az/examples.py,sha256=I0NZ0a650i8kTuBw8p2QhOANSCiqc0nLZRnrk5SA2u0,765
spacy/lang/az/lex_attrs.py,sha256=OfZQLlybhr3c7TZd1ZNz6EyD6WC_E7l2OkDxKijuIzE,1786
spacy/lang/az/stop_words.py,sha256=E10S_YshQa8QXgojMsih4sAqr41eWgplm9bEahTjC-A,1111
spacy/lang/bg/__init__.py,sha256=y1YrRdfdQlw3TGIsG3KHqiKaSY_2gPl4DrcTSxA7AT4,948
spacy/lang/bg/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/bg/__pycache__/examples.cpython-311.pyc,,
spacy/lang/bg/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/bg/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/bg/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/bg/examples.py,sha256=36pksYTnijoWBOIDmNLbAVcDFa4zYO9zdtomkiuck_0,649
spacy/lang/bg/lex_attrs.py,sha256=faMCG5RITlOrv_BUldDrZZoCbPz03yIjiDrzkHaOX0w,2136
spacy/lang/bg/stop_words.py,sha256=tMIP6vEtXPXFRyyYK9RE2swcxL-bJj6UFlrFoM85hdw,4827
spacy/lang/bg/tokenizer_exceptions.py,sha256=aXwnDi6u8C8BjT4ZMY1OsayAMkYw9DDDLSqPcLvb-Hs,9323
spacy/lang/bn/__init__.py,sha256=hVwHd_jlUMqYJJp-AoOpM_X-mzE-_xkyNip202STXMw,1222
spacy/lang/bn/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/bn/__pycache__/examples.cpython-311.pyc,,
spacy/lang/bn/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/bn/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/bn/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/bn/examples.py,sha256=hchWoBKFkWGkML4223ZV3UKseO7HplAxynAIYKQ0nDA,314
spacy/lang/bn/punctuation.py,sha256=YyT-_RwPXz1GsysAfql6zhryEuDvvuPzaoUIMONX3Zc,1315
spacy/lang/bn/stop_words.py,sha256=HzcPcAKwdOcTpTzYJNe1UW5GMv3it59gd8NmYpzmGfc,6198
spacy/lang/bn/tokenizer_exceptions.py,sha256=BIuqj8wiQTENJCB4-gSAnkOX_dQ-Mg12cOY6Qkb8wKQ,997
spacy/lang/ca/__init__.py,sha256=N-RmAxI6ZSy0Yd8ZJ_BXfqSTCr25hPVSZrYSayEOAdg,1397
spacy/lang/ca/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/ca/__pycache__/examples.cpython-311.pyc,,
spacy/lang/ca/__pycache__/lemmatizer.cpython-311.pyc,,
spacy/lang/ca/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/ca/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/ca/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/ca/__pycache__/syntax_iterators.cpython-311.pyc,,
spacy/lang/ca/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/ca/examples.py,sha256=uF5Z4DbU1gf8MauITplga1ytbbhoGeZ3IX19kSFUOo0,613
spacy/lang/ca/lemmatizer.py,sha256=zuzsrKUYT3cQSM21dafO2qFP7ANuUOh3bGJMmyWA1Ic,2924
spacy/lang/ca/lex_attrs.py,sha256=VqjVpSc9lgzEiEN4xLDzPM6pkzkn9MBqGVwenFNwVgI,1015
spacy/lang/ca/punctuation.py,sha256=7x5Bn5fPzWGuvzuGdtDed-J5zI6hifkNy9m2ejK0IPM,1707
spacy/lang/ca/stop_words.py,sha256=-m8za0h81UbOw4i0UG857zjVO_9nQASaxk1gOg71U5Q,1671
spacy/lang/ca/syntax_iterators.py,sha256=i942WNVnH2HOPW_iZXrI9mQy8vdwb3YV_lJpOvQsfUk,2042
spacy/lang/ca/tokenizer_exceptions.py,sha256=_VI32f7dploTdh20QTc3oVOk7fGtuAx2-Wl-qTUXzBs,2030
spacy/lang/char_classes.py,sha256=dYGU78hbt9dyRhfS96h2TARG9rFS3ct1DfcmRF5p_Bg,15011
spacy/lang/cs/__init__.py,sha256=LVOBOl-_ILhPAGMcRz1NLNJSH7EtiboNGAujFA0w6uE,321
spacy/lang/cs/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/cs/__pycache__/examples.cpython-311.pyc,,
spacy/lang/cs/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/cs/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/cs/examples.py,sha256=kCgV00XVc4Lp0yCvl89DwSH00xOud01HCMgI-Dzv_p4,1552
spacy/lang/cs/lex_attrs.py,sha256=a-C2DCEroB1LyKmC83SmYYbNlDbJ4Sk3kxjqEKMs1nc,1135
spacy/lang/cs/stop_words.py,sha256=Oa--mrk0zKN_MWUV2E-PWMsf483x-SGeMXpgDvzNX7U,2628
spacy/lang/da/__init__.py,sha256=R-oJB8bgYMDODx7zO8VtgPOVVNBAqyUNvFMFthP-fks,651
spacy/lang/da/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/da/__pycache__/examples.cpython-311.pyc,,
spacy/lang/da/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/da/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/da/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/da/__pycache__/syntax_iterators.cpython-311.pyc,,
spacy/lang/da/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/da/examples.py,sha256=FLPTyVtK9ux7ewZ75uodGgDT3Sjzjv8z7a_I6xUe5iI,585
spacy/lang/da/lex_attrs.py,sha256=W9pPkn9LTKNeMKuqUokynvfchErRSeZLz0-gIWKpQQQ,3626
spacy/lang/da/punctuation.py,sha256=ACkVysZ9xxlfjLcsojef80PiPYrILK8IqdCpa7Kw_Tk,943
spacy/lang/da/stop_words.py,sha256=xvlGGoRV5LtAP8upcVEhXZ4DIRaGdBuoMPCDf3NQqaU,1390
spacy/lang/da/syntax_iterators.py,sha256=TqXE-R6cO11qtU4MOA9eZM1oOXNeWC4MN4uCDhKBRkM,2261
spacy/lang/da/tokenizer_exceptions.py,sha256=EIBMqPWl9ISV-f6_bDifdvho3_g_o7wGbSSu_OVYGDo,9537
spacy/lang/de/__init__.py,sha256=6bGyzcn18Fmv6PundLYJhdoDKVkrmdJxMx0y_LNkUO0,638
spacy/lang/de/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/de/__pycache__/examples.cpython-311.pyc,,
spacy/lang/de/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/de/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/de/__pycache__/syntax_iterators.cpython-311.pyc,,
spacy/lang/de/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/de/examples.py,sha256=rdNiIKOByHM4RiHOzoi7oq0rx3843UTQzG9MEcM6C7M,697
spacy/lang/de/punctuation.py,sha256=WMitssJb3s0frsHS9FA3e5YzUt_-HP_EIowdQdVNCwI,1460
spacy/lang/de/stop_words.py,sha256=25baHVaTDhAAlPU20EnPJ74MafkROAzXnRwI5MczoZw,3739
spacy/lang/de/syntax_iterators.py,sha256=anMU6SsW1e7O5mEndAJ2lMi9Wn3bh5FSfbhCTbM3hHY,1893
spacy/lang/de/tokenizer_exceptions.py,sha256=wjQWX3mutEKEOGsNcYoS1A-OBzKn4lFt_mhxod_BLy8,6124
spacy/lang/dsb/__init__.py,sha256=9oxxbnRTODrkvWmwSovnmNzIz1vlwoyXCFc4cXdequA,350
spacy/lang/dsb/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/dsb/__pycache__/examples.cpython-311.pyc,,
spacy/lang/dsb/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/dsb/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/dsb/examples.py,sha256=3P1lklRPW1ei6vlRSizXW0oGHK6iAHuDM0-hlxwKBZQ,568
spacy/lang/dsb/lex_attrs.py,sha256=jVBjesvzatTtye-2eDDLh2OeVD3lmfuvGfgpIt88hH8,2019
spacy/lang/dsb/stop_words.py,sha256=DJ6QDzYe6eN0Q4gSTTDG7T4gEfeR_R86_RoAk-98Bsc,133
spacy/lang/el/__init__.py,sha256=va8JHdmo1obOU6evhyIlyHTFcQGV3KEz-R2BsqRI3_Y,1381
spacy/lang/el/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/el/__pycache__/examples.cpython-311.pyc,,
spacy/lang/el/__pycache__/get_pos_from_wiktionary.cpython-311.pyc,,
spacy/lang/el/__pycache__/lemmatizer.cpython-311.pyc,,
spacy/lang/el/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/el/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/el/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/el/__pycache__/syntax_iterators.cpython-311.pyc,,
spacy/lang/el/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/el/examples.py,sha256=_cQbPtUwB-TTQNS0A6BsfRuQ39mLP7fw9uOtDczKTxM,1996
spacy/lang/el/get_pos_from_wiktionary.py,sha256=X3xF5rlLb7OhSLOuRX957eR9lRqpggOVHVVBzzuuGDo,2149
spacy/lang/el/lemmatizer.py,sha256=UJpdYmXFb0SXkSyEYSJfGKLXoUFg1cJ1ZI5fBCz3900,2257
spacy/lang/el/lex_attrs.py,sha256=kGn4bzLt8eC5Mv3xJ7t-gaZHjCdj495ypfWYxi2xQV8,2419
spacy/lang/el/punctuation.py,sha256=hK-fTjsnDHL6-Gvrkbiq9yZfDS8Ub9xtQermJjRPVCQ,3506
spacy/lang/el/stop_words.py,sha256=wCpgnP35DX2qw8VAP-oJGgNSg49jEusVCC8JFpxcgEI,8187
spacy/lang/el/syntax_iterators.py,sha256=fGUK0oT2WmvGldwJChRt_LvKPY44HTbADo6lfeGUvag,2343
spacy/lang/el/tokenizer_exceptions.py,sha256=F1QxmLFwZHQv881xEXmz003HIs5G4E5kwtHo4vrMv3w,10471
spacy/lang/en/__init__.py,sha256=NTwAl6t8uG4PS8gCxLfV0JVBZgjDsxnPq9M1FpwjL0M,1285
spacy/lang/en/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/en/__pycache__/examples.cpython-311.pyc,,
spacy/lang/en/__pycache__/lemmatizer.cpython-311.pyc,,
spacy/lang/en/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/en/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/en/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/en/__pycache__/syntax_iterators.cpython-311.pyc,,
spacy/lang/en/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/en/examples.py,sha256=paT-ZtCInFSLtMBzDC1O7s0K4thsQc7tmkXqQHQk-00,574
spacy/lang/en/lemmatizer.py,sha256=xQ-Tod3RI1sbS7xd7cWkEnHya9E47hNZ0BlPWGbgxZQ,1520
spacy/lang/en/lex_attrs.py,sha256=TKoj1bzh9R0hUQUUsQgjOyfoVAoxvvaL82AZBjKLxwc,1630
spacy/lang/en/punctuation.py,sha256=pMLgYGwZRLpzgc_4XcFtsgRzNl3kYSjBblMTpqp7zj4,588
spacy/lang/en/stop_words.py,sha256=t3w3m4xfRGBB4BFpppgGwtmjl523U0LV87w4S-QQvSw,2221
spacy/lang/en/syntax_iterators.py,sha256=ITkQWvraE1s8GwRxBhC2zLBmDitYSDxtdl4OHAktZGU,1620
spacy/lang/en/tokenizer_exceptions.py,sha256=-ZzAp7jqPwKDs58ZMyUmdnBOxhG-ryYQPq3KgwhrmTk,14778
spacy/lang/es/__init__.py,sha256=mEjfPgwrCcoM_j5D0OU00kQqwXQu5lfCf1IY6VglWHg,1338
spacy/lang/es/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/es/__pycache__/examples.cpython-311.pyc,,
spacy/lang/es/__pycache__/lemmatizer.cpython-311.pyc,,
spacy/lang/es/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/es/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/es/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/es/__pycache__/syntax_iterators.cpython-311.pyc,,
spacy/lang/es/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/es/examples.py,sha256=EpY8in34BTVcMC8l1GR9tqPSRfVQlR3ZiSzSEOruo_4,800
spacy/lang/es/lemmatizer.py,sha256=e4D9un6-y0B7qArgI_-7Q9qm2PeESBA-q0E_AnM4eNg,16448
spacy/lang/es/lex_attrs.py,sha256=xSvogCRcMQEPsFYTZvm-Qx21t4MPFcyQNPjR16m09Wg,1900
spacy/lang/es/punctuation.py,sha256=WwYl1F_xIQlK9tLFo-yR3XCCH1piAJOS6Z__tUEUHmM,1278
spacy/lang/es/stop_words.py,sha256=jizTlvy5N-nQmLAVwYUZC1QqXv7Xe9t-nU1yMjW2mgs,3468
spacy/lang/es/syntax_iterators.py,sha256=bKhySySxPqxI539XqKbLAAltLsxbYrI6iazzrwv1Ym8,2790
spacy/lang/es/tokenizer_exceptions.py,sha256=ET8bkyX0dR0bQjeVDncHDlBdGGZVElRccQhn7qa5Soo,1541
spacy/lang/et/__init__.py,sha256=swVV1JaW9GQNEBKSV-QAO7OjL0fkngGo9JTJKdYkvGw,265
spacy/lang/et/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/et/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/et/stop_words.py,sha256=p3f6Aa8PDXTPqiKxfSsfEE_9LBsmqOTayGmDDJucPHw,287
spacy/lang/eu/__init__.py,sha256=gQzi2YvWd4_j8dHIMuRyKsJrq3xwt67pOcrGdd9GlOo,405
spacy/lang/eu/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/eu/__pycache__/examples.cpython-311.pyc,,
spacy/lang/eu/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/eu/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/eu/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/eu/examples.py,sha256=4Yemvo2C6RiLmonJKRD1qRljmMMqu4ID0yl0BHTO_Lg,430
spacy/lang/eu/lex_attrs.py,sha256=6K1uXNmsJx_mBubPfzHua435LA5vd2TuAkLybbh-Yrc,1169
spacy/lang/eu/punctuation.py,sha256=QGpDXn2GI0giTe5paUEw9bXqL5fF89De6gBgTfWm_To,82
spacy/lang/eu/stop_words.py,sha256=q-d3a_gjlgRVf9bm_-l33KICnlb1qITNLLJBw_OH8YE,865
spacy/lang/fa/__init__.py,sha256=4uoAlXaKoSBE-MbAeFnnliR28eBUXO7kJfvcecMQFq8,1355
spacy/lang/fa/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/fa/__pycache__/examples.cpython-311.pyc,,
spacy/lang/fa/__pycache__/generate_verbs_exc.cpython-311.pyc,,
spacy/lang/fa/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/fa/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/fa/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/fa/__pycache__/syntax_iterators.cpython-311.pyc,,
spacy/lang/fa/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/fa/examples.py,sha256=b00tiJRxEWeJ_wjkaxmQV4eCwGW-lM8Bp8QAT9SYehE,530
spacy/lang/fa/generate_verbs_exc.py,sha256=-Uwhwh4B2KTYu24t9mrLnowurNoJ0eMGUIXsYbIBDKc,15507
spacy/lang/fa/lex_attrs.py,sha256=En9T_CJB3NIAW7eSNOauVxTiREmdc9HpRDD9CL-G-aY,1488
spacy/lang/fa/punctuation.py,sha256=kwFO-dF3X5M3zSHl9VmQ0MzISjGUBKTCSLCspCVXddw,523
spacy/lang/fa/stop_words.py,sha256=VCbkQeLN8_NgMOYxSqQlXR5OQicCS5Ybo4Xm-bY63TI,4161
spacy/lang/fa/syntax_iterators.py,sha256=fZxCTM_lTpVcZ4qJJMY_76lwJD_eI3pXrFaBHBoW5Dw,1605
spacy/lang/fa/tokenizer_exceptions.py,sha256=0CKwapiUZ8g1F9x9202BC5LFYugOTYhOfOlGKXQh2aE,65668
spacy/lang/fi/__init__.py,sha256=CRo2F5VhbvkR4IONJxa1kKlYvesQbqgwRdJGmZhZ1jY,655
spacy/lang/fi/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/fi/__pycache__/examples.cpython-311.pyc,,
spacy/lang/fi/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/fi/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/fi/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/fi/__pycache__/syntax_iterators.cpython-311.pyc,,
spacy/lang/fi/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/fi/examples.py,sha256=Y8OCJxQnDjWVeoACOcn_rTO-XXdEb2KC_qynFKSrPBQ,560
spacy/lang/fi/lex_attrs.py,sha256=evDem_ItNBdr3o3SSNn6fek0HXelljjTqdy2CDIPKxY,1133
spacy/lang/fi/punctuation.py,sha256=uaWSoImS-YFh6A8Rf1mHg6VPTqz3kNgFQbG7EW1OYYM,886
spacy/lang/fi/stop_words.py,sha256=fpzLwU1H0F_L3BzDjFCg90YfwVpGXibyJT517cCL3QM,6546
spacy/lang/fi/syntax_iterators.py,sha256=_j68WcN8ZIT59Np6EBVI_9ZHCs1-bv8oPX4zQI7TeiY,2458
spacy/lang/fi/tokenizer_exceptions.py,sha256=P2FjyyUMvCHSOC3AQMV5DIAKIaksvbaUlQ5ZEuPG3EY,2580
spacy/lang/fr/__init__.py,sha256=USxp8XotnmHUmRyAAr6sfMlG3P5M8XWBSxgvC0KllPg,1459
spacy/lang/fr/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/fr/__pycache__/_tokenizer_exceptions_list.cpython-311.pyc,,
spacy/lang/fr/__pycache__/examples.cpython-311.pyc,,
spacy/lang/fr/__pycache__/lemmatizer.cpython-311.pyc,,
spacy/lang/fr/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/fr/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/fr/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/fr/__pycache__/syntax_iterators.cpython-311.pyc,,
spacy/lang/fr/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/fr/_tokenizer_exceptions_list.py,sha256=NFyro9MHB3ESK7XC5UrTR8JevRjdxhIsPfUpZnKZlr4,376238
spacy/lang/fr/examples.py,sha256=U19QRWst70fOOTYlQy-XNaBj5vdQZbvz6f6Ka8bc82g,960
spacy/lang/fr/lemmatizer.py,sha256=Wso4aXTdK8wxD7Q25mzAr32rDqTyyd0BRE3piG8XiE8,3103
spacy/lang/fr/lex_attrs.py,sha256=GE4raEjnLk9cH9ER50d9r4GhFa3i9qqIyM-R4Et3ido,1671
spacy/lang/fr/punctuation.py,sha256=pGiXzayXBYSdnfBbV_m29WY_7-ABfwzLhvLphb01S6o,1524
spacy/lang/fr/stop_words.py,sha256=sOe7bx2a3Q6XleqnE7ZTsvxrEO8hyHebB5cA7rg5BdA,3588
spacy/lang/fr/syntax_iterators.py,sha256=3mk6EXJ0LKsOcaom7GTLi6zQEcS2o2upipxNxpu7iBA,3209
spacy/lang/fr/tokenizer_exceptions.py,sha256=juX5dQczyRitcMt-1gMMywKtXiPdrf3FZ_quCeWC2S8,11676
spacy/lang/ga/__init__.py,sha256=BNS0Br5L4bRxjkOxbJ3f0ikKNHhkAO-iqsTBnQa07Eg,852
spacy/lang/ga/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/ga/__pycache__/lemmatizer.cpython-311.pyc,,
spacy/lang/ga/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/ga/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/ga/lemmatizer.py,sha256=iKlfkhnoYUSx9DoTNSkdJ_D8lagrK1cVKNdrTBxBfC4,5098
spacy/lang/ga/stop_words.py,sha256=hhQX1ZWyWAbGR_xqjbMvNxn_NOGD2T57ndaYprSYmAI,651
spacy/lang/ga/tokenizer_exceptions.py,sha256=Z6XcRv81n6Q8qKFB23BIk2PGVJFxoyt7YvXSslABvNU,1955
spacy/lang/grc/__init__.py,sha256=HZDaH9plP2knkGNE68x16TimoI-_SsWSRNJ_IivE8E4,642
spacy/lang/grc/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/grc/__pycache__/examples.cpython-311.pyc,,
spacy/lang/grc/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/grc/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/grc/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/grc/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/grc/examples.py,sha256=qc2idWpdjDmWvdJh-0A_-WfB5ZVUpGeSUZn0H_ZoG-U,1083
spacy/lang/grc/lex_attrs.py,sha256=9yFYUeXtXAJWEJauJhiAL2-R16EYIeNSkKd2_G7c9qc,6955
spacy/lang/grc/punctuation.py,sha256=E-DUuRJV0ZPkDXsWQ_-98nrWlDSUxWzLbFp2U4CfHAk,1128
spacy/lang/grc/stop_words.py,sha256=BJnEOTTM-iK5qAwC7UGiE-O7HHjHIQS2TyPox4Z7keg,9425
spacy/lang/grc/tokenizer_exceptions.py,sha256=LxYV_dmJWUlDYgCoTyoX8swj5r2Dtz12SKvSakAxGrk,6879
spacy/lang/gu/__init__.py,sha256=HdANFnFqj716Zp42JhjxT3LmTMclhx7oWBRnKnxCgbM,265
spacy/lang/gu/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/gu/__pycache__/examples.cpython-311.pyc,,
spacy/lang/gu/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/gu/examples.py,sha256=azYslIKVVuXf1XqKLtPgc2ZyUz0RsrVM43lebWbhlZQ,1233
spacy/lang/gu/stop_words.py,sha256=M5mZ-PJ6f-CP9R__YhCEkesa30i8wwG2-vwXk7fLSuI,1092
spacy/lang/he/__init__.py,sha256=jxfpOhQyvJFtO-OwL6wlesj9RzaOuIj-LpIZZvcPd40,408
spacy/lang/he/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/he/__pycache__/examples.cpython-311.pyc,,
spacy/lang/he/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/he/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/he/examples.py,sha256=Rsi8orYnRL8xiT9vfvOabOp1aF_uP8haN-dCB9EXE1s,1018
spacy/lang/he/lex_attrs.py,sha256=CmsTNg4bfhZyIZivs9Bkw2S2LL3MpsJzU1sdJ81n3wI,1854
spacy/lang/he/stop_words.py,sha256=lL3fSBVBI81D6uVi8vsZIUy37a1tYPER1GZMOvKsBpY,2078
spacy/lang/hi/__init__.py,sha256=My0PNDRB0BpXEXhGQR6irVWULGt_PapkM5aDESURNLA,321
spacy/lang/hi/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/hi/__pycache__/examples.cpython-311.pyc,,
spacy/lang/hi/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/hi/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/hi/examples.py,sha256=rcUt8Ai2YA0Fwhyeovf8WpJH4jt18j09jPMgxKa8qSc,1538
spacy/lang/hi/lex_attrs.py,sha256=JV6CXS_igQncizI5IC13RXUQ0f94knDQgbJreoAv_3g,6078
spacy/lang/hi/stop_words.py,sha256=W3rrJYPIAwA02rb5cLgA_7TBXgpjRafbd6vxhCYGxlI,3214
spacy/lang/hr/__init__.py,sha256=eKrU7qpH5H_T8q3CMEFTtmpIY19seVDCAQjSkrXc4NI,265
spacy/lang/hr/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/hr/__pycache__/examples.cpython-311.pyc,,
spacy/lang/hr/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/hr/examples.py,sha256=QcYby6oQFVNzDetgOyfbiy8hcJqRbKH1GQyP5nYxtt0,504
spacy/lang/hr/lemma_lookup_license.txt,sha256=NA7Mg7DIJL8zD1uQKPu9wOFuah6XN1zYH0cFTpJkD44,984
spacy/lang/hr/stop_words.py,sha256=YF0ae-a9QfOyk9cNohZadb_LT2_4qVrmZ66D1H5QEFs,2343
spacy/lang/hsb/__init__.py,sha256=SENjcFJ4vcYVeV_xkeiDvFAKkNWIFErHut0Wsq7lB7s,455
spacy/lang/hsb/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/hsb/__pycache__/examples.cpython-311.pyc,,
spacy/lang/hsb/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/hsb/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/hsb/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/hsb/examples.py,sha256=RkJUOETfPcnyk1cAAMndC7GWAEtUwm5umYzYjcYLEmg,655
spacy/lang/hsb/lex_attrs.py,sha256=FkHtl2P55IAoM9VXuKXgCcpC-wz6h3fufIXRQxB_jx0,1897
spacy/lang/hsb/stop_words.py,sha256=61RWIlR0ctPp-j1f0vhUDZ5cVcaPO4pJYyC7fSe0YgU,142
spacy/lang/hsb/tokenizer_exceptions.py,sha256=0m6d555wSdVVf9suyG0KIjfu2H_V8vjBBXhaVWjq1Mo,404
spacy/lang/hu/__init__.py,sha256=dSwBRrJODe5FOkMBF3waC1p-pUXVlcTP1I11JBncGAk,605
spacy/lang/hu/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/hu/__pycache__/examples.cpython-311.pyc,,
spacy/lang/hu/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/hu/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/hu/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/hu/examples.py,sha256=XSt0PLEabm2cqGiUPLluh3IPy5WRZ9HMKpm3B5T6uio,420
spacy/lang/hu/punctuation.py,sha256=ef_da7a8f0Bv8sFwWEX490ubcWsmDPXs0xBtVZiLEMI,1544
spacy/lang/hu/stop_words.py,sha256=f27xbpOgpENj-af3GkCFj2rbDX_Zabe-1voHJ_4klik,1446
spacy/lang/hu/tokenizer_exceptions.py,sha256=jJxT0Rzb1GsRN7nllG20uwi2_iy_kMTj_7hS8CCKM0k,9056
spacy/lang/hy/__init__.py,sha256=v6Us8iXt7JMVK_qGaUpQgQ7ybWBnxeLS5dLDE-kNjl0,333
spacy/lang/hy/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/hy/__pycache__/examples.cpython-311.pyc,,
spacy/lang/hy/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/hy/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/hy/examples.py,sha256=qMN1LPP6W93LoNCSUrGGxaW1hy7EJQRTOzLbUbuFGzU,454
spacy/lang/hy/lex_attrs.py,sha256=CFhxaqm-EkRGANGnbwaG5qZB5M10cuTL8wcdw9igRtU,1217
spacy/lang/hy/stop_words.py,sha256=_FgA_mjC3WBjVGGT5ocZcizo18baVvVRxYIiQIU7Ct0,1174
spacy/lang/id/__init__.py,sha256=Otv_Fs7h3TS0EEv307hjwN_ItluZGvCbcgJSEloeFzA,722
spacy/lang/id/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/id/__pycache__/_tokenizer_exceptions_list.cpython-311.pyc,,
spacy/lang/id/__pycache__/examples.cpython-311.pyc,,
spacy/lang/id/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/id/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/id/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/id/__pycache__/syntax_iterators.cpython-311.pyc,,
spacy/lang/id/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/id/_tokenizer_exceptions_list.py,sha256=szqi4HgQvdkesccZ6lIfsuChTtcrbpvkonGHnr-hAUM,57501
spacy/lang/id/examples.py,sha256=zalF8AFkwpK1m47rijeZjD0aJZTylzu0NXXPNKoMZQs,744
spacy/lang/id/lex_attrs.py,sha256=IQEK5EtM53L3Hi2UPSMXsnBW4Ev1c3kxloRnHaXmwEk,1344
spacy/lang/id/punctuation.py,sha256=56JYywDjWoSQZBO6YlvuirJCh4XS56ls8OFpc-4AQs0,2200
spacy/lang/id/stop_words.py,sha256=rfrNIC1F8G3KnF1PUUVgpEBKROsIQd1Ynd1ArMiRoFg,6625
spacy/lang/id/syntax_iterators.py,sha256=l8jmNz7xeHlFkjv5vLSSW-tBwc1gjviK_s38Ij4ANBk,1579
spacy/lang/id/tokenizer_exceptions.py,sha256=Tvbe9CJC_vQH_9TKNumHyJim5Kd-Id80ol9bLAhAQEw,4426
spacy/lang/is/__init__.py,sha256=k2bJ_8Be5xPMChcxO_niYS2IbI6Da_HRsnVBvlhQIpY,269
spacy/lang/is/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/is/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/is/stop_words.py,sha256=EXvUI9qz7EXQvuATHfJDD9oF6oM4_exf1Uyv9hAvAx8,1177
spacy/lang/it/__init__.py,sha256=SplSnla5QckQQobMMrv5CekSRD-cCKQleNajuLt91ws,1278
spacy/lang/it/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/it/__pycache__/examples.cpython-311.pyc,,
spacy/lang/it/__pycache__/lemmatizer.cpython-311.pyc,,
spacy/lang/it/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/it/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/it/__pycache__/syntax_iterators.cpython-311.pyc,,
spacy/lang/it/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/it/examples.py,sha256=LJ4T3pfU6GmnfaOpcCDrxABzZB72BFvKLLX5PuMzsBs,485
spacy/lang/it/lemmatizer.py,sha256=1h391bJJBuPKnbPHtDHO7AH1stlxtXk2u9YSWmbiphs,4747
spacy/lang/it/punctuation.py,sha256=_lGGA4CKTy0heNjUnF9lbctoB3JmU0RnfTROZzt47Go,902
spacy/lang/it/stop_words.py,sha256=agTiYrl4t-8vHlleZOjdFstYZD2HdQWtKoar7iMpKsY,4197
spacy/lang/it/syntax_iterators.py,sha256=gF8Cyi6aOL-SGR7810zepHgGjhdcDo2rowjzSDPa47c,3223
spacy/lang/it/tokenizer_exceptions.py,sha256=A9yQunRKYTl0CDfXzp48Lt1UJefbu95UBS6RRY9rEp8,1226
spacy/lang/ja/__init__.py,sha256=68mzDWg5ueunOLDManlnERCnZk-MUPIFhwyhlNM4sCs,12965
spacy/lang/ja/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/ja/__pycache__/examples.cpython-311.pyc,,
spacy/lang/ja/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/ja/__pycache__/syntax_iterators.cpython-311.pyc,,
spacy/lang/ja/__pycache__/tag_bigram_map.cpython-311.pyc,,
spacy/lang/ja/__pycache__/tag_map.cpython-311.pyc,,
spacy/lang/ja/__pycache__/tag_orth_map.cpython-311.pyc,,
spacy/lang/ja/examples.py,sha256=8-O7TAHWXFykbPhC4NlTVxbrUmraQ-ZamzTtZEf3qu8,513
spacy/lang/ja/stop_words.py,sha256=L0DZgqvjvTpJYq2f0Wrkue5423i6gEqxJJnUNmMUhIA,1376
spacy/lang/ja/syntax_iterators.py,sha256=86HBPy2MvQue9DS-yATakY2S5ffoi_UjRWcaJASFA7Y,1681
spacy/lang/ja/tag_bigram_map.py,sha256=vurQQpZV__0ZMuMnkgpXUHULzJ3AaX6NSPdi0P0-2OM,1675
spacy/lang/ja/tag_map.py,sha256=KDS9dpMRbpMWDd3znMSp8ys2Q38W40lqFvl9imRen1s,3809
spacy/lang/ja/tag_orth_map.py,sha256=XHc6dKfvs4-s3CO6-fgxobsBjosIxpc1WKGITqPTo-4,568
spacy/lang/kn/__init__.py,sha256=1DJARxqhlLq6EFrNPRzVKCtddg8BUu4CXtTM65W5P9o,261
spacy/lang/kn/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/kn/__pycache__/examples.cpython-311.pyc,,
spacy/lang/kn/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/kn/examples.py,sha256=vmndHquv2M8-H9UHpjwJTeDYs9FnmluL2Etx9515_i0,1229
spacy/lang/kn/stop_words.py,sha256=heqqOvegkHUVuMyhfFdbvW9MXHoOmIjy42XE9JK-WtU,1339
spacy/lang/ko/__init__.py,sha256=47kF5TjNv283hM9uhLk47jBc3iUViGrVEpUFjcSKFjg,4428
spacy/lang/ko/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/ko/__pycache__/examples.cpython-311.pyc,,
spacy/lang/ko/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/ko/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/ko/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/ko/__pycache__/tag_map.cpython-311.pyc,,
spacy/lang/ko/examples.py,sha256=e7Hfr5AklFlXqV5Xgv9O11L9Si6y1KWllSuuVkm5tPc,544
spacy/lang/ko/lex_attrs.py,sha256=alRLqiTKNGz-n8zyrwuuypFJWng10XKuXSBco1gZuBk,1116
spacy/lang/ko/punctuation.py,sha256=iwvxBj_3ZwzpNdlVoIdmIcxYbHmwUtxJenzJpHZEIJE,280
spacy/lang/ko/stop_words.py,sha256=mj55PmpDCzfFKD0S-aPWhfslUH676mngevUgGLq0N7M,416
spacy/lang/ko/tag_map.py,sha256=YFlL9ioxfwgYHbRgN-yIhMM2FPsY0jmSzYCBC1Ms_TA,1984
spacy/lang/ky/__init__.py,sha256=OlTKMRfgcv9caQ-jGBP5RQmNYnyiPngmbZoGE7fopcc,507
spacy/lang/ky/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/ky/__pycache__/examples.cpython-311.pyc,,
spacy/lang/ky/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/ky/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/ky/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/ky/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/ky/examples.py,sha256=Y0DLLQAJqFbw6kLhweF4BDsGUb9jh2e1Hk_1gTiustE,933
spacy/lang/ky/lex_attrs.py,sha256=dQQcKGMrbmyXHzr0TW3_ZhP_ded2ioeHliZjdXM4Qy4,973
spacy/lang/ky/punctuation.py,sha256=GSwtfvjYEU8naazkfaIO7CCe2RUksFu3xGdgxmLneVQ,869
spacy/lang/ky/stop_words.py,sha256=CbB8f1CS366hu5FBbbbiCX6FB9ZDcNgzl58chZnUB9Q,1106
spacy/lang/ky/tokenizer_exceptions.py,sha256=Pd8BvXnRwQWsMGYPN06mf4Yune32mLBODXEfR9DIX1s,2102
spacy/lang/la/__init__.py,sha256=DMQPm0cG8J_AcXmoGuN6ryjNSsLHWUu6C9jMEp1ZyCU,426
spacy/lang/la/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/la/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/la/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/la/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/la/lex_attrs.py,sha256=2gHO9L_1I0Sv1SaxiLXzzcJI3anWTDc_bQKx3kaNThc,775
spacy/lang/la/stop_words.py,sha256=A94nMEoHTw1GyF1ushvyqFtmeDlESEHD-bEAU5pN9cQ,656
spacy/lang/la/tokenizer_exceptions.py,sha256=w8V6WVv7CtqKZLePuiJcK8YQzHaIOpEkAq3HfJkt8p8,1251
spacy/lang/lb/__init__.py,sha256=iMweuwO-M8OOE64B_VXl4kwqKdw8ecf1LKuUBIAdWpM,535
spacy/lang/lb/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/lb/__pycache__/examples.cpython-311.pyc,,
spacy/lang/lb/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/lb/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/lb/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/lb/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/lb/examples.py,sha256=px80hXqUPVv0tJlsDQSJYmcVMm_6kdmvhM4qUrFIlqQ,921
spacy/lang/lb/lex_attrs.py,sha256=N9IB20G0fE8QKDaiexrJHm1dMoys6ycmAKa3sXnuJEU,1384
spacy/lang/lb/punctuation.py,sha256=gxGY5Poy3r9OjEljIqDUCcJRsP_EvrlbbnUY2hmcWNw,662
spacy/lang/lb/stop_words.py,sha256=DTkY6Pgb1XjDv3bD1jG2eIkYvjygM6cTLr5j2SW10Eo,1338
spacy/lang/lb/tokenizer_exceptions.py,sha256=uL1vr3jnOTHrKQejRrlE0CACCFpLzFaunS4QE_hI5pA,1221
spacy/lang/lex_attrs.py,sha256=CFs9coC-xNgp_JlUDE3XQ0bSyAbA2LU8TzEL0B48r-4,6158
spacy/lang/lg/__init__.py,sha256=tmAdXffhiNb55nemLQcuQtr5_F1Oio5AoDOhCC93jvw,406
spacy/lang/lg/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/lg/__pycache__/examples.cpython-311.pyc,,
spacy/lang/lg/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/lg/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/lg/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/lg/examples.py,sha256=RcpWEuFjI9XmZktNs2YenN0WBhyUHRc6pXqd4yz1hTg,539
spacy/lang/lg/lex_attrs.py,sha256=OndRpwzBfwbOr6IY-IAjEU6LAMkfRvJvNbSVjpkt6No,2775
spacy/lang/lg/punctuation.py,sha256=pMLgYGwZRLpzgc_4XcFtsgRzNl3kYSjBblMTpqp7zj4,588
spacy/lang/lg/stop_words.py,sha256=0zdX5GzLJQGOHHGlWetYbeyEaJfTTsZaWh07MvOkSoY,1380
spacy/lang/lij/__init__.py,sha256=YvvzvZ1Rn4QKOieDnKS611p2j_t4IgswBDA2rOHf6GE,448
spacy/lang/lij/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/lij/__pycache__/examples.cpython-311.pyc,,
spacy/lang/lij/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/lij/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/lij/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/lij/examples.py,sha256=4wG7CJIBIN5o9x3UaezvSAe4-K1Il72Hb3bTu-yYh38,411
spacy/lang/lij/punctuation.py,sha256=uVEh6MqRGMPMg_WL9mzmvY-z84TOasUSkIglIBJsHfY,282
spacy/lang/lij/stop_words.py,sha256=_8_wvIcuk3uWBBvxQsKKt5LLo3A5tvFloH_aM9z0ykk,892
spacy/lang/lij/tokenizer_exceptions.py,sha256=0r9JVCTkHNPn1yuCooiToNuVRUAZ-AnH2YH_UnJgtj8,921
spacy/lang/lt/__init__.py,sha256=mxJ69vvJpaDCE-HZ3lfbYmvHA_r1Z7rERxITqejQ6Wg,578
spacy/lang/lt/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/lt/__pycache__/examples.cpython-311.pyc,,
spacy/lang/lt/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/lt/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/lt/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/lt/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/lt/examples.py,sha256=xRt9Oa5AnmHf0pdfCpoOFdv2r54mzZfCaGyTYWD2f0w,620
spacy/lang/lt/lex_attrs.py,sha256=8gugw5vY22Owx1dsmmO-UYKClgo_5p70XoGIkbSMraQ,23614
spacy/lang/lt/punctuation.py,sha256=xYI1SJh4IYwkHXynX82W6Q2zef_conztKmfPePmcxys,742
spacy/lang/lt/stop_words.py,sha256=QZAMwwPmw0LZRzen79mUA3C_8N3q16KmLSYeh-bLu74,21024
spacy/lang/lt/tokenizer_exceptions.py,sha256=LyiGkebEdBIHD7z4nTN4YaJSda6oxZgE6cdtzpHL4Cg,398
spacy/lang/lv/__init__.py,sha256=JR3ZisYkoDfZTTC9PP1jtUhoKHoFHmRad4G9JaFzlIk,261
spacy/lang/lv/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/lv/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/lv/stop_words.py,sha256=RmsiFu7FpypSXR47ylxFqwphsD1v1KlDShcpgWByJY4,1252
spacy/lang/mk/__init__.py,sha256=D6XeumE8gbdK63yft_yT-5ExnSF7StO9k6yc3LdY114,1751
spacy/lang/mk/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/mk/__pycache__/lemmatizer.cpython-311.pyc,,
spacy/lang/mk/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/mk/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/mk/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/mk/lemmatizer.py,sha256=bLenJDxpjCbhHlWG4Jnic-9Ki1A223aZ_wAwC2tHsM8,1776
spacy/lang/mk/lex_attrs.py,sha256=QkW-7NK5tUwIQTL9HUYHTpFEJ5Czm52u9b2jrYb1Sc4,3606
spacy/lang/mk/stop_words.py,sha256=uqaWWVWbohSY5cVBxpBkXkAb5I_Of1VDkOLdFQdhWpg,9921
spacy/lang/mk/tokenizer_exceptions.py,sha256=elfigAuLHi4HGrzz1KsC1XvjuQbVlLgyBQpHPfBeqHo,3671
spacy/lang/ml/__init__.py,sha256=0DB0G_Yz-ZO18a_XQmj60mOIoDWrCe3OORN3f0NWTVI,337
spacy/lang/ml/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/ml/__pycache__/examples.cpython-311.pyc,,
spacy/lang/ml/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/ml/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/ml/examples.py,sha256=kPYoIerYcUBl4See8O64pKnApQuL6OqaZKCtg-BTaQ0,1418
spacy/lang/ml/lex_attrs.py,sha256=H1x8dlC1ZNnjSIaKnk7QiUWUjRu591CLgxqfQdSCxNg,2422
spacy/lang/ml/stop_words.py,sha256=9qiBsIRcMYKSjoyd1rymt16ADnmncJ8CPH2jidkV9R8,197
spacy/lang/mr/__init__.py,sha256=3vlOL4f5Z-Q1BSWhrBm4Yz0ghAdr2Hby4z1BAYw7SMk,261
spacy/lang/mr/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/mr/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/mr/stop_words.py,sha256=FAmWTdp-CqIRshGvfke7l0bgSS2I5OOg28__hiDMvjw,2648
spacy/lang/nb/__init__.py,sha256=_gRFSERF2t1YV301fKf6MckE3RA0Hd9ygOLtZMu2FWU,1346
spacy/lang/nb/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/nb/__pycache__/examples.cpython-311.pyc,,
spacy/lang/nb/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/nb/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/nb/__pycache__/syntax_iterators.cpython-311.pyc,,
spacy/lang/nb/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/nb/examples.py,sha256=HN4wF08J9u3eVe0JcdSR57tY5QhUgk2ctd8awXvu4f0,442
spacy/lang/nb/punctuation.py,sha256=Ukie9CE0fPcgWuyTdnBeJYzT1uO0echWeNpk5NOI2lM,1722
spacy/lang/nb/stop_words.py,sha256=Yb_pOJAkxa466znM8-uaXhJywLY4ga_xqU2WHIeVofI,1210
spacy/lang/nb/syntax_iterators.py,sha256=isaz-skZQ3AAlmftSY-Effx1JfUt_3JOJtrDK2QLzEk,1562
spacy/lang/nb/tokenizer_exceptions.py,sha256=CmDXvM98DkyLXM3Qn3BaVnsefCo5_bTnBSk6P_fAPEI,3292
spacy/lang/ne/__init__.py,sha256=VVGL3mt-XHQrjeVxWMiQ1b-RqUfmVc8-9Nm7rUdQ92Y,325
spacy/lang/ne/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/ne/__pycache__/examples.cpython-311.pyc,,
spacy/lang/ne/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/ne/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/ne/examples.py,sha256=H5WyGuVuVC5GEUzA-BixEcPatUHsbrq8CkCwH_kiuso,1122
spacy/lang/ne/lex_attrs.py,sha256=XF937O5PsyURPz-gob2_7dtL5T6D7VVYjyZTAfMcdJ0,3371
spacy/lang/ne/stop_words.py,sha256=SkKIXg7nHfUdK27DSnfnh0g2Ud9Ba_P6wMC96mjZJHo,7629
spacy/lang/nl/__init__.py,sha256=eKzCXlWh_WX0PkSZYSEbVTGCCibrA8GGiA9j5-zUCxE,1408
spacy/lang/nl/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/nl/__pycache__/examples.cpython-311.pyc,,
spacy/lang/nl/__pycache__/lemmatizer.cpython-311.pyc,,
spacy/lang/nl/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/nl/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/nl/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/nl/__pycache__/syntax_iterators.cpython-311.pyc,,
spacy/lang/nl/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/nl/examples.py,sha256=28vswa9lz5GCDMxOxdx2iVkdNxuCYH1WK2HjFOrEwi8,455
spacy/lang/nl/lemmatizer.py,sha256=V3ADssYt7NaB6HVh8qcaRvBNZ1Ryd3CH0nVQQuEnpuc,4740
spacy/lang/nl/lex_attrs.py,sha256=cessvnKzDWSZUXtowwp2WoTb8AfpIKrtY0M5Arm4oD0,1344
spacy/lang/nl/punctuation.py,sha256=eqgTSzR7QegiwSUOXubIg7SaNL3g8qbGTqQ0ST7CjRE,1594
spacy/lang/nl/stop_words.py,sha256=Rr2DXFE-WPYgm28fHXllZHGO2OboVDIIMmFCVparArA,3162
spacy/lang/nl/syntax_iterators.py,sha256=0wrDIBD0_wnR2pVz6OVnSQrrZtmp9DmE6DZynPbDqkw,2943
spacy/lang/nl/tokenizer_exceptions.py,sha256=E8VRZE7P0wCW9nAZgixXbl9bOUUmRvaTTNpTgRE4MkM,25907
spacy/lang/norm_exceptions.py,sha256=ZGmzDTcCexrANn_gPdEqNbLiceSDaHh4n39sm-Zt5y4,1484
spacy/lang/pl/__init__.py,sha256=TfMXmbJi4L3kNrlYeL8Vpm0X_j9cfygCdkQDzDwi5MM,1440
spacy/lang/pl/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/pl/__pycache__/examples.cpython-311.pyc,,
spacy/lang/pl/__pycache__/lemmatizer.cpython-311.pyc,,
spacy/lang/pl/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/pl/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/pl/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/pl/examples.py,sha256=HJilebEFe1z97PqOy9whydjGEXJUQO195Qpojg48V14,701
spacy/lang/pl/lemmatizer.py,sha256=QCzzw4x2ZilPN1twoW30BI0P4JOYMQG4twGGf7B6Gpk,3652
spacy/lang/pl/lex_attrs.py,sha256=3xKyETheFg-zH7b7pWtaUo92poXvSrvj2d9EU1bfS-o,1259
spacy/lang/pl/punctuation.py,sha256=mMGCafJ2qWu_oApjhmf7MaEPDZq7mnBv1H6c1FcR1rs,1407
spacy/lang/pl/stop_words.py,sha256=ZGtJuxZ7afMHQNRBay2ma7JVGGZh8xIzDGtXjRheQkc,2438
spacy/lang/pt/__init__.py,sha256=qAUoPyzoACX1tPV18c67W2iFDn1fNWKbwZTUDvHv9fo,667
spacy/lang/pt/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/pt/__pycache__/examples.cpython-311.pyc,,
spacy/lang/pt/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/pt/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/pt/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/pt/__pycache__/syntax_iterators.cpython-311.pyc,,
spacy/lang/pt/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/pt/examples.py,sha256=DVRcAlpxr4Nshpn9nHOqn6WI-PRQC42ZmxyIY3xfOx8,486
spacy/lang/pt/lex_attrs.py,sha256=kaUkvKJP5wd83T8bX-1Mgw3PG1AkbHL-7oRM3a6P8GQ,2157
spacy/lang/pt/punctuation.py,sha256=FNAWGaz6opyvryFrl_1XOE0Mx1qMiXQI9ESjKjjtk-A,469
spacy/lang/pt/stop_words.py,sha256=NhbgY6eIlAvV4GOXf5fQVd9qnD-lxUJyVj9_v_BVrc0,2632
spacy/lang/pt/syntax_iterators.py,sha256=WnNLqY7tStDdTJAK1oobYZnyHvmKrGrqPODcCwcA6pc,3173
spacy/lang/pt/tokenizer_exceptions.py,sha256=9w7R7MRNTQ3uDmZqMgCqVl67kyBT0VgQW3YuzcCUDIw,771
spacy/lang/punctuation.py,sha256=FNInetCdLPBK_N-V6uIJBvN3kOvfuAHbdN9DtdjfMu4,2258
spacy/lang/ro/__init__.py,sha256=mSUrphN78EzRzUyNdz7N30uQR5QEJgU6SX1CRVgxD6s,829
spacy/lang/ro/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/ro/__pycache__/examples.cpython-311.pyc,,
spacy/lang/ro/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/ro/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/ro/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/ro/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/ro/examples.py,sha256=fih3NZrF7U8gQp-HG45MpTwR1s_iqjSUf5snsLkL8ro,620
spacy/lang/ro/lex_attrs.py,sha256=jHbvMFeghyMdVpG8yNXHb1aqm7aiiYwXydZl5Kmnytw,1723
spacy/lang/ro/punctuation.py,sha256=Knpd8XXYQJ7E-KE881__Ga88JtQVJQgaZ3FjKn1eMvs,3319
spacy/lang/ro/stop_words.py,sha256=3w-5tCaUQrO0eK9XUFdaltPKiOHQ5aPGneaxnCh5vWs,3444
spacy/lang/ro/tokenizer_exceptions.py,sha256=YSeEZxktT-1GffF9pmen_wfEcBb0hFkmtqWA2H8dTMY,1557
spacy/lang/ru/__init__.py,sha256=sa4YxWWva8zOK5yF1B8IEDU6AwGQ3ZSE6Lj_DduFgvc,1367
spacy/lang/ru/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/ru/__pycache__/examples.cpython-311.pyc,,
spacy/lang/ru/__pycache__/lemmatizer.cpython-311.pyc,,
spacy/lang/ru/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/ru/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/ru/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/ru/examples.py,sha256=KyKqOedlBau3S6yC20YRk5yqPJhSEIHqryD7hzMWcTc,4621
spacy/lang/ru/lemmatizer.py,sha256=Qb0Ww90id6XxGWC0_GJAqSOqnrfdpHK4ReTHHEwdM98,8194
spacy/lang/ru/lex_attrs.py,sha256=P_R6XhqwW11l0HxhtOzh8_dFJY8xs6GwClpgbeeFnz4,18963
spacy/lang/ru/stop_words.py,sha256=TIDDXCUlHrownYkmreNwqjibo_FU5PNBj8UaTUPHEOY,8467
spacy/lang/ru/tokenizer_exceptions.py,sha256=zH_J9uKrt_LATZZesxBJVTzGPvnco16IKeAutWM1pBY,25801
spacy/lang/sa/__init__.py,sha256=uVYlY0TmGXt9Yj9QnCmOBzhBmlS4RMx7S3KtHzlB3ac,333
spacy/lang/sa/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/sa/__pycache__/examples.cpython-311.pyc,,
spacy/lang/sa/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/sa/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/sa/examples.py,sha256=ut7L8BMbtOhRaAOIpARNLestlRWL9u607CwHamEIziY,796
spacy/lang/sa/lex_attrs.py,sha256=BzQaYB4SxLYAvfifQIMUzZ9ojcjgx2ox_1nCiZvcE1U,4338
spacy/lang/sa/stop_words.py,sha256=PSmEfRkQRzIcqKAWTr5q-s5oO-q8E1cYr6tFeXF6OJw,9879
spacy/lang/si/__init__.py,sha256=ASLsZa3fPsEufnXfe-T25qzpZIWRI6b5pVtBmzzUa5E,329
spacy/lang/si/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/si/__pycache__/examples.cpython-311.pyc,,
spacy/lang/si/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/si/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/si/examples.py,sha256=IYXhhVA7W-pgeucEW4mGCKh9ibnTOnUzqZvltH4sOJk,963
spacy/lang/si/lex_attrs.py,sha256=Ch9E4iWCc78Vwxzc6b6hex1JziLww9mPg8Su_yLgJeA,1314
spacy/lang/si/stop_words.py,sha256=Bo1UgDYSSCJDGckpMCfD4kqAwp_28_43obV0hmSXxB4,2602
spacy/lang/sk/__init__.py,sha256=i8s3oDRbh1s-Oa5Hd5qJx5W3RX1UXiJ1_STUA91uDFs,325
spacy/lang/sk/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/sk/__pycache__/examples.cpython-311.pyc,,
spacy/lang/sk/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/sk/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/sk/examples.py,sha256=a27R3m7xM7FrGztxUiJCBkJOGyHBAOx53LMToZCz-dI,752
spacy/lang/sk/lex_attrs.py,sha256=LPiqigDoWakm-d9p4t10lB4o9Rsjie1WQMXk6RaYYxs,1129
spacy/lang/sk/stop_words.py,sha256=9M1izuYTadYb1Z39RBKlseL4PS_SYGqTZaRwsHXLgQk,3063
spacy/lang/sl/__init__.py,sha256=v6FyVuo31vudLPtRgzZIERf28Hhq1c88mAoIyf5ePyU,629
spacy/lang/sl/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/sl/__pycache__/examples.cpython-311.pyc,,
spacy/lang/sl/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/sl/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/sl/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/sl/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/sl/examples.py,sha256=eLstCmIbo1Zjh4y84ArrwoNLroS8EaqC4l1UYbBFFUk,593
spacy/lang/sl/lex_attrs.py,sha256=WuZIUOuGf4v_4TBBMe8pH1U7xfwgKCNkcVrdMOM8eL8,6748
spacy/lang/sl/punctuation.py,sha256=FlS1KtcFgfU0FnIinrSi0a9n9n78SdPOzMrW-KMOJDg,3358
spacy/lang/sl/stop_words.py,sha256=vn_Gc9OjH_bP7GKsDavkzKf-9XlPyparLG7W7LFBzds,2562
spacy/lang/sl/tokenizer_exceptions.py,sha256=g83EVGWCgrgcGE1Y7_hEs-w3h_UcmLJbjw1m2JmEJBo,13700
spacy/lang/sq/__init__.py,sha256=c2iw1YMqEeipMJ3M5MEx6y-ZVVAmzySvC9Jha8LNH1g,265
spacy/lang/sq/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/sq/__pycache__/examples.cpython-311.pyc,,
spacy/lang/sq/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/sq/examples.py,sha256=fHDerNI_lZqt5dvKOc4bJvAuy8IEZhbVvHDBFg_I2ug,481
spacy/lang/sq/stop_words.py,sha256=Z9BeFjdiCeh54CCVQy5gmUUqnc-3929YnRvBm3DkLUM,1439
spacy/lang/sr/__init__.py,sha256=JlkJobNyhB6__-tGRpWB7IpDnDDZFfu7HOT5CTa3lhY,434
spacy/lang/sr/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/sr/__pycache__/examples.cpython-311.pyc,,
spacy/lang/sr/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/sr/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/sr/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/sr/examples.py,sha256=kXXFBobg6dRLOcaEXT5L7EW6_Gb9fa6TDOgX9qzIuSY,931
spacy/lang/sr/lemma_lookup_licence.txt,sha256=grEvnqf2QM4EdMoQEtglVRz73zHXSBLUw-l8QLEKnIM,1580
spacy/lang/sr/lex_attrs.py,sha256=wZPOpI9qycAU0mqnZWNfbi0urnAnyj7UEZOrPwTA2RY,1492
spacy/lang/sr/stop_words.py,sha256=NZA6m9NUrzF2VdnFB4rPG8Yw8qB6a0XfbdnwFCnumNc,4288
spacy/lang/sr/tokenizer_exceptions.py,sha256=rk592tMGdPvTTHXtPaPcYk41tGUwBUYdhty_SlRxRrU,3617
spacy/lang/sv/__init__.py,sha256=EREvj08-xiL5NaR729ztmxudy2tIGq3-xRV35-DtFjY,1366
spacy/lang/sv/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/sv/__pycache__/examples.cpython-311.pyc,,
spacy/lang/sv/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/sv/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/sv/__pycache__/syntax_iterators.cpython-311.pyc,,
spacy/lang/sv/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/sv/examples.py,sha256=bGgneC4bRd7IKOujyrwhJ_zuw14ZvTEGVQfOhh4YnYM,455
spacy/lang/sv/lex_attrs.py,sha256=D9CZQ5dUx0_0bH_3hoirZS8YAdWJk5yHX2rJvwlETU0,1013
spacy/lang/sv/stop_words.py,sha256=vOxCdNYwB3_yQ1J6qJ8fvg2ujTZy-VhO0rNhauybPs8,2611
spacy/lang/sv/syntax_iterators.py,sha256=QuQEmqmMRn_5yjUAd-o6WwMLVxPjydauMMuHjsFiPOE,1570
spacy/lang/sv/tokenizer_exceptions.py,sha256=Amv1KccCDvt2atk_82vgXjKc4UF38GCiIiQBQtzvbyE,3811
spacy/lang/ta/__init__.py,sha256=oBpQXrJZPFCg2nbPi6XromK1UmDFXbkVdHXrAiLbWd8,321
spacy/lang/ta/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/ta/__pycache__/examples.cpython-311.pyc,,
spacy/lang/ta/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/ta/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/ta/examples.py,sha256=EV3SgndvOMJ4DSCLQdREezQeJtDPQljZ1he8b1uKwaY,2727
spacy/lang/ta/lex_attrs.py,sha256=aJgE0onIcvRi-4valgXP2q7vGCWiKVwQ0VmuN0TSX1M,2369
spacy/lang/ta/stop_words.py,sha256=O7cCwUSk8gvCjm3vc8AJTt6SyqNLFs3luBhGLGOZDAA,2149
spacy/lang/te/__init__.py,sha256=AlmuC6hiM9ut4M_jh-v-JiZdMsdVbG6pCwdzJv-BiCU,325
spacy/lang/te/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/te/__pycache__/examples.cpython-311.pyc,,
spacy/lang/te/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/te/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/te/examples.py,sha256=h8NXcL1kqqlC3jHjFgQNHeIHvd3fFebhm-4xT3Z9SfE,1263
spacy/lang/te/lex_attrs.py,sha256=LLGx2EJqiJZ4XO-5n9yUcN7J61FPb7d9sEHMgConWhs,1317
spacy/lang/te/stop_words.py,sha256=grEG5XgO80PZfB2FbU4_qOSq3TgH035FWVa7t-Q5MMo,1163
spacy/lang/th/__init__.py,sha256=E_ohYaDJ1cS_QICd2PqNNtia-0-EtEFFYeqz3ZLf2jc,1426
spacy/lang/th/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/th/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/th/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/th/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/th/lex_attrs.py,sha256=aRuhUMcctWB-cyZr_Xh2vT6nASe3b2fuo1wSb0lt2IU,1537
spacy/lang/th/stop_words.py,sha256=XDPxX727hZFYO_cag1bE2IJ5tvkxkC7saFViCCLwR-k,19538
spacy/lang/th/tokenizer_exceptions.py,sha256=Mnjemd7w8z6kLCiuXiyzjTIMke32T9pRkSpHTkf6VdY,18773
spacy/lang/ti/__init__.py,sha256=GMYs5y0GuU_50NB2z5qYugQsov9rQwbKnXkudnNugUs,862
spacy/lang/ti/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/ti/__pycache__/examples.cpython-311.pyc,,
spacy/lang/ti/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/ti/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/ti/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/ti/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/ti/examples.py,sha256=6AL1XkQCqnaOAFJlredzSwsRculdxRs9JsRp5ocY6YM,839
spacy/lang/ti/lex_attrs.py,sha256=GprYGv-NZ-KWX-oypdySINOjSauyPp25x_7sZOOsS6M,1581
spacy/lang/ti/punctuation.py,sha256=AhgV2VFLpjDftdAGGFyyncp4knBFCyi1GJ3GS38LNrI,564
spacy/lang/ti/stop_words.py,sha256=2fH5we0_yrBpueRhnLt3IZl1bDzRUk7jC5k0fKlhu2Y,2004
spacy/lang/ti/tokenizer_exceptions.py,sha256=mFLN626tyGdI2hofywjqNf1gxaAyv6FIalPnSmCrQOQ,361
spacy/lang/tl/__init__.py,sha256=_8064sZOgqnZNWJViY7Qd9tO2bOKS8-lVraxNsdbo_k,434
spacy/lang/tl/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/tl/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/tl/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/tl/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/tl/lex_attrs.py,sha256=Rh3zkg1fCY34w9LfM4g3yPAyF54HrBHM1fypyDwfUck,1000
spacy/lang/tl/stop_words.py,sha256=eLIUpFYpthu0IldXrxOFESniG6nqVARKX4BD9-CC-L8,1116
spacy/lang/tl/tokenizer_exceptions.py,sha256=1R4KCLvijp-DoW8ooh7ArUqbJ5GX9vzgO8Py-ooWP6c,707
spacy/lang/tn/__init__.py,sha256=sTVayuTRyRrKj-N42nXe_zBkqhhqGJ6GGcohdJfn1Wk,410
spacy/lang/tn/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/tn/__pycache__/examples.cpython-311.pyc,,
spacy/lang/tn/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/tn/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/tn/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/tn/examples.py,sha256=NIZ7ErTpo6p-qQCXajonm0FOzySWTZjUCMszzRvgkfU,436
spacy/lang/tn/lex_attrs.py,sha256=ZJL_Z6SYUDGtNOLa1qGMdPO0bNRhkvZZGgnkFb9_KCA,2050
spacy/lang/tn/punctuation.py,sha256=pMLgYGwZRLpzgc_4XcFtsgRzNl3kYSjBblMTpqp7zj4,588
spacy/lang/tn/stop_words.py,sha256=WEOgc_FnSf52V-mdMooCOPyjahEzvD1zHYZHUMytBmM,816
spacy/lang/tokenizer_exceptions.py,sha256=g48Q2g0Gn9-xoh0RNO70b_0UtapNberPQBAcTPLVstg,3734
spacy/lang/tr/__init__.py,sha256=l7ZZA9yKv5p02N5OJOABvQHGPKfgeJOOSBSj4Fn2IjY,567
spacy/lang/tr/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/tr/__pycache__/examples.cpython-311.pyc,,
spacy/lang/tr/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/tr/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/tr/__pycache__/syntax_iterators.cpython-311.pyc,,
spacy/lang/tr/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/tr/examples.py,sha256=D1YnRW_84zgjLhFDrSnnd2WAsO09Qw_FpiveTHAM8G4,627
spacy/lang/tr/lex_attrs.py,sha256=2k2SGDM60hMkRZs6QJgIQ2uZBRZaIymipkaVqe7AYOM,1763
spacy/lang/tr/stop_words.py,sha256=eva9AuhPLrVI3BLdu7faLSY34BXpQequHsbmsTLwciI,5063
spacy/lang/tr/syntax_iterators.py,sha256=oCrunjOtSwtc6kNXGqGaqqrcPQU79NyVuxcuFkRASWY,1910
spacy/lang/tr/tokenizer_exceptions.py,sha256=uEkqJxzj7_m8LIN7W9RXPKHSLe2uCdn_5_4LY6PjVRc,6283
spacy/lang/tt/__init__.py,sha256=Up_oS9mhQ2sUc8YehPdu1li5hO8Q2QjZVX-1PuuOfY4,503
spacy/lang/tt/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/tt/__pycache__/examples.cpython-311.pyc,,
spacy/lang/tt/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/tt/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/tt/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/tt/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/tt/examples.py,sha256=1pvKNtPIFow7_mayaj4cHqtyP5EWbVb_y13YTA21DxI,913
spacy/lang/tt/lex_attrs.py,sha256=48lx_3n_hdRppHJXtzO0h7OBkBgVTSkY-r6Q-aLR8ok,1175
spacy/lang/tt/punctuation.py,sha256=relrzTTMJQ2VZ0gOaxF2V_Yqo2-2IyvjPIlSnMzzkRU,819
spacy/lang/tt/stop_words.py,sha256=8ORDfQ8g48stMfO-qGlL3DtBCYZIaZqMLTq17P5ElgI,18740
spacy/lang/tt/tokenizer_exceptions.py,sha256=YO6vYHAE0pMsqTWRxUAyKK0DW8znHZTspvWdbWL26Xk,1809
spacy/lang/uk/__init__.py,sha256=74Atdi1RBmNFLR28dy5tSjt7uKwNipWraHJkunlQ2ZY,1383
spacy/lang/uk/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/uk/__pycache__/examples.cpython-311.pyc,,
spacy/lang/uk/__pycache__/lemmatizer.cpython-311.pyc,,
spacy/lang/uk/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/uk/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/uk/__pycache__/tokenizer_exceptions.cpython-311.pyc,,
spacy/lang/uk/examples.py,sha256=dc47hrk1fs2_alTzyxzaOVCV4fCEChmHTH29spcg6KY,1817
spacy/lang/uk/lemmatizer.py,sha256=8fGLInSsBPWWuNeRZ9NiRRIKyYoJ5xQcLQEKCiukzG8,1761
spacy/lang/uk/lex_attrs.py,sha256=zsyQYi3usR5wtpF41c5NLHD0SaP5oJrIwNXy9LYhotM,1645
spacy/lang/uk/stop_words.py,sha256=U7hOjkC10YeEL3osa5W9z6RbfFAeVy2XWKJh9Qsa4vA,5356
spacy/lang/uk/tokenizer_exceptions.py,sha256=lYxpd1sVxanfTOFDq8X9-04Dtvpe6W4Xkhaf9CwNL_I,1426
spacy/lang/ur/__init__.py,sha256=oB1QCtO-k0ma6lu0E1eJkZ9_jbXi-RsX9CUNcPu15K4,480
spacy/lang/ur/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/ur/__pycache__/examples.cpython-311.pyc,,
spacy/lang/ur/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/ur/__pycache__/punctuation.cpython-311.pyc,,
spacy/lang/ur/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/ur/examples.py,sha256=yoJDYpxNh8DbFmO6dDIz-Hv5gCca3mdeESzhtRNLrr4,315
spacy/lang/ur/lex_attrs.py,sha256=MI4PQL6PG3DlouJQBVXB0i28lzd7coZhBUTY9gU-VEg,2282
spacy/lang/ur/punctuation.py,sha256=QGpDXn2GI0giTe5paUEw9bXqL5fF89De6gBgTfWm_To,82
spacy/lang/ur/stop_words.py,sha256=pf7hT7MY-5R_ZzN1hgDwqU08nKPfQa47Ia6asVsPMAU,5235
spacy/lang/vi/__init__.py,sha256=8A6Wk9aBl0p15N4v6W9WdDaNFQh_i2XPGQsrab0xagw,5743
spacy/lang/vi/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/vi/__pycache__/examples.cpython-311.pyc,,
spacy/lang/vi/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/vi/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/vi/examples.py,sha256=XE7nnoOZhfP6QhUOSVRdkf5RzsbCL5RNMeTaEC6rthQ,786
spacy/lang/vi/lex_attrs.py,sha256=yabOBcapfqPOa60BpRKzORympndep_j1JYcw4_Ds24c,1443
spacy/lang/vi/stop_words.py,sha256=QgL_H7UlMX8MnHrg4nmo80_Z21gx4Cl1P8cuhFs7N3E,22544
spacy/lang/xx/__init__.py,sha256=V2Ww_qAgVKPIn-zlyj-HY7piYNCOM5cShDFGAXoe1Sg,278
spacy/lang/xx/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/xx/__pycache__/examples.cpython-311.pyc,,
spacy/lang/xx/examples.py,sha256=n3SJglw4nCNj-lmqNcbtxAxL8lSXzBuTOZpDLIi2dRU,8256
spacy/lang/yo/__init__.py,sha256=qZu-npgnuk7RVWbMZ0eVZMM2l4xoepLxbZmQTJSalz4,325
spacy/lang/yo/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/yo/__pycache__/examples.cpython-311.pyc,,
spacy/lang/yo/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/yo/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/yo/examples.py,sha256=Vm6fSr-ZQHA_IO2vdiFZ6s-QYK6Q2AH_m5ufYGfudSw,1291
spacy/lang/yo/lex_attrs.py,sha256=2KG9hzHCDCWzHUuULCKN3CBQcbFqPun2gjqgDv9dNr4,2635
spacy/lang/yo/stop_words.py,sha256=c81W0td_RiCY231omW4l4wHsXhDYSlNE19BdTMkRl8I,617
spacy/lang/zh/__init__.py,sha256=ldd-oKuTsF3JzWWYxtxt-CfHlaLad4HN2V5b4o5PoRg,13075
spacy/lang/zh/__pycache__/__init__.cpython-311.pyc,,
spacy/lang/zh/__pycache__/examples.cpython-311.pyc,,
spacy/lang/zh/__pycache__/lex_attrs.cpython-311.pyc,,
spacy/lang/zh/__pycache__/stop_words.cpython-311.pyc,,
spacy/lang/zh/examples.py,sha256=nWohZaaGZ6IP2PNCi2bI48gzkdBMfQJmY3animjlLp0,808
spacy/lang/zh/lex_attrs.py,sha256=AJWMY_b2VqCzrH9R89lJZ4pOsG-Fyo8vJBXmtTwk9Pc,1711
spacy/lang/zh/stop_words.py,sha256=hjVdk_pF5Xg3jiy9talatR0ofMhk_uATcGO_uWeQ064,15308
spacy/language.py,sha256=2Qoy_k48-AUZ6KMRHoAfKRxS91gNoC44LTBfGSA6a7s,103832
spacy/lexeme.cp311-win_amd64.pyd,sha256=othzofeaqics0cgusttlLW1-UOfVYvpD_mWtsyQxOoQ,163328
spacy/lexeme.cpp,sha256=ey7rnTy7GKqW9rIe2BoiWOphv3iDwUErwNi7Ws5SxOA,1288111
spacy/lexeme.pxd,sha256=mdcWVU-v75fEcL2fxhpobwvRyambUFEyRx6GcdTMJYo,2710
spacy/lexeme.pyi,sha256=_IZoejsaSAq1UggJG9Y-fTgE89UP8DMmwA655W7TywQ,1508
spacy/lexeme.pyx,sha256=hw-eGp8BzRWMGTQ0LAXCl5kjPTZHV6Hs6rxx-2frY8s,17353
spacy/lookups.py,sha256=kmdToXW5YoX9dSqDXJ37k6hmz0ppxSo0a1_Z0TU9vLo,11165
spacy/matcher/__init__.py,sha256=5gBe1LQFzTGPgEse1RMPP9TOrY-uR_obL30VjIwwwfo,238
spacy/matcher/__pycache__/__init__.cpython-311.pyc,,
spacy/matcher/dependencymatcher.cp311-win_amd64.pyd,sha256=NEiNzYUybGT9pi7qxKxvl8FOtVX4oTWZZgVVxRPE9ao,277504
spacy/matcher/dependencymatcher.cpp,sha256=bZ1OwmRovsAeJgD2vG8JBWHyNDTMTLvPgdCAaw0hyLE,1661169
spacy/matcher/dependencymatcher.pyi,sha256=a15E08_iRYrPxCc8yHTQT1-byATDmJ-7V6BOT1lipVc,2190
spacy/matcher/dependencymatcher.pyx,sha256=rUTxV5I0l4m39om-Dp9wkb_ytQT6dAibQGc84LXPyiQ,18280
spacy/matcher/levenshtein.c,sha256=FYsuzgmGjo1Z-JYz4KobBkH94FrdJzU1TaE078RJNBY,227683
spacy/matcher/levenshtein.cp311-win_amd64.pyd,sha256=fyihsGUWXEhIAx9aVUPWx0lpUU_aoJ059VYkWvRKXYY,43008
spacy/matcher/levenshtein.pyx,sha256=Gnm48kC3yfz1iESpe4zyZ9yqIKkdf2oqaCSiav1b_K0,977
spacy/matcher/matcher.cp311-win_amd64.pyd,sha256=ZQaP_XRNynFQxLwLTfrHfmLqicm_2jvs6Hf1Rw-ktq8,422912
spacy/matcher/matcher.cpp,sha256=khBpX9NEJncch02nsa6e_OgGFLn1mnrY9TQy534co2Y,2518134
spacy/matcher/matcher.pxd,sha256=IguLa882lNhHfqiH3QPhc3H74rrwkSta0DQhVnVCVf4,1610
spacy/matcher/matcher.pyi,sha256=6nTmMeNZxmMvcSXAfYhrOu-gDUY3SY8JqYqIvFmgQ88,1905
spacy/matcher/matcher.pyx,sha256=yj2LQmo42xVoBE9fc8nYG0KXZ9bv3pTVp35XUwfsom0,50710
spacy/matcher/phrasematcher.cp311-win_amd64.pyd,sha256=vtIU237K3rF8LRM2Yl34Ak9xRGcfoOu0-Kz4ts0H6Ek,202240
spacy/matcher/phrasematcher.cpp,sha256=n9XVlONbhJZgHxP5UquXkeVReMERU_c4BZGECUq9OfU,1392189
spacy/matcher/phrasematcher.pxd,sha256=BczEL--qCMBqM8UxG8vucGmEEwldhPnA_CIYhcz1UZ8,576
spacy/matcher/phrasematcher.pyi,sha256=ejadx-AgbMyc_tVc6QS3vtciSK9o4vWnBJiQyFKXN8c,1075
spacy/matcher/phrasematcher.pyx,sha256=b0MTUfBkA811QCmFHWhJnhEWAYprQUcZiAzEwu5o7Ow,14719
spacy/matcher/polyleven.c,sha256=2Qn5qoQ4s7MopAg4UUKq-gQNL8RVse1Y1Ex3rxqmoSk,9955
spacy/ml/__init__.py,sha256=NJlyIGLT19zxcigWJTCYjLOMvlVofpkyZbeNjVZ75r4,111
spacy/ml/__pycache__/__init__.cpython-311.pyc,,
spacy/ml/__pycache__/_character_embed.cpython-311.pyc,,
spacy/ml/__pycache__/_precomputable_affine.cpython-311.pyc,,
spacy/ml/__pycache__/callbacks.cpython-311.pyc,,
spacy/ml/__pycache__/extract_ngrams.cpython-311.pyc,,
spacy/ml/__pycache__/extract_spans.cpython-311.pyc,,
spacy/ml/__pycache__/featureextractor.cpython-311.pyc,,
spacy/ml/__pycache__/staticvectors.cpython-311.pyc,,
spacy/ml/__pycache__/tb_framework.cpython-311.pyc,,
spacy/ml/_character_embed.py,sha256=GftOTC7-iKzntuSEmf4ZI4PnCvsBGWoWotkgo1LT6gY,2052
spacy/ml/_precomputable_affine.py,sha256=VvzK4paUqDEjrY6YNl2nwv3xDZnVmAPIH3KtWysW8G0,5998
spacy/ml/callbacks.py,sha256=BtZMKHfcdjy2L8LQ5FjZuPMEi4vaoVWZZ84BfUhGels,3911
spacy/ml/extract_ngrams.py,sha256=igvhdljn1mNij_naojaXosmyGR7bs4qXhJ_9apPSjec,1225
spacy/ml/extract_spans.py,sha256=7ucSxZv0r4KcgnfME_rElfRWWBh8HDpIgENUS9ZMBAY,2369
spacy/ml/featureextractor.py,sha256=JpuccW4Emglrz12RwAw_yXlA-5h7-0msMyyMAmpu1xE,997
spacy/ml/models/__init__.py,sha256=QGOnh4yO6bmBtlflT-L3I1_pH6xtouXl284btaYfS_0,231
spacy/ml/models/__pycache__/__init__.cpython-311.pyc,,
spacy/ml/models/__pycache__/entity_linker.cpython-311.pyc,,
spacy/ml/models/__pycache__/multi_task.cpython-311.pyc,,
spacy/ml/models/__pycache__/parser.cpython-311.pyc,,
spacy/ml/models/__pycache__/spancat.cpython-311.pyc,,
spacy/ml/models/__pycache__/tagger.cpython-311.pyc,,
spacy/ml/models/__pycache__/textcat.cpython-311.pyc,,
spacy/ml/models/__pycache__/tok2vec.cpython-311.pyc,,
spacy/ml/models/entity_linker.py,sha256=Dqqzxp-E70NbnqSn0ZoYz6QghjMxOkCyt690mUJhAXw,4381
spacy/ml/models/multi_task.py,sha256=pj04UBzftU-S6PBC7Am4d2eD780LEnrhkG2Se_gjmOU,9521
spacy/ml/models/parser.py,sha256=YVBYlxPRhyKM96wbyMroaIglbR4wFim1M6gjM6xUUJA,7070
spacy/ml/models/spancat.py,sha256=WgdPClsvPAudOKa_rh9esc2u_Rl-t6n6IcLwqv31KJo,2434
spacy/ml/models/tagger.py,sha256=0vnJJaTXF2Xy9CPyEsilJKKHKeOhP2PcEPbKeMlnkxs,1282
spacy/ml/models/textcat.py,sha256=SsvneLE6Nx9O7SUC7hgmVnORfskl0bmvVUWoDB6-5cQ,6923
spacy/ml/models/tok2vec.py,sha256=BTFTnnK69E7oz2E4pniM0LJF08jcG4ttJGtGwPZrpgk,14358
spacy/ml/parser_model.cp311-win_amd64.pyd,sha256=TCujZn6xrjGgdaIofqKEeFcj138GNmoXhH-3mcjPfVs,223744
spacy/ml/parser_model.cpp,sha256=rhuguG7yHN4XszDkxXe5rDyXv1vhpLv5m_Wj5eOYN3c,1767821
spacy/ml/parser_model.pxd,sha256=_MiytjbstlH-Si0daKXZX4CnilIxmpJtJyvRPuRmvIQ,1215
spacy/ml/parser_model.pyx,sha256=y0zlRNF3tD6FD_T9WUWG_yq9xbwuI1hPcjgNj2FZY1k,18673
spacy/ml/staticvectors.py,sha256=5ATtlLIkhSGJa0vbT_9ii28WfatxzfP7FjX3CdUrcS4,3968
spacy/ml/tb_framework.py,sha256=Ee-3x54Xjxw5kcNXyziGKaT-OLRnefTsX926_22DEZ4,1514
spacy/morphology.cp311-win_amd64.pyd,sha256=vP1cmqw7cpRt-A51oZSDSaDKdu_AdRn80Y9QdSgf7ac,80896
spacy/morphology.cpp,sha256=h-lnt8KhQlJAvfJ_G_KWmbX-lkXmkbtXg1ypDRYjeKI,543964
spacy/morphology.pxd,sha256=SdmOG37jo4DKl8Qktl1qeA0WHdM1h1lKCG4Ky8LqVfo,831
spacy/morphology.pyx,sha256=fQPEcWLt4x6Ual-KlO22D8WtY6iLUwo3NiKkDnCerZ4,8181
spacy/parts_of_speech.cp311-win_amd64.pyd,sha256=Mv4drq4XskVCh1PLLh9bP99rE3WO99GuNmIfFKr0uS8,67072
spacy/parts_of_speech.cpp,sha256=GVYOFFJYlRIIMrd4qP8T1dttrtggs9BVQRyXIgcaCu4,398810
spacy/parts_of_speech.pxd,sha256=_7vDWNxL6okeFopSoKiUL04nz6sBGBE_VT6Lt190n5Q,280
spacy/parts_of_speech.pyx,sha256=tQBQVbrHmkv_V3OQ_jU-22XLPmET2bFBcV8Kw96seuU,461
spacy/pipe_analysis.py,sha256=p2Uc2Vz6b77IIe5VbCD2Gq--P3Xz-yABlzwOjAGb6CI,6378
spacy/pipeline/__init__.py,sha256=Tj49WpBEiOc9SmlU-bZY1yZY0CJkO8Z8wyAalHhzrNc,1241
spacy/pipeline/__pycache__/__init__.cpython-311.pyc,,
spacy/pipeline/__pycache__/attributeruler.cpython-311.pyc,,
spacy/pipeline/__pycache__/edit_tree_lemmatizer.cpython-311.pyc,,
spacy/pipeline/__pycache__/entity_linker.cpython-311.pyc,,
spacy/pipeline/__pycache__/entityruler.cpython-311.pyc,,
spacy/pipeline/__pycache__/functions.cpython-311.pyc,,
spacy/pipeline/__pycache__/lemmatizer.cpython-311.pyc,,
spacy/pipeline/__pycache__/span_ruler.cpython-311.pyc,,
spacy/pipeline/__pycache__/spancat.cpython-311.pyc,,
spacy/pipeline/__pycache__/textcat.cpython-311.pyc,,
spacy/pipeline/__pycache__/textcat_multilabel.cpython-311.pyc,,
spacy/pipeline/__pycache__/tok2vec.cpython-311.pyc,,
spacy/pipeline/_edit_tree_internals/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/pipeline/_edit_tree_internals/__pycache__/__init__.cpython-311.pyc,,
spacy/pipeline/_edit_tree_internals/__pycache__/schemas.cpython-311.pyc,,
spacy/pipeline/_edit_tree_internals/edit_trees.cp311-win_amd64.pyd,sha256=n6e-lsn_vqwc2mTxdHKnLQk86uG6QU88F7eLeMAwSk4,112128
spacy/pipeline/_edit_tree_internals/edit_trees.cpp,sha256=aBDWXcrSMlUbWdvRJ7v6gHGciicGp6Vq6ZIusY9mHZw,662571
spacy/pipeline/_edit_tree_internals/edit_trees.pxd,sha256=P0kkaTWQ19UfrMTWneQchJwTXxdryUMtTsQJyX7gATY,3542
spacy/pipeline/_edit_tree_internals/edit_trees.pyx,sha256=bKoXaCi2zTkJVmm0fLvODBtyeeI6c5otdddui6M4OiY,11052
spacy/pipeline/_edit_tree_internals/schemas.py,sha256=fWSWqnXS0zBVQqtGeA3AYtsRjCnW1vHKz8CCtmzs07A,1518
spacy/pipeline/_parser_internals/__init__.pxd,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/pipeline/_parser_internals/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/pipeline/_parser_internals/__pycache__/__init__.cpython-311.pyc,,
spacy/pipeline/_parser_internals/_beam_utils.cp311-win_amd64.pyd,sha256=pEOgle1qiETAFgJOGJOGjF6K7K3d9fEST-Q3B8e4RhY,231424
spacy/pipeline/_parser_internals/_beam_utils.cpp,sha256=kx0RkXwD-wzYPzosLjofWUqVCWtC-bEznvJxXay1LCM,1640493
spacy/pipeline/_parser_internals/_beam_utils.pxd,sha256=iFXt-_yjdJIdFMp79xvEMjFrclqx_LvLSat4w6M1-A4,260
spacy/pipeline/_parser_internals/_beam_utils.pyx,sha256=pGrZeF7EZtztAUjh_5P6HMh2NTN5uhhcnxMjKNoy6bI,11947
spacy/pipeline/_parser_internals/_state.cp311-win_amd64.pyd,sha256=-xHmT3Xhy7ByjvpJyoqZTmOqMBzy-9I1iDyoQbZrO6w,23040
spacy/pipeline/_parser_internals/_state.cpp,sha256=ZALuDEGjbgX1LWClc-4AQqMx3LXoAmS-lSWqZYyaYMQ,433222
spacy/pipeline/_parser_internals/_state.pxd,sha256=NdpWGKvlhQf9PQExAMPWdJktd7hDzWWYJpGYKbBl_nY,12737
spacy/pipeline/_parser_internals/_state.pyx,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/pipeline/_parser_internals/arc_eager.cp311-win_amd64.pyd,sha256=_tM88k-1gwrZV1oJt09U4t8ePhkqnAoFw_EdcwdmNGA,320000
spacy/pipeline/_parser_internals/arc_eager.cpp,sha256=5-BmIz7q--EnLiDUOhkrmc3FFKt5vogf8YzSlhUoOso,2186546
spacy/pipeline/_parser_internals/arc_eager.pxd,sha256=agdTzaDEPPv3gYnirQURZ5KJZwOZdojz6bPd-IfbYpA,218
spacy/pipeline/_parser_internals/arc_eager.pyx,sha256=c6E-6EyeX-ffJakqVP_q-PCaFevn4zl-OSBe4-fghtk,31313
spacy/pipeline/_parser_internals/ner.cp311-win_amd64.pyd,sha256=mP2gD0lCV9SJE2YMgXoxR9EwAbO--2QiGfDlOJuFTGg,206336
spacy/pipeline/_parser_internals/ner.cpp,sha256=E1ZEWkLHXTYcDzJhaw-3h1jUVNTOya8EdLAER5ckP4U,1787577
spacy/pipeline/_parser_internals/ner.pxd,sha256=t4C1A08YZB6G8pg7ewex8XCdqfGgRP51-ML4PwHi4X8,109
spacy/pipeline/_parser_internals/ner.pyx,sha256=wrXUu3w8uSUK3XmMdSlA0cAO3mYrVsQmxJL4ivBIa2A,24037
spacy/pipeline/_parser_internals/nonproj.cp311-win_amd64.pyd,sha256=csm3TRrRcnz7fbMc5yomtoEGaNwzCeNI1AT2To8wqpQ,192512
spacy/pipeline/_parser_internals/nonproj.cpp,sha256=KamLeOpvbWH9rHmPXvI7sfmlalOOwqgWoSqDOefnIbc,1215243
spacy/pipeline/_parser_internals/nonproj.hh,sha256=O2QsGbVxKQ8Y9GBho6javj3wsSCV8NRT2ND0eHOssxA,198
spacy/pipeline/_parser_internals/nonproj.pxd,sha256=iYQck7bNSYo0sHjykW46g9OQCrIAxLofBeKAxkL7J8s,137
spacy/pipeline/_parser_internals/nonproj.pyx,sha256=NNTRRfQgPF1M5UQEDxUQKDC_ckoa9c7pnuUc-4li1C0,8856
spacy/pipeline/_parser_internals/stateclass.cp311-win_amd64.pyd,sha256=d9Ic9kGbrbmfZ7ntC_ud-VmOoEsA9uPqHt91OR-_SBE,167936
spacy/pipeline/_parser_internals/stateclass.cpp,sha256=--ziF_wTd7lNY1L0xQquOOV16KXym89N6IB8bmzRb7E,1304419
spacy/pipeline/_parser_internals/stateclass.pxd,sha256=ojvOQlbOMU4k7IUeI0842NM66PHa0dtFwh3ccuSZI3s,773
spacy/pipeline/_parser_internals/stateclass.pyx,sha256=Rlt3lqXxYpqz_rEAnbsnGL_Xxphm4yC5c5lwPpQxqC0,4800
spacy/pipeline/_parser_internals/transition_system.cp311-win_amd64.pyd,sha256=s3XvyEV365P77HFplWh16sDLaEQ_YYjnAPKqgPGgZlo,198144
spacy/pipeline/_parser_internals/transition_system.cpp,sha256=bFEkJeK8NQHhF1LEc3rDrZSsg9mDGK4CMdBzHGYWSlQ,1463545
spacy/pipeline/_parser_internals/transition_system.pxd,sha256=HFLBvVw7gbQlxuLcUKjzKAMOtH1W0GmGP2sizoZZ3T0,1779
spacy/pipeline/_parser_internals/transition_system.pyx,sha256=6-M3wBxrTW2SdGKaulEXsSvrEeQrtxOurIGJ3bkxRR0,9487
spacy/pipeline/attributeruler.py,sha256=mI0xlhyWuQ_awTxOg9PiGSw777TWhtYy5wmBa8OzSHI,14051
spacy/pipeline/dep_parser.cp311-win_amd64.pyd,sha256=lSQzhFgLhE3jdP0HvvBG6_FE-WK1Y1Yh7Fb4TCure9A,202240
spacy/pipeline/dep_parser.cpp,sha256=-odkhd27VxbYSJ4hhB7qUymhKcBVDA8B7sApz_SmdKA,1467792
spacy/pipeline/dep_parser.pyx,sha256=So76tlMeseOOMmedeDm3j9JUPHBXPyLQ7eXazF0AF9E,13442
spacy/pipeline/edit_tree_lemmatizer.py,sha256=gknAA4A_WP1jP0a_u8QlRXWt8RF3iXT2WVfCvYunirE,15773
spacy/pipeline/entity_linker.py,sha256=TfOotzoaBLB223c8uH750c06lTnZxNYXhRp4_N3D6LI,28095
spacy/pipeline/entityruler.py,sha256=bvirauTn7DhRjgD0ppA00gI6vSUqhizW_gf3z1Jr38w,21612
spacy/pipeline/functions.py,sha256=R7_efpVvFyFOPwJ-RoWZrfYF8kT8jgIN8tAvmtew6ys,6905
spacy/pipeline/legacy/__init__.py,sha256=1j5edMXlnmpKU_OkEj4p6Nt2SP5vKbRprVWX2L3g-p8,77
spacy/pipeline/legacy/__pycache__/__init__.cpython-311.pyc,,
spacy/pipeline/legacy/__pycache__/entity_linker.cpython-311.pyc,,
spacy/pipeline/legacy/entity_linker.py,sha256=X1bcePMc-tJhiSsLoGsxr1mwamNG4np7_HAzHjrmrKQ,19230
spacy/pipeline/lemmatizer.py,sha256=PnYPC21IoC4_t741CxGd_YQqCLX7PvUq9keuaQTm9KE,12514
spacy/pipeline/morphologizer.cp311-win_amd64.pyd,sha256=DYmORFlWWHSFNvbOJOS2YlU-boYTjGTEBMf6y7zCtN4,214016
spacy/pipeline/morphologizer.cpp,sha256=q-nJM0Re5oFvh5JxYfF5vUbVz_jHbLDKRI-k_NUf1KI,1315560
spacy/pipeline/morphologizer.pyx,sha256=CIPj4TubJazXVXEhoh1dORkOhx9muxoP0pIvRRFu6-E,12751
spacy/pipeline/multitask.cp311-win_amd64.pyd,sha256=k3xht2wzqwKrL4tLw5l3tqALyqQBQihV6qrsK8Us45w,208896
spacy/pipeline/multitask.cpp,sha256=O36t-rYnWnbzWtoyQZQW_ZjQFsS9xQ7aI3sOXr0qZb0,1293900
spacy/pipeline/multitask.pyx,sha256=kuCwVBIS3STcxT4Cp22jh02g2dHrJX7FTe7OJZc3fZg,7800
spacy/pipeline/ner.cp311-win_amd64.pyd,sha256=i_C70Cbh7UJ-bdKQXRKfPIUPe7J5-e0p6I8d4lunhqU,200192
spacy/pipeline/ner.cpp,sha256=zvsJ-3enu3t4nfbMQ5tp5Hapjl3VwWxzSerCmwdmeds,1448084
spacy/pipeline/ner.pyx,sha256=Kf0aw_3gynRhPXcUzE99_9jWielZCert_nnILqZokDo,10324
spacy/pipeline/pipe.cp311-win_amd64.pyd,sha256=-TmdDit4RhboJJ6rH9g9ZJsHuCNFP0oLu2s4N1m2AgI,181248
spacy/pipeline/pipe.cpp,sha256=-vAlofmIXDmO7xK7ZWLfEOeeJOsH_T3hCGZPlhw92po,1168665
spacy/pipeline/pipe.pxd,sha256=mQBwT5sVlL33DiQa9dMiw5C95c_DHpQhsZsXLPRizug,44
spacy/pipeline/pipe.pyi,sha256=-M5my7z3yqp4QnQks64j6FOurkgyHTTFfjosgL-HhGo,1264
spacy/pipeline/pipe.pyx,sha256=SL234N51lxYpPmIMOCcwF_tGwxmucmLxlMpMXgbSkSI,5776
spacy/pipeline/sentencizer.cp311-win_amd64.pyd,sha256=m27CiJy2-dgbxY9gQCayrYnEjjExV1eR9hlPOQHYivQ,197120
spacy/pipeline/sentencizer.cpp,sha256=kWDvsfeaoI2CyjcbkPMebpYB7fweemBIBFxfju6yLL4,1231108
spacy/pipeline/sentencizer.pyx,sha256=sdlKUgucF71d53xQA6qlZQnzCb9vP3g00R2eoRHBmDM,7018
spacy/pipeline/senter.cp311-win_amd64.pyd,sha256=jEqHg-cmquUHHmmSuygE9MDxNdzzoUZ8kKMK_08o1I4,183296
spacy/pipeline/senter.cpp,sha256=lCzxxTkYaZQ3IQeUf-x8JGyrWSS3vkhWgxvFFgZQ0Qw,1162897
spacy/pipeline/senter.pyx,sha256=xZ5vAyRqjF7Mmy4W0ezNFoqWMCUjH6yAxxoTvxENI74,6713
spacy/pipeline/span_ruler.py,sha256=7Bs57vVtU91RackoZLXTRMMwEJslClSl3cx1ga1RQmI,22107
spacy/pipeline/spancat.py,sha256=-OETRytiKoBfPFkz8kIAyh0yfShxwpwl8fckwG69ROw,29687
spacy/pipeline/tagger.cp311-win_amd64.pyd,sha256=7mNh93JUYHAe34ysK8AtU3c0Kc7ETtI07aZKlGTTznE,235520
spacy/pipeline/tagger.cpp,sha256=A6BeAnRJGVYba1oHetVmtQCordso6AnbdUNImVG3jr0,1409472
spacy/pipeline/tagger.pyx,sha256=YSHN4biZW_kWhQo3ND0w7CzExBDkCsjiauoBIKY1Nvg,12534
spacy/pipeline/textcat.py,sha256=T70BgEIePlFfFLTmOQc34UJ3AY_BYgYUX8I-KBu7agc,15351
spacy/pipeline/textcat_multilabel.py,sha256=eeU-OEE13JUY7OqOiw9YwfEXFC96ci9zM391wzsm7dM,6816
spacy/pipeline/tok2vec.py,sha256=iy2FyhQeIh7eaLntuOytfnsAxYBPzyZHjihvK0RIMV4,13710
spacy/pipeline/trainable_pipe.cp311-win_amd64.pyd,sha256=q_A7ib4whQJJbWq7UgLgKBb88lKY-H8WzpE-x_JUdb8,259072
spacy/pipeline/trainable_pipe.cpp,sha256=BZ7pOifVN6_pSuzq5bMvn0WPyJSORdONotvi9X7MDSw,1553233
spacy/pipeline/trainable_pipe.pxd,sha256=qSEsbryFZrGixK5iKzhcJ59D8iRUfVTvSdWnfFDSS1E,206
spacy/pipeline/trainable_pipe.pyx,sha256=DS7SrHPAShO0VQYVCH04B418fiZPJBWkwIelThq_xFA,14385
spacy/pipeline/transition_parser.cp311-win_amd64.pyd,sha256=ZSdEr12c9xHYihn8Fmw3vRcqpWQLVLf1356cFCusRjc,324608
spacy/pipeline/transition_parser.cpp,sha256=CX3BxYDfnv4RFuxu9ylXB_WRbfzSOTeyC3v03BxYNBA,2205786
spacy/pipeline/transition_parser.pxd,sha256=evis_Onr7C4JLhX_rX1YnCBFmj44qr2ivczcGaCbYtg,735
spacy/pipeline/transition_parser.pyx,sha256=8gLyzfwGM9mmXfOdFHo9eCgtnPj8X07TLqJCXNBbVYQ,28224
spacy/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/schemas.py,sha256=cYhOx0IPTlVTgQHhWn1yoUhmqrwVPeVf4Mth3umsEZA,24445
spacy/scorer.py,sha256=h7eniJFxAR8J0fBAuNjRKjM8PZS98C0gqvWA2XrM5_o,42015
spacy/strings.cp311-win_amd64.pyd,sha256=ARxXgtryy-zESql6J0WeLROf1n9xfkkGfnFL9OOx1zM,78336
spacy/strings.cpp,sha256=zbVr52-NgQC_pcb92LN_rt3bp2-yoHEXrnCh68ckLzk,434308
spacy/strings.pxd,sha256=CTZ2T1L3Qrj5yN7hGMIvwYlkkcYEb_efgEAn7W-3c_4,756
spacy/strings.pyi,sha256=17iW2y5USZZr-lrzRz3gqWHCq29gRZoEtz5lWST00P0,1123
spacy/strings.pyx,sha256=ubbsce_4vhltjIq6xdO4GJEIVDhy79sXwYOI_MBx6UM,10938
spacy/structs.pxd,sha256=KWYT-RxChBGwPP4zh3kGGzPUMXePEs3ccAJTb8B28PM,2648
spacy/symbols.cp311-win_amd64.pyd,sha256=VM_B3lSD_FceKKrHv27T6vc5_37vh2WR8D0nYdzyi5g,85504
spacy/symbols.cpp,sha256=P5VzZYqMVf6sZ2V8O9CvRANkFfVztsnT-dPelAuTxu8,499660
spacy/symbols.pxd,sha256=ecUhRezuCbeTCMBoMLVl1UoohSu4TWBX60WpYWRhwKg,7408
spacy/symbols.pyx,sha256=9AazhP5rSOGriDmui8uSOeinlxnVJxNEraDv3HWZB_8,14656
spacy/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/__pycache__/conftest.cpython-311.pyc,,
spacy/tests/__pycache__/enable_gpu.cpython-311.pyc,,
spacy/tests/__pycache__/test_architectures.cpython-311.pyc,,
spacy/tests/__pycache__/test_cli.cpython-311.pyc,,
spacy/tests/__pycache__/test_cli_app.cpython-311.pyc,,
spacy/tests/__pycache__/test_displacy.cpython-311.pyc,,
spacy/tests/__pycache__/test_errors.cpython-311.pyc,,
spacy/tests/__pycache__/test_language.cpython-311.pyc,,
spacy/tests/__pycache__/test_misc.cpython-311.pyc,,
spacy/tests/__pycache__/test_models.cpython-311.pyc,,
spacy/tests/__pycache__/test_pickles.cpython-311.pyc,,
spacy/tests/__pycache__/test_scorer.cpython-311.pyc,,
spacy/tests/__pycache__/test_ty.cpython-311.pyc,,
spacy/tests/__pycache__/util.cpython-311.pyc,,
spacy/tests/conftest.py,sha256=TqyBkYdDTCq7CQgid4iO8-fTgyEW3lOlqhZn1fZnzd0,12012
spacy/tests/doc/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/doc/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/doc/__pycache__/test_add_entities.cpython-311.pyc,,
spacy/tests/doc/__pycache__/test_array.cpython-311.pyc,,
spacy/tests/doc/__pycache__/test_creation.cpython-311.pyc,,
spacy/tests/doc/__pycache__/test_doc_api.cpython-311.pyc,,
spacy/tests/doc/__pycache__/test_graph.cpython-311.pyc,,
spacy/tests/doc/__pycache__/test_json_doc_conversion.cpython-311.pyc,,
spacy/tests/doc/__pycache__/test_morphanalysis.cpython-311.pyc,,
spacy/tests/doc/__pycache__/test_pickle_doc.cpython-311.pyc,,
spacy/tests/doc/__pycache__/test_retokenize_merge.cpython-311.pyc,,
spacy/tests/doc/__pycache__/test_retokenize_split.cpython-311.pyc,,
spacy/tests/doc/__pycache__/test_span.cpython-311.pyc,,
spacy/tests/doc/__pycache__/test_span_group.cpython-311.pyc,,
spacy/tests/doc/__pycache__/test_token_api.cpython-311.pyc,,
spacy/tests/doc/__pycache__/test_underscore.cpython-311.pyc,,
spacy/tests/doc/test_add_entities.py,sha256=N6zANYs9_Y0eMxShr1Fl6Op3mS10u5Zlw7qYA_3IuOU,1859
spacy/tests/doc/test_array.py,sha256=Paz4t_Ule6GvFoo9yDCDLj_MVQTh2jbM98arE6-Eyr0,5124
spacy/tests/doc/test_creation.py,sha256=fP2Pr51zXtgv-cuJwEZBfRgQtkL_33BttQsSGG0Titk,2729
spacy/tests/doc/test_doc_api.py,sha256=DVg7QdYr7DrzjQ3lflfvl9iq-qKLqpnGWpmprswl_ow,36895
spacy/tests/doc/test_graph.py,sha256=dz8_1vAVTbciuLaNET_aMUCXL2rwrhxuIow6nWx-cws,1867
spacy/tests/doc/test_json_doc_conversion.py,sha256=1G7vkSsbGyBvg7ByhOx55_iHProSp6yX8GQbahl-Fb8,14039
spacy/tests/doc/test_morphanalysis.py,sha256=K-haVQ8osFVzftsc82IWZZ_Q9a4FQGVncw6Kmp4gBy4,3245
spacy/tests/doc/test_pickle_doc.py,sha256=Ncp9HmkcqCdW4tSiHEQA5tnc19cvfD9UD_QUKgvfDZg,1524
spacy/tests/doc/test_retokenize_merge.py,sha256=bAJ7Xvt7CwIkLHMUjjEom912ih_-Dl3aXwWB-QrJ7pE,19449
spacy/tests/doc/test_retokenize_split.py,sha256=kXlG1wDdiULMymRbftgv2wcHjdkp3_0XFFI8wqUIWQY,11233
spacy/tests/doc/test_span.py,sha256=cYPADCCYoIZELcTZ46i788rMbbug4jrFY6F11JL4nDQ,26155
spacy/tests/doc/test_span_group.py,sha256=8sdoye09k5_MiCOWk0m1joh0DftLxUw87DMh2Mdch9c,7940
spacy/tests/doc/test_token_api.py,sha256=aD5uMHB3tV58fnydFWHFzB06Pvi6nN2mNYSoxg9FDkc,11459
spacy/tests/doc/test_underscore.py,sha256=vXtraE6J0rCKMMK8TUHgGZfW5u6hh8dCVrCCaxkBY24,5737
spacy/tests/enable_gpu.py,sha256=3SmrS2K52tlcV7qrld5UzkTMF2zKfit0ptIU68_D_TE,48
spacy/tests/lang/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/__pycache__/test_attrs.cpython-311.pyc,,
spacy/tests/lang/__pycache__/test_initialize.cpython-311.pyc,,
spacy/tests/lang/__pycache__/test_lemmatizers.cpython-311.pyc,,
spacy/tests/lang/af/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/af/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/af/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/af/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/af/test_text.py,sha256=38UgXyHz3N1gJHxLRo19WsXut-pp_Uh8NNBi_Hg0PTY,953
spacy/tests/lang/af/test_tokenizer.py,sha256=WJZHJv4D4V3pIyBSm7I7TItz4Vaq1x2WiGUOLEWPHg8,739
spacy/tests/lang/am/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/am/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/am/__pycache__/test_exception.cpython-311.pyc,,
spacy/tests/lang/am/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/am/test_exception.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/am/test_text.py,sha256=80u6QPuD3XDcfKU_IbjyWZkaslunfLrIr5-YwZhgboU,1888
spacy/tests/lang/ar/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ar/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/ar/__pycache__/test_exceptions.cpython-311.pyc,,
spacy/tests/lang/ar/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/ar/test_exceptions.py,sha256=hM9owHEhXVNhksqQ4b81H4d6r6ljmeCbFJewJAKvOYM,641
spacy/tests/lang/ar/test_text.py,sha256=4yvJ035BgYB9cUzEoGpJIPuhOiGQ01ChtPjDuhB2xQ4,830
spacy/tests/lang/bn/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/bn/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/bn/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/bn/test_tokenizer.py,sha256=SWj41ssZ3Mc24h_2AIFg8waDNmA5Ekwvi4wD6QeDROs,3660
spacy/tests/lang/ca/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ca/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/ca/__pycache__/test_exception.cpython-311.pyc,,
spacy/tests/lang/ca/__pycache__/test_prefix_suffix_infix.cpython-311.pyc,,
spacy/tests/lang/ca/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/ca/test_exception.py,sha256=yPcIuayC-qGnDPV1hNB54iuE1Tq2dgruMrwZlid7AsQ,649
spacy/tests/lang/ca/test_prefix_suffix_infix.py,sha256=kEY88p34-SDccWCwIaltNb62d5ow9oMVr499mMPrdb0,511
spacy/tests/lang/ca/test_text.py,sha256=DTkFggWMoCLixd5sh0DIMS_RUivXR3eoWCXWW9mREqg,1955
spacy/tests/lang/cs/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/cs/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/cs/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/cs/test_text.py,sha256=4yupiA1KXaRR-3cNxq-3TlxPuxpWDHOxB-TzO5loFXw,533
spacy/tests/lang/da/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/da/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/da/__pycache__/test_exceptions.cpython-311.pyc,,
spacy/tests/lang/da/__pycache__/test_noun_chunks.cpython-311.pyc,,
spacy/tests/lang/da/__pycache__/test_prefix_suffix_infix.cpython-311.pyc,,
spacy/tests/lang/da/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/da/test_exceptions.py,sha256=Q00wmSCcZPU1_obLsayuKp6L4JnvmF3kXxfcan1EgLQ,1883
spacy/tests/lang/da/test_noun_chunks.py,sha256=0O1f9MP1OVHJXuqhsQ8ynbPxrgfghDh0mRGicz3fAVo,2133
spacy/tests/lang/da/test_prefix_suffix_infix.py,sha256=B14Zwf8SuhntKmngQ22zLccJbvZxxc7ELUZ-nlM6OPw,5591
spacy/tests/lang/da/test_text.py,sha256=H4V3YXEdwBFoZGZUTJJCKVrJxyKbLihpayJnDrM83M8,1259
spacy/tests/lang/de/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/de/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/de/__pycache__/test_exceptions.cpython-311.pyc,,
spacy/tests/lang/de/__pycache__/test_noun_chunks.cpython-311.pyc,,
spacy/tests/lang/de/__pycache__/test_parser.cpython-311.pyc,,
spacy/tests/lang/de/__pycache__/test_prefix_suffix_infix.cpython-311.pyc,,
spacy/tests/lang/de/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/de/test_exceptions.py,sha256=vqKBIOfx70X8HMc04h7qxZh98oU4bVfwRqM0FuROtQU,618
spacy/tests/lang/de/test_noun_chunks.py,sha256=r90RAxjpHxdofxTElS9-Byg_xaZTP4A5CcY2aU2U9pM,274
spacy/tests/lang/de/test_parser.py,sha256=RRA-tpkZCo9KIehVJkc5b8KzyGy-DVt886S2Yg73UOI,1216
spacy/tests/lang/de/test_prefix_suffix_infix.py,sha256=RH7-f79GFUniRs10a3KYT_CBdirkSaZmI4WgsPcbS5E,3503
spacy/tests/lang/de/test_text.py,sha256=SYBCcSMS0RnMCXLXbVn2OZqP7Ir6CJC9cen_Zrb0LvU,1550
spacy/tests/lang/dsb/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/dsb/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/dsb/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/dsb/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/dsb/test_text.py,sha256=k-BaAZyAdbKZf_nz22m2xo2FgijTTLC5fu1zCIFJWTs,585
spacy/tests/lang/dsb/test_tokenizer.py,sha256=yU9DVyRl4XR4vcmYwVkM3QBOgPdjwZVZH3MdUTV4knM,778
spacy/tests/lang/el/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/el/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/el/__pycache__/test_exception.cpython-311.pyc,,
spacy/tests/lang/el/__pycache__/test_noun_chunks.cpython-311.pyc,,
spacy/tests/lang/el/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/el/test_exception.py,sha256=P8b5DEK8Nengb7EYOgnq1KOSCxf2IThOzbidrfzOyHY,528
spacy/tests/lang/el/test_noun_chunks.py,sha256=r1RQf-woEbKUlYmtzYYrXz8vwzl9PaAgCVpThtcYi44,314
spacy/tests/lang/el/test_text.py,sha256=MQU2nWLXwzSgn1D2Bij6Nlmq46XTaWWyiM6ZjNJfTdI,1799
spacy/tests/lang/en/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/en/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/en/__pycache__/test_customized_tokenizer.cpython-311.pyc,,
spacy/tests/lang/en/__pycache__/test_exceptions.cpython-311.pyc,,
spacy/tests/lang/en/__pycache__/test_indices.cpython-311.pyc,,
spacy/tests/lang/en/__pycache__/test_noun_chunks.cpython-311.pyc,,
spacy/tests/lang/en/__pycache__/test_parser.cpython-311.pyc,,
spacy/tests/lang/en/__pycache__/test_prefix_suffix_infix.cpython-311.pyc,,
spacy/tests/lang/en/__pycache__/test_punct.cpython-311.pyc,,
spacy/tests/lang/en/__pycache__/test_sbd.cpython-311.pyc,,
spacy/tests/lang/en/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/en/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/en/test_customized_tokenizer.py,sha256=zvWK_FyFLw8Pd9SIA7Wggquu0S2nCG62vt7tArMNWHg,3951
spacy/tests/lang/en/test_exceptions.py,sha256=utGMVXoxZRdaCHgUHg6E9BIK8oW8X_D8-t8K0Nzjdv8,4285
spacy/tests/lang/en/test_indices.py,sha256=hG93NO7QCJT4jjq0i8FNWTXD545CafHWptlrvanxEhg,748
spacy/tests/lang/en/test_noun_chunks.py,sha256=AC0J025I4oToHsh14cWiI-tKyv0TUBJQz2hdPOrbfpA,1592
spacy/tests/lang/en/test_parser.py,sha256=6gSLK3O9Xm7nEEnArkh5KoIcyh6lj3r_F13DzvYxoMg,3020
spacy/tests/lang/en/test_prefix_suffix_infix.py,sha256=n7PEePPBt7sPP0EF-TkZ9PRDE21zGtO5BwGrwDCTBl0,4391
spacy/tests/lang/en/test_punct.py,sha256=mPGvON-ZNsXp3BS7R1hseoKvPwEh8WtKseFVZWwutwU,4549
spacy/tests/lang/en/test_sbd.py,sha256=KMK8GD9Noom_XXIilrQPLvBrd-Bbn8pDOPNOEQRPnZ8,1752
spacy/tests/lang/en/test_text.py,sha256=WhHjGn3vQqbv8UeEorm72gbBamO8tG3qC5utedxU9mc,2031
spacy/tests/lang/en/test_tokenizer.py,sha256=p1YI09mmwTkTQh7CpXM0lwlWzO_15uA6S6rxb_EpxEs,6116
spacy/tests/lang/es/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/es/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/es/__pycache__/test_exception.cpython-311.pyc,,
spacy/tests/lang/es/__pycache__/test_noun_chunks.cpython-311.pyc,,
spacy/tests/lang/es/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/es/test_exception.py,sha256=zW0QYlCHcYqxfSn-JZV6KphkXWvJm2CS_hZSpTqDAS8,571
spacy/tests/lang/es/test_noun_chunks.py,sha256=LIdAQwfSyrGx2IiJUqFtnrmgYc66P60QxMGkaQDXOXQ,10124
spacy/tests/lang/es/test_text.py,sha256=d-bt77xnCAzyBCCd7tr3jJEkRn7ArRloqgAXHcrKxSM,2098
spacy/tests/lang/et/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/et/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/et/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/et/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/et/test_text.py,sha256=CGlPU82c-ks2PvE6uBsx5bcFrN9C-I1HhyRQTyxXUtc,959
spacy/tests/lang/et/test_tokenizer.py,sha256=aSKDcg4Qta1y7eTLwyCRB-KxEFQ2tfp2ovl9JWOP5ME,766
spacy/tests/lang/eu/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/eu/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/eu/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/eu/test_text.py,sha256=DAAJjFeYxB4YYH6UvDWwjZ6CODc_7KFy5MM1caWvbDI,507
spacy/tests/lang/fa/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/fa/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/fa/__pycache__/test_noun_chunks.cpython-311.pyc,,
spacy/tests/lang/fa/test_noun_chunks.py,sha256=zRmA8IcpUnHMRp-PwPKWwgoD00WpLGf1PxjjX0nZu80,305
spacy/tests/lang/fi/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/fi/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/fi/__pycache__/test_noun_chunks.cpython-311.pyc,,
spacy/tests/lang/fi/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/fi/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/fi/test_noun_chunks.py,sha256=me-7Ai9sMSGMJWmH-utQnja4q06MAtDcCNceeg_-4Vg,7447
spacy/tests/lang/fi/test_text.py,sha256=J0M5uI5eTkvbEiMW5CxTjyMkusg5q5pBbNVfD8wO0rc,566
spacy/tests/lang/fi/test_tokenizer.py,sha256=VipXi0CbHGhhm9kmylKq0Z6JGlJ1FqlZY_GQkkh7eus,2985
spacy/tests/lang/fr/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/fr/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/fr/__pycache__/test_exceptions.cpython-311.pyc,,
spacy/tests/lang/fr/__pycache__/test_noun_chunks.cpython-311.pyc,,
spacy/tests/lang/fr/__pycache__/test_prefix_suffix_infix.cpython-311.pyc,,
spacy/tests/lang/fr/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/fr/test_exceptions.py,sha256=h49lZwR0jmcSlHgn8l7JvaBbpOQl17KAQ33IJprCO6U,2301
spacy/tests/lang/fr/test_noun_chunks.py,sha256=_SrfXwPKOWXUU4qd14b5smxiOtdq-jU1wLSkJ8Y2b5w,8583
spacy/tests/lang/fr/test_prefix_suffix_infix.py,sha256=uthjTXC9jWdJtc0cliXlkoZ_24s68ZAmhF8oaLjTvGU,794
spacy/tests/lang/fr/test_text.py,sha256=CpmQdrBH8nEYQEp4L-aVDMQU1FQ1buLH918lfFCi47E,1033
spacy/tests/lang/ga/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ga/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/ga/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/ga/test_tokenizer.py,sha256=hlIwHvnlaj-SOoLcN22ZvALF5DQO6-wOTnluG9RBTlI,701
spacy/tests/lang/grc/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/grc/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/grc/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/grc/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/grc/test_text.py,sha256=yIvgaa4CeGuK7dgD5b6nTBmK5wKblmX4YYh-wl47jNM,566
spacy/tests/lang/grc/test_tokenizer.py,sha256=q0yBEAJqSTcmfA89SPFPlwaMaQ36_TkFl-nuqa3KJeo,1345
spacy/tests/lang/gu/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/gu/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/gu/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/gu/test_text.py,sha256=GLtbOyuRLKwhPeNJCi6L-P8ampU0akZx18CWT_mK5xo,701
spacy/tests/lang/he/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/he/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/he/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/he/test_tokenizer.py,sha256=yJcNd-ikA7iLUy8m7IU_ftKVZ1TqpDIfzUWzwk1vBag,2324
spacy/tests/lang/hi/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/hi/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/hi/__pycache__/test_lex_attrs.cpython-311.pyc,,
spacy/tests/lang/hi/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/hi/test_lex_attrs.py,sha256=0xokvoVRH9zC6LroL_GQY7Pm1RyK5yQ67KjEGdNpXMo,1884
spacy/tests/lang/hi/test_text.py,sha256=lDIp0c--qaKcCWmetK0I61NopRLzi79gDYdYvXuovb0,411
spacy/tests/lang/hr/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/hr/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/hr/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/hr/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/hr/test_text.py,sha256=fpr_xDtu2MUoHf0TWmvZIaIoKNo4ElrkqVd5Y7WQ0lg,980
spacy/tests/lang/hr/test_tokenizer.py,sha256=W_I5gOtbKrAQ9nTlb9zq29nEVPqk9DvjJzGCohBKADs,832
spacy/tests/lang/hsb/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/hsb/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/hsb/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/hsb/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/hsb/test_text.py,sha256=ardRbipilRC6GOwR-Wp1dqrtWp4ZuNp020wHTXWeO1U,591
spacy/tests/lang/hsb/test_tokenizer.py,sha256=GVCgP5hw4A3W5y5OcFYMLRUiyqTHgh_R7jHnZYGkMpE,898
spacy/tests/lang/hu/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/hu/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/hu/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/hu/test_tokenizer.py,sha256=YbGzwpzypmHV4cD6kQjDWfxmN52_r6xC63DSUVRSL6I,14812
spacy/tests/lang/hy/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/hy/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/hy/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/hy/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/hy/test_text.py,sha256=dozCzmWreQbUz4FxdIFq-i1A4VmmItstiyerJmqWFrg,218
spacy/tests/lang/hy/test_tokenizer.py,sha256=-C73-GMmaaKxV-Jity3ZxzMEqjxHn3qaf8ThcZumtkk,1396
spacy/tests/lang/id/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/id/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/id/__pycache__/test_noun_chunks.cpython-311.pyc,,
spacy/tests/lang/id/__pycache__/test_prefix_suffix_infix.cpython-311.pyc,,
spacy/tests/lang/id/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/id/test_noun_chunks.py,sha256=ULACuOkyjRfARyui3trrac6BURIhp64a3Ab1rknvfH4,264
spacy/tests/lang/id/test_prefix_suffix_infix.py,sha256=ICH4G1DhZJ-XqzpqeYF8pQxYVjpwZmFU_kQXk8WT4bw,3603
spacy/tests/lang/id/test_text.py,sha256=iHQp3_VKFv1M2-7dtdflgplsKmZ4y0tFnofMTMeVk3s,213
spacy/tests/lang/is/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/is/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/is/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/is/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/is/test_text.py,sha256=9u0bEr9n_DYB5YcuMhL2qkcPcJnOQZG1pA5J70wp5pA,1006
spacy/tests/lang/is/test_tokenizer.py,sha256=CbAJo1uWexq0dRC7DjoXfUjyp5ADSdVGUeF24DLYgdM,813
spacy/tests/lang/it/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/it/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/it/__pycache__/test_noun_chunks.cpython-311.pyc,,
spacy/tests/lang/it/__pycache__/test_prefix_suffix_infix.cpython-311.pyc,,
spacy/tests/lang/it/__pycache__/test_stopwords.cpython-311.pyc,,
spacy/tests/lang/it/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/it/test_noun_chunks.py,sha256=ST6Sz6qgB0uT_bzPgyXpI59dYuT1OhSMd2-rptU9lDc,8860
spacy/tests/lang/it/test_prefix_suffix_infix.py,sha256=tcjrTqy6cF_G3V_0Qx6iD0Bb_L0Hck3kRtu5nMJ36rA,370
spacy/tests/lang/it/test_stopwords.py,sha256=CtIWKyApYsBQ4MQcQKrNzNqpij2XRfI4yAzIn484jIo,466
spacy/tests/lang/it/test_text.py,sha256=FDHmZjI9RF03-G5lEQU-7P5CJZ56rbTv7iXdYYzmqKE,425
spacy/tests/lang/ja/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ja/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/ja/__pycache__/test_lemmatization.cpython-311.pyc,,
spacy/tests/lang/ja/__pycache__/test_morphologizer_factory.cpython-311.pyc,,
spacy/tests/lang/ja/__pycache__/test_serialize.cpython-311.pyc,,
spacy/tests/lang/ja/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/ja/test_lemmatization.py,sha256=QMSb2llxVUBcRgJs7-kK7BCemTg4twVYokR13B70W54,718
spacy/tests/lang/ja/test_morphologizer_factory.py,sha256=QA0u8ftMX4gqXtb3pMhWb6AyjvY52UtvuDM35IS78qI,252
spacy/tests/lang/ja/test_serialize.py,sha256=rZEgFyU72BgOAUP9ZclouxrhJtG8t46MokUM5R1rYWQ,1347
spacy/tests/lang/ja/test_tokenizer.py,sha256=XFiMDBeIm87IfE3gDjJQj2cRhOR_0BaycI4vduL3ciA,8067
spacy/tests/lang/ko/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ko/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/ko/__pycache__/test_lemmatization.cpython-311.pyc,,
spacy/tests/lang/ko/__pycache__/test_serialize.cpython-311.pyc,,
spacy/tests/lang/ko/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/ko/test_lemmatization.py,sha256=mcGOA8DCJrDnxd8wdWQohWCwR40-FsrqPdbsziLjhU8,318
spacy/tests/lang/ko/test_serialize.py,sha256=DmRErvmNxrK-9zOw3slKz4G8_x5TodbtNI9vMOuHwz0,736
spacy/tests/lang/ko/test_tokenizer.py,sha256=i57i_tuf1Vr-y6sitMUtP4lDq6gC2atezLG-iXqzGHQ,2968
spacy/tests/lang/ky/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ky/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/ky/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/ky/test_tokenizer.py,sha256=Jx8syyzpsgwcxp6g46nIAjvqH0vNNX_v5SX72m_ymgo,4100
spacy/tests/lang/la/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/la/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/la/__pycache__/test_exception.cpython-311.pyc,,
spacy/tests/lang/la/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/la/test_exception.py,sha256=pnlUIANbRNLTIyx31qdzqHCz-WEqPTsJqPeX7AHXNXk,244
spacy/tests/lang/la/test_text.py,sha256=KndJC0wdCJoZNMVTKRVWH5BRlUyW6SCaGStxQhkjqHE,838
spacy/tests/lang/lb/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/lb/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/lb/__pycache__/test_exceptions.cpython-311.pyc,,
spacy/tests/lang/lb/__pycache__/test_prefix_suffix_infix.cpython-311.pyc,,
spacy/tests/lang/lb/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/lb/test_exceptions.py,sha256=Uneh-YjxNseMsCTv2lb-PQAhe-HJ0XwJFItmPcsAZRg,610
spacy/tests/lang/lb/test_prefix_suffix_infix.py,sha256=yL2VY1iUNTdIaq1qFQlREhPxm39Gi_Eyt2ITd40K26M,603
spacy/tests/lang/lb/test_text.py,sha256=2gMZkdSN8pwCdALSbAzGiAni7Wz5WPCYYF8an_Swujk,1323
spacy/tests/lang/lg/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/lg/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/lg/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/lg/test_tokenizer.py,sha256=6kZvUhCpOrQ2fdn8sQ_cFDP02wT4FxJwUjvCeWuPP_Y,464
spacy/tests/lang/lt/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/lt/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/lt/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/lt/test_text.py,sha256=ufLYjnQSHZ_5DYlYo8NX0O6H-c_FORSCA2hI-7u9gj4,1697
spacy/tests/lang/lv/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/lv/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/lv/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/lv/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/lv/test_text.py,sha256=dZZCSKffPxNlVFC6ByCfKUv7AHwl1_Osys0PwWzySwE,1045
spacy/tests/lang/lv/test_tokenizer.py,sha256=Y4g_uaHPtgy4IwY2b4yW9o0UOybW83-jX7woTi27pUQ,808
spacy/tests/lang/mk/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/mk/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/mk/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/mk/test_text.py,sha256=qkUCZfKgQTQ5L8KKH4FbYmFD4X7xHtlyV8s7xpV4lno,4429
spacy/tests/lang/ml/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ml/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/ml/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/ml/test_text.py,sha256=qQGlrSmaubLNc-9ARiQSYzktEKU_lPXiXzFUY3zUW1M,1097
spacy/tests/lang/nb/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/nb/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/nb/__pycache__/test_noun_chunks.cpython-311.pyc,,
spacy/tests/lang/nb/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/nb/test_noun_chunks.py,sha256=B-NlCy_FMhkUmt8DtpQ9b0y3-rxoyxnV2mnEuSCpsPw,285
spacy/tests/lang/nb/test_tokenizer.py,sha256=518FF29v6lyJ88SI6uZ9smrUNlWZpfhZvlZiVI6UFiY,650
spacy/tests/lang/ne/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ne/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/ne/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/ne/test_text.py,sha256=No3TyHdAS5Ybe0rV7OOHjqOwfRatygU10ktnXZsZGxs,783
spacy/tests/lang/nl/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/nl/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/nl/__pycache__/test_noun_chunks.cpython-311.pyc,,
spacy/tests/lang/nl/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/nl/test_noun_chunks.py,sha256=-lhjtzcwv6vXeMgZKpN0gcQaZkyVZZdRHbbifR7s7iY,4528
spacy/tests/lang/nl/test_text.py,sha256=QboYMKpkbemr_CyJJoiKhJPZfASA1HTJA-fonu6srPU,717
spacy/tests/lang/pl/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/pl/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/pl/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/pl/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/pl/test_text.py,sha256=qdyxq-4NFgJvfbuGB4NcM35vpjOuefH_v9G1HHM4Z38,545
spacy/tests/lang/pl/test_tokenizer.py,sha256=l0nvFUb0ADooJ77Y-ZbS2P81mWsQJsr5qVWoAwhYCIM,578
spacy/tests/lang/pt/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/pt/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/pt/__pycache__/test_noun_chunks.cpython-311.pyc,,
spacy/tests/lang/pt/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/pt/test_noun_chunks.py,sha256=gYna7cPDKWHtF4HXxStHvccYOHOuaCDjAm8ReqvmXSw,7969
spacy/tests/lang/pt/test_text.py,sha256=peQsO2lumtTwRXceQyZTCD-UX3FmYY6XxhBQyZ8rT0E,227
spacy/tests/lang/ro/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ro/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/ro/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/ro/test_tokenizer.py,sha256=iven2-PuZ-yS2mqPKxjZaPSO--RW5iJIxOGCLl-Kag8,756
spacy/tests/lang/ru/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ru/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/ru/__pycache__/test_exceptions.cpython-311.pyc,,
spacy/tests/lang/ru/__pycache__/test_lemmatizer.cpython-311.pyc,,
spacy/tests/lang/ru/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/ru/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/ru/test_exceptions.py,sha256=DL1fT5b9asOsVn4yF-AKINWFHExPUVaI4rA5QCRLYSE,364
spacy/tests/lang/ru/test_lemmatizer.py,sha256=38uhOdn47K3cmc8oz7ln5J-tOs8dVuOq1g0A6vJo1N8,4067
spacy/tests/lang/ru/test_text.py,sha256=E4Yja0xdqtqmbffAhcNiCB6vWZRAn95k72BiE1ZZSJQ,228
spacy/tests/lang/ru/test_tokenizer.py,sha256=PRlj03gpFukHT9HRLx55wOuSStDCfXeyXwcVH7GzfhM,6141
spacy/tests/lang/sa/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/sa/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/sa/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/sa/test_text.py,sha256=zrIvezsBb5twTG3R-4FMP5dmdiSikdS158m_ymyA_LM,1339
spacy/tests/lang/sk/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/sk/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/sk/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/sk/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/sk/test_text.py,sha256=NwbRs3s4Ct237UKc-l5BWGKrnppOj5cs8tA2LCFEuGo,1533
spacy/tests/lang/sk/test_tokenizer.py,sha256=mG4Kq2xP0tiIE5oy5jRv4WA4yJD-nfs-l6O9l9XYs64,468
spacy/tests/lang/sl/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/sl/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/sl/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/sl/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/sl/test_text.py,sha256=J9_mWfor_w8KcIMPtk3EnBJjMzyNU3TyqFEdvHK72xs,1019
spacy/tests/lang/sl/test_tokenizer.py,sha256=zZrM7PMHdBqBBB4BCr-YoIAlXOkczdjbeH5FseMSFlw,863
spacy/tests/lang/sq/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/sq/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/sq/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/sq/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/sq/test_text.py,sha256=dXj7Cg1Ea7iVg19saOSxDsIdnJFxCCHGzmb64qU9MmI,1203
spacy/tests/lang/sq/test_tokenizer.py,sha256=Og8WFtuSG9ILc4H7lLdOYMQCpkS-R8W9Urlq7xpvJMI,848
spacy/tests/lang/sr/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/sr/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/sr/__pycache__/test_exceptions.cpython-311.pyc,,
spacy/tests/lang/sr/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/sr/test_exceptions.py,sha256=KqnQeBQAH0fbrIZ27V8lkW1TwJTsKDKpH3gDBl3njiQ,526
spacy/tests/lang/sr/test_tokenizer.py,sha256=JnJAZQ8W0h2E_A-7u5VZkpCW0sp1uV_MsC0eifyCWb4,4426
spacy/tests/lang/sv/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/sv/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/sv/__pycache__/test_exceptions.cpython-311.pyc,,
spacy/tests/lang/sv/__pycache__/test_lex_attrs.cpython-311.pyc,,
spacy/tests/lang/sv/__pycache__/test_noun_chunks.cpython-311.pyc,,
spacy/tests/lang/sv/__pycache__/test_prefix_suffix_infix.cpython-311.pyc,,
spacy/tests/lang/sv/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/sv/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/sv/test_exceptions.py,sha256=1v15qkJKJgFjHoupaI8in9BwfF4YFGuXljKU6gPlavM,2530
spacy/tests/lang/sv/test_lex_attrs.py,sha256=V8sklkTtpS0yRlF7HShS3Gkt4Yx1VetkEFzPRAWTtD0,713
spacy/tests/lang/sv/test_noun_chunks.py,sha256=YkvcHfffZFvK59JhBhj2OTVKkushAENRB63pTHs0nxE,1905
spacy/tests/lang/sv/test_prefix_suffix_infix.py,sha256=_NDUz1GG7ofPS8eYE7damA2zFJVq8mMf5dvqEbu8ZFY,1087
spacy/tests/lang/sv/test_text.py,sha256=u-SjK4xInt_BUnvuUhjabYxbb2JqnqpLvz7voTivIg8,737
spacy/tests/lang/sv/test_tokenizer.py,sha256=vJMpiVLcyewlHBeHoLUuKTCI886kWpnfvVBuh3aSFeA,1036
spacy/tests/lang/ta/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ta/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/ta/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/ta/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/ta/test_text.py,sha256=bnjxAl8Btcvkp_0flGG29vt18OoiwTUu3pYNg6xSyjM,2762
spacy/tests/lang/ta/test_tokenizer.py,sha256=91fLMLGRHNYXng5sm7IbBs6kEg3k3YHehqhOCCLVqkg,8017
spacy/tests/lang/test_attrs.py,sha256=tME12dxmc31SnSEJjsgPGARwSzNZ_H7Ot44UrffVqKE,3702
spacy/tests/lang/test_initialize.py,sha256=VdvI4gWV_spngnofQYahdg6lIoMSlPSXwDcwJI_vwqQ,946
spacy/tests/lang/test_lemmatizers.py,sha256=YuaUtsEIRNg6zuk4HiTvu2D89TPztmcfbtaxgV_4fXE,1964
spacy/tests/lang/th/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/th/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/th/__pycache__/test_serialize.cpython-311.pyc,,
spacy/tests/lang/th/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/th/test_serialize.py,sha256=zV1LosdvG_b1Mw_WjCyjxUbHaQh-z01CaW72MQLIo8c,730
spacy/tests/lang/th/test_tokenizer.py,sha256=V0ctcHhaepZdZ8U2jjDf0WpiehJbqi_6pvnBC13pE-w,327
spacy/tests/lang/ti/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ti/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/ti/__pycache__/test_exception.cpython-311.pyc,,
spacy/tests/lang/ti/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/ti/test_exception.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ti/test_text.py,sha256=_ljfsUdaLB0jC3Kvc0PVVbsce56ywqKCehce2sj8Mak,2171
spacy/tests/lang/tl/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/tl/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/tl/__pycache__/test_indices.cpython-311.pyc,,
spacy/tests/lang/tl/__pycache__/test_punct.cpython-311.pyc,,
spacy/tests/lang/tl/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/tl/test_indices.py,sha256=wO9XybcK95I4Jma0uHV271x5UHdG5rBN26Q7GEKgLcc,265
spacy/tests/lang/tl/test_punct.py,sha256=vpR25MF8UGRiu-utrVlfdL1ErVYWkCpP6R83xfncFOI,4547
spacy/tests/lang/tl/test_text.py,sha256=rbCebBtF1S0yO9nHhL3EpQxluLLJ5AQrDtbzBRf3Ako,2552
spacy/tests/lang/tr/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/tr/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/tr/__pycache__/test_noun_chunks.cpython-311.pyc,,
spacy/tests/lang/tr/__pycache__/test_parser.cpython-311.pyc,,
spacy/tests/lang/tr/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/tr/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/tr/test_noun_chunks.py,sha256=S2VnDNh-psiE3idxxtvZSV0HgnCVY1n_N6Jr7JYyLgI,434
spacy/tests/lang/tr/test_parser.py,sha256=UdFSv1O2fyYQWxm--VJPOVrCcdE2SdrYtx0C8yi3OkU,20359
spacy/tests/lang/tr/test_text.py,sha256=Llq2EHBwzh4v-VfmjfybZPqm8_hY8RceqGnOXBiEc3E,1843
spacy/tests/lang/tr/test_tokenizer.py,sha256=iQC0RPoIkZDufLJGirFSNtrWR4qk5xn4YAswDjvpDJA,20286
spacy/tests/lang/tt/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/tt/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/tt/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/tt/test_tokenizer.py,sha256=57IrWYSbMvyowq4k287FCaCy0-DhcS27llNIhzM3TWA,3805
spacy/tests/lang/uk/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/uk/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/uk/__pycache__/test_lemmatizer.cpython-311.pyc,,
spacy/tests/lang/uk/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/uk/__pycache__/test_tokenizer_exc.cpython-311.pyc,,
spacy/tests/lang/uk/test_lemmatizer.py,sha256=kmzGu6hjUJ24i-4jDCT4lSawURdgbWBtSP07lL7RWc8,864
spacy/tests/lang/uk/test_tokenizer.py,sha256=-phpM82DrPpfUYbm4_WjZ_hqMXmFKHiBr9zwU0zvsPo,5565
spacy/tests/lang/uk/test_tokenizer_exc.py,sha256=AjcwFraAyqJbbTjVUCktYuQmO1H8H0Of-WdAjQMsn3I,375
spacy/tests/lang/ur/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ur/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/ur/__pycache__/test_prefix_suffix_infix.cpython-311.pyc,,
spacy/tests/lang/ur/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/ur/test_prefix_suffix_infix.py,sha256=zxcOQGPBjV_tKrMteLQxVU52Sd_NkYLd5gV0edKiGfk,237
spacy/tests/lang/ur/test_text.py,sha256=ycw5veAPOiifwJ0zbOMH8mCEpEZZNpCzM5T74Zokzx0,492
spacy/tests/lang/vi/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/vi/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/vi/__pycache__/test_serialize.cpython-311.pyc,,
spacy/tests/lang/vi/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/vi/test_serialize.py,sha256=Xy1clc27VDg7h-DSpnw1jAyb6tMC4uVuVV7nn23A7JA,1349
spacy/tests/lang/vi/test_tokenizer.py,sha256=cr1TmFTaHS9gZg9RW_VfWijL4kC3JPee28Chazt1hWQ,1724
spacy/tests/lang/xx/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/xx/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/xx/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/xx/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/xx/test_text.py,sha256=w0yzR8-rXBEkyS7eS99MwWZk3P6sD7HDFrD5fcAk8Y0,1744
spacy/tests/lang/xx/test_tokenizer.py,sha256=GvABfRk9yWz4mRk1PHOXTAzSADx7suOgR8Vr45-YiF0,694
spacy/tests/lang/yo/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/yo/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/yo/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/yo/test_text.py,sha256=u3CNEfIzXXh8OP4sqDCbw87Fm6I9mvuJ8L7xL2PxwWU,1519
spacy/tests/lang/zh/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/zh/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/lang/zh/__pycache__/test_serialize.cpython-311.pyc,,
spacy/tests/lang/zh/__pycache__/test_text.cpython-311.pyc,,
spacy/tests/lang/zh/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/lang/zh/test_serialize.py,sha256=WjwgbjMr48XuIAGvqHEcVBJy1q6B8AblXiRCoIXF0ok,1290
spacy/tests/lang/zh/test_text.py,sha256=Kn58rL0dPZF2DIt0yVvfOBEJanXjAXv8lmr5XXrTXYw,475
spacy/tests/lang/zh/test_tokenizer.py,sha256=fR9dm_kg8RpAdhTTEv7fFzOAqMBG2lhZZkGZgRspYzM,2897
spacy/tests/matcher/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/matcher/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/matcher/__pycache__/test_dependency_matcher.cpython-311.pyc,,
spacy/tests/matcher/__pycache__/test_levenshtein.cpython-311.pyc,,
spacy/tests/matcher/__pycache__/test_matcher_api.cpython-311.pyc,,
spacy/tests/matcher/__pycache__/test_matcher_logic.cpython-311.pyc,,
spacy/tests/matcher/__pycache__/test_pattern_validation.cpython-311.pyc,,
spacy/tests/matcher/__pycache__/test_phrase_matcher.cpython-311.pyc,,
spacy/tests/matcher/test_dependency_matcher.py,sha256=y3fBQTpQslI5aPeM9HjnioXqjadNGDXI8rR5sUq63AQ,15356
spacy/tests/matcher/test_levenshtein.py,sha256=SVYBfJ6wZG5y16T9hEX5k93uV2Isuhg57uxDL3CYimw,2870
spacy/tests/matcher/test_matcher_api.py,sha256=C9XO9CMgLATRU29uSLif01cXqsMArBPX0vUz9MwUi1w,30824
spacy/tests/matcher/test_matcher_logic.py,sha256=b-SMBE8pJbUtUki_KywzRl7Kfbcj2c0LTLcAAzykeKk,27971
spacy/tests/matcher/test_pattern_validation.py,sha256=NjTdaQYfwQRKUn5Dw2Xwe89PAJq0X-hZ9HFyPkyNzVw,3418
spacy/tests/matcher/test_phrase_matcher.py,sha256=WYUKmaZ5uWGZqxiFc9Qu8op-XDZ8AzxzIeaHWD1qFnc,18476
spacy/tests/morphology/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/morphology/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/morphology/__pycache__/test_morph_converters.cpython-311.pyc,,
spacy/tests/morphology/__pycache__/test_morph_features.cpython-311.pyc,,
spacy/tests/morphology/__pycache__/test_morph_pickle.cpython-311.pyc,,
spacy/tests/morphology/test_morph_converters.py,sha256=Ds6BVEguOfp-D3MlxlV87QNK7nBlrFcGrIE4b8TVJ3I,877
spacy/tests/morphology/test_morph_features.py,sha256=Yif5-7D2LjzTOWUW0ZxQ0UfU7cuHXqIkZypg9YsCDbk,1397
spacy/tests/morphology/test_morph_pickle.py,sha256=XHwrAFadmVod-dRayUE_wRGge-_RnhUgq1GTUIQi-OM,689
spacy/tests/package/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/package/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/package/__pycache__/test_requirements.cpython-311.pyc,,
spacy/tests/package/pyproject.toml,sha256=YBxGOSNpHEAyqO-sBqP31nZtGlTW3EKLPe0YvG_eEj4,261
spacy/tests/package/requirements.txt,sha256=0tyZrICOTt8i5egpqja2dBPsrWn3GWM_gCNOmM9Ss4E,1023
spacy/tests/package/setup.cfg,sha256=A3HJiLzx00WhuXEFiRREg2GK4sk6V3-KZnwjWRye89o,3897
spacy/tests/package/test_requirements.py,sha256=gz0cZqIM6r0KYnj7EszDSo7OuSJtceSCdoRf08fngkE,3284
spacy/tests/parser/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/parser/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/parser/__pycache__/test_add_label.cpython-311.pyc,,
spacy/tests/parser/__pycache__/test_arc_eager_oracle.cpython-311.pyc,,
spacy/tests/parser/__pycache__/test_ner.cpython-311.pyc,,
spacy/tests/parser/__pycache__/test_neural_parser.cpython-311.pyc,,
spacy/tests/parser/__pycache__/test_nn_beam.cpython-311.pyc,,
spacy/tests/parser/__pycache__/test_nonproj.cpython-311.pyc,,
spacy/tests/parser/__pycache__/test_parse.cpython-311.pyc,,
spacy/tests/parser/__pycache__/test_parse_navigate.cpython-311.pyc,,
spacy/tests/parser/__pycache__/test_preset_sbd.cpython-311.pyc,,
spacy/tests/parser/__pycache__/test_space_attachment.cpython-311.pyc,,
spacy/tests/parser/__pycache__/test_state.cpython-311.pyc,,
spacy/tests/parser/test_add_label.py,sha256=cKC0KEDddoFXAFHqiyLutMbmhv9bJ8KE1nu_PJ9q7IM,5077
spacy/tests/parser/test_arc_eager_oracle.py,sha256=j3i4E9VmyeSuVq6WlN4J9UJ7szEq-d1k2yhsmt07gR8,10378
spacy/tests/parser/test_ner.py,sha256=j1GSsdOTVCNsK3QMnN7YfZ390CG9rrwtRH8ZaqcvJ6s,29950
spacy/tests/parser/test_neural_parser.py,sha256=X7K9z1k2Htg61liLySlwgpEUEYKyGrciWJRFsU8kBac,2969
spacy/tests/parser/test_nn_beam.py,sha256=G6Tte_F-sjkK0As9KjnE3tWiPS3g3Fb3xbYrHDV344k,3742
spacy/tests/parser/test_nonproj.py,sha256=dlJGeJjvUYyqsdVC4sXv6UUYWZuIZp5IiIryJOt35Wg,6438
spacy/tests/parser/test_parse.py,sha256=S2uOmHHbq2ZtPsfDYabtUFZ7NSdwY3kxZgdJt2T7_IQ,20702
spacy/tests/parser/test_parse_navigate.py,sha256=_uahNVRUXiG2b7FW_K441hJlnauAQ1_65AFXNgA5Dhs,6340
spacy/tests/parser/test_preset_sbd.py,sha256=V21fgWJLGA1bq86yt3YgHHoA3DJI3AZLVFbOJVYJ7SQ,2647
spacy/tests/parser/test_space_attachment.py,sha256=-kZGWiKJkOj6M1eVKTDqnS_pDRCyf7B16Z0VJo0eejM,3005
spacy/tests/parser/test_state.py,sha256=Qh0UBzTHvbp4G4g-2vpe7LU5TppnROv7qtr1dOrRObY,1973
spacy/tests/pipeline/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/pipeline/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/pipeline/__pycache__/test_analysis.cpython-311.pyc,,
spacy/tests/pipeline/__pycache__/test_annotates_on_update.cpython-311.pyc,,
spacy/tests/pipeline/__pycache__/test_attributeruler.cpython-311.pyc,,
spacy/tests/pipeline/__pycache__/test_edit_tree_lemmatizer.cpython-311.pyc,,
spacy/tests/pipeline/__pycache__/test_entity_linker.cpython-311.pyc,,
spacy/tests/pipeline/__pycache__/test_entity_ruler.cpython-311.pyc,,
spacy/tests/pipeline/__pycache__/test_functions.cpython-311.pyc,,
spacy/tests/pipeline/__pycache__/test_initialize.cpython-311.pyc,,
spacy/tests/pipeline/__pycache__/test_lemmatizer.cpython-311.pyc,,
spacy/tests/pipeline/__pycache__/test_models.cpython-311.pyc,,
spacy/tests/pipeline/__pycache__/test_morphologizer.cpython-311.pyc,,
spacy/tests/pipeline/__pycache__/test_pipe_factories.cpython-311.pyc,,
spacy/tests/pipeline/__pycache__/test_pipe_methods.cpython-311.pyc,,
spacy/tests/pipeline/__pycache__/test_sentencizer.cpython-311.pyc,,
spacy/tests/pipeline/__pycache__/test_senter.cpython-311.pyc,,
spacy/tests/pipeline/__pycache__/test_span_ruler.cpython-311.pyc,,
spacy/tests/pipeline/__pycache__/test_spancat.cpython-311.pyc,,
spacy/tests/pipeline/__pycache__/test_tagger.cpython-311.pyc,,
spacy/tests/pipeline/__pycache__/test_textcat.cpython-311.pyc,,
spacy/tests/pipeline/__pycache__/test_tok2vec.cpython-311.pyc,,
spacy/tests/pipeline/test_analysis.py,sha256=YiFi_Q9Zqfou5ze99_xvlm3gee6fMHyBxgFm_x3CpI0,3570
spacy/tests/pipeline/test_annotates_on_update.py,sha256=Gwsm2tAgSB9UqwB3f8uMXwJl5P7UnN6DPQ6vWmmn53I,3304
spacy/tests/pipeline/test_attributeruler.py,sha256=U1kYDK2V9VI-sL2xa2JXUkwe-s_elO2Zj4_cb4qqsMM,9828
spacy/tests/pipeline/test_edit_tree_lemmatizer.py,sha256=CYEyu0AD8Kb5dwsRYilbwSlD_c54DXx5bZ86bMR9TWk,10780
spacy/tests/pipeline/test_entity_linker.py,sha256=-xbaX9jzqh8gYICn6mnj-Aqp4c8EYI_cbs3MDT14lH8,46418
spacy/tests/pipeline/test_entity_ruler.py,sha256=izBlPi-_iP--3R8vGqQmV_zeaE2-mTj0tA5fzhV_K2Y,26607
spacy/tests/pipeline/test_functions.py,sha256=Uq9cS30rA_kjg3UPux1IzJ8TTRA_SDSqbD40K0vQ04w,3289
spacy/tests/pipeline/test_initialize.py,sha256=UC8D7Y3YlElbvm4hBSNbjR_2wA6Jk1SV-uGEk5O014k,2419
spacy/tests/pipeline/test_lemmatizer.py,sha256=UZ9H8MI9-27wdMskYUXgH_lCFpztYRRLuw-Tl1HCzSE,3838
spacy/tests/pipeline/test_models.py,sha256=JhkI_tQBq3yfYFOxujBxxmPh8xi2HlHYf1kdgkcODfI,3952
spacy/tests/pipeline/test_morphologizer.py,sha256=9yQzKEY7Hf7R40qy-AniJRz5dOka0kTatIUzBkVWdCs,7305
spacy/tests/pipeline/test_pipe_factories.py,sha256=_IbGMj_xam4IK1H8npX96FAaUN64h6QUu9JNbFfhsd0,20492
spacy/tests/pipeline/test_pipe_methods.py,sha256=Hyen9TKezo5xhw8Ut3lw88s1weZLWv4G5OBkMnZiQy0,23997
spacy/tests/pipeline/test_sentencizer.py,sha256=QYyFihwU-engJ7WLY86KZiBLL5ay9m-EthtDurQZou4,18425
spacy/tests/pipeline/test_senter.py,sha256=Pmu0RWwQ2GxY2RazUta-IsJvUPYVPfFkvSff0-UFra8,3416
spacy/tests/pipeline/test_span_ruler.py,sha256=HNqoBgaQ5RRbd3t6Vm3KkL0zlrTRojgh3-ESlL_UxFk,16686
spacy/tests/pipeline/test_spancat.py,sha256=mX5kTRSGH4kfd-lA9DJnFDCnlcqXmXVLlRAkSh5Vqx4,21953
spacy/tests/pipeline/test_tagger.py,sha256=4zIUklNuzczgLbIa3PsZiucnrNCQ8lcgOQFjwMfwt3o,6955
spacy/tests/pipeline/test_textcat.py,sha256=a25EdGplVs-j4NjGUEx6X6Y4GSRcsQ49Tg8ekJzFCB4,36657
spacy/tests/pipeline/test_tok2vec.py,sha256=7dYPgoPb_XyJ6AvI71f-VrjQJ-A4mkuZUi3EMbofyy8,22447
spacy/tests/serialize/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/serialize/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/serialize/__pycache__/test_resource_warning.cpython-311.pyc,,
spacy/tests/serialize/__pycache__/test_serialize_config.cpython-311.pyc,,
spacy/tests/serialize/__pycache__/test_serialize_doc.cpython-311.pyc,,
spacy/tests/serialize/__pycache__/test_serialize_docbin.cpython-311.pyc,,
spacy/tests/serialize/__pycache__/test_serialize_extension_attrs.cpython-311.pyc,,
spacy/tests/serialize/__pycache__/test_serialize_kb.cpython-311.pyc,,
spacy/tests/serialize/__pycache__/test_serialize_language.cpython-311.pyc,,
spacy/tests/serialize/__pycache__/test_serialize_pipeline.cpython-311.pyc,,
spacy/tests/serialize/__pycache__/test_serialize_span_groups.cpython-311.pyc,,
spacy/tests/serialize/__pycache__/test_serialize_tokenizer.cpython-311.pyc,,
spacy/tests/serialize/__pycache__/test_serialize_vocab_strings.cpython-311.pyc,,
spacy/tests/serialize/test_resource_warning.py,sha256=C_LSm4ZwhzMeXCMCRJNckgeD4zdhfol3jN2tK8sboho,4328
spacy/tests/serialize/test_serialize_config.py,sha256=_m1Kia2zqdECt2CniCWO2MRDz-PMvzGotidaoX2HdzA,18708
spacy/tests/serialize/test_serialize_doc.py,sha256=afu1kWRW5_LLQ0sxO2yeEs_vVW2E8tHLPNcvcqKY-L4,7595
spacy/tests/serialize/test_serialize_docbin.py,sha256=ZNMcwlDE8__hRWqNtPObe8ENujznsi1oT8VokD2rAxo,4141
spacy/tests/serialize/test_serialize_extension_attrs.py,sha256=14Ifo2x5xUm4q16fMth91x7EUrFVR-ReHM2YttF5u8U,974
spacy/tests/serialize/test_serialize_kb.py,sha256=5rOotpJ6GJnnXYLx0cj0j7UQMBVTh5CUrW54LbtRKCg,7263
spacy/tests/serialize/test_serialize_language.py,sha256=h5qz5lNWh1NCFtV5olUOR-KdBhRIEZLm6GSCiweqSrQ,3729
spacy/tests/serialize/test_serialize_pipeline.py,sha256=aUb-irUf6zAaBNNfveOEHgdYtLPLF2Zjc4WOsI644_Q,17330
spacy/tests/serialize/test_serialize_span_groups.py,sha256=y3DDgaQzY7hrn-GX1J1RNWJJjViQRDVGDHQkJHCrPFA,8929
spacy/tests/serialize/test_serialize_tokenizer.py,sha256=11UKpmBt7A03lOLIgpdc_BYQzeg6rL-Duwu8y8Sxk0Y,5586
spacy/tests/serialize/test_serialize_vocab_strings.py,sha256=1M2AaQgAaDaGPSr1swKLo1QrpZIR_C11Jol_Us9ph24,7262
spacy/tests/test_architectures.py,sha256=MwwGFtOFQu-ryEf37mLdbnCgaoncyE4tSusz_SsJQKI,462
spacy/tests/test_cli.py,sha256=GEnRvJDmu_vFaikGWiwQXOuW2u1jp_tp4XKCtWDRwZw,51346
spacy/tests/test_cli_app.py,sha256=fLNSpe1dcUUyyVzp2c6Td1BcbI3D6s97bIoR0sUVjJk,8173
spacy/tests/test_displacy.py,sha256=vT6hdStr-cEdArjxHxRqoNpBGKlR8D6PJvRcZelsKzU,13979
spacy/tests/test_errors.py,sha256=awSfQ69K1XuL4X82-pfjRbJg4kMoeqnJvPTl1LoTkEI,349
spacy/tests/test_language.py,sha256=jWbbxvEyaiFnQZvlxVMXXO6LV7GRRyG0aSiGzqDOmx4,27793
spacy/tests/test_misc.py,sha256=2N0Gy_SPzqml3yD5sYhQUYvvtCSA2FizrPAxP4oc5Jw,15888
spacy/tests/test_models.py,sha256=7VIlRinw-lV9-dAphcK7QW89eQVNHaPNnmbncpOvipY,9360
spacy/tests/test_pickles.py,sha256=q4RP3BFjxFwL_6vawVfeYSH6IwwbkSZqMaFzuG6IYwY,2083
spacy/tests/test_scorer.py,sha256=_SaigfulnCZ34V-BcxRgA8arRgZPWpY1jI1_zIm2TiE,17380
spacy/tests/test_ty.py,sha256=76nkWHSDlnw3hAuVr5GHOratmXxqf-N_lz4Ah5E9xOg,766
spacy/tests/tokenizer/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/tokenizer/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/tokenizer/__pycache__/test_exceptions.cpython-311.pyc,,
spacy/tests/tokenizer/__pycache__/test_explain.cpython-311.pyc,,
spacy/tests/tokenizer/__pycache__/test_naughty_strings.cpython-311.pyc,,
spacy/tests/tokenizer/__pycache__/test_tokenizer.cpython-311.pyc,,
spacy/tests/tokenizer/__pycache__/test_urls.cpython-311.pyc,,
spacy/tests/tokenizer/__pycache__/test_whitespace.cpython-311.pyc,,
spacy/tests/tokenizer/sun.txt,sha256=kP35baZeinL_9jG7WR06vfnjJtnGN4nDDkuWeJiiWJU,2595
spacy/tests/tokenizer/test_exceptions.py,sha256=BV9kUwONgBFL9JiqHIhBmEh7uzb6k17uZvVHMhPWrJ4,1809
spacy/tests/tokenizer/test_explain.py,sha256=DkNV3Yny_rjQqJcSxjVQhx7XKHeglwWsAN3uvuou3N4,4978
spacy/tests/tokenizer/test_naughty_strings.py,sha256=twv3xOvYq2ZKpy1BpcTso8aR4-zHWHfV5Q92qEKLfF4,7581
spacy/tests/tokenizer/test_tokenizer.py,sha256=dbCK0dC7gjBLr_MfyaG3Ij4mGYfh0hKk_zA99UK2KBM,19142
spacy/tests/tokenizer/test_urls.py,sha256=20RSAlsssK438gGw3SYTE3EewbN9f1fGhMFbt49iMEo,6669
spacy/tests/tokenizer/test_whitespace.py,sha256=icALPI0Nq5vhcPXl2DS0xBU5KZ1nLm40Ngb8X86EU4A,1340
spacy/tests/training/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/training/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/training/__pycache__/test_augmenters.cpython-311.pyc,,
spacy/tests/training/__pycache__/test_corpus.cpython-311.pyc,,
spacy/tests/training/__pycache__/test_logger.cpython-311.pyc,,
spacy/tests/training/__pycache__/test_new_example.cpython-311.pyc,,
spacy/tests/training/__pycache__/test_pretraining.cpython-311.pyc,,
spacy/tests/training/__pycache__/test_readers.cpython-311.pyc,,
spacy/tests/training/__pycache__/test_rehearse.cpython-311.pyc,,
spacy/tests/training/__pycache__/test_training.cpython-311.pyc,,
spacy/tests/training/test_augmenters.py,sha256=b01Psjk2YsqRRkkr6p0skTfuoM7vTvJASXir7jK-aGI,10848
spacy/tests/training/test_corpus.py,sha256=tmiCokBkZ0XbnoIIqIuWIpwv_6H5xA_l3zu7EegwODc,2019
spacy/tests/training/test_logger.py,sha256=3SCcMDq6ph_FDQwlU0WMxamtDB0oGHkpWaE9lZ4Se6s,630
spacy/tests/training/test_new_example.py,sha256=KK1spc1Msqdt1XiO8pvPfWN4Md7-cPOo3VmibpUMlk8,16616
spacy/tests/training/test_pretraining.py,sha256=XdJxnt8Sd9qGYeU2YAIt2cA9daA8aao1R-hu98xnyIw,12406
spacy/tests/training/test_readers.py,sha256=49RCWkjdRSWFNlTInNoIe4nlLuUOMgArDWq_36aPtlQ,4067
spacy/tests/training/test_rehearse.py,sha256=Dh91VqLHH8SVcSDr7a6-TyzLukR35fy-9EJ0QRu4VdQ,6616
spacy/tests/training/test_training.py,sha256=nx3wQYpIkOjeSM4st8JUu60zP6Yq5R54F0CvGXUsjQc,46716
spacy/tests/util.py,sha256=7POr47kOYSBHZYnr3kbHhZ89IAX12z5srwmkT0BV0NY,3381
spacy/tests/vocab_vectors/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/vocab_vectors/__pycache__/__init__.cpython-311.pyc,,
spacy/tests/vocab_vectors/__pycache__/test_lexeme.cpython-311.pyc,,
spacy/tests/vocab_vectors/__pycache__/test_lookups.cpython-311.pyc,,
spacy/tests/vocab_vectors/__pycache__/test_similarity.cpython-311.pyc,,
spacy/tests/vocab_vectors/__pycache__/test_stringstore.cpython-311.pyc,,
spacy/tests/vocab_vectors/__pycache__/test_vectors.cpython-311.pyc,,
spacy/tests/vocab_vectors/__pycache__/test_vocab_api.cpython-311.pyc,,
spacy/tests/vocab_vectors/test_lexeme.py,sha256=pAu0oKME71gAAkCMeFgR9OWcyR7eg3zxrAbuOjH3Qfc,2935
spacy/tests/vocab_vectors/test_lookups.py,sha256=DoZC2oGTNkdrS83nB6FOalJkKwgcuHVnbytCVsbdG0s,4792
spacy/tests/vocab_vectors/test_similarity.py,sha256=IfhEGT8tQvJ7e4vr59ZE909bzsBwqXs0ERpMdi68_wA,3944
spacy/tests/vocab_vectors/test_stringstore.py,sha256=-0nYsHpNn9YA2o32tFOcDo5tQ6MffwAbbUbA2IDXbDk,3435
spacy/tests/vocab_vectors/test_vectors.py,sha256=EaZoZg6QSR0f2NveMzxEORaOtO5vsFeQaRLa117P9Aw,23229
spacy/tests/vocab_vectors/test_vocab_api.py,sha256=5aneb5InIbpcJFbFgwvt_T2OSLqvY2BiwKsjqQtOemg,2229
spacy/tokenizer.cp311-win_amd64.pyd,sha256=rOCNFWyj29v8wrNlRTJdhmRZJv6ByXbye8-6W39Y9Yk,320000
spacy/tokenizer.cpp,sha256=_a4p_T7GR5dbfupzWPUgZDq-JvPgppMHx-K31dcPZaQ,1908609
spacy/tokenizer.pxd,sha256=-m1kJk34o9fj8fkckOXbbjGVCEltdmhLm901qT2F_qA,2413
spacy/tokenizer.pyx,sha256=ld8kCbUbn_GhmiMiCSGeH1KqnQnWJU7XhrH3K2eu-lg,37414
spacy/tokens/__init__.pxd,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tokens/__init__.py,sha256=r4iMSf5Vo3EbeBCfUBLcDkQhvqvbUIyloPre7rd5l-w,259
spacy/tokens/__pycache__/__init__.cpython-311.pyc,,
spacy/tokens/__pycache__/_dict_proxies.cpython-311.pyc,,
spacy/tokens/__pycache__/_serialize.cpython-311.pyc,,
spacy/tokens/__pycache__/underscore.cpython-311.pyc,,
spacy/tokens/_dict_proxies.py,sha256=AyswxWSVMSVetIaAWvQBu6QeJwOqh0tjDOoN6S7b32o,4625
spacy/tokens/_retokenize.cp311-win_amd64.pyd,sha256=zYXP54Ghj5luD6rtCBHYwJaN6001ipU_hbhkzvGjSDs,237568
spacy/tokens/_retokenize.cpp,sha256=wIEHp3vJCYHyrqmXE0vsks-qy8D4xz6lVWy91DJtkdc,1569489
spacy/tokens/_retokenize.pyi,sha256=NGyEtXVYw3I0sHrSfkcITiDnML5CPRKUdSavfPSRrkY,723
spacy/tokens/_retokenize.pyx,sha256=LOczLfGW8Of75dVkxBC61UzHxmqSwr2PDbsajuMxT64,20922
spacy/tokens/_serialize.py,sha256=WWRTNQ-622OpVbbRIsfT9ylda8iZT6AyKwdyWGdpoj0,12410
spacy/tokens/doc.cp311-win_amd64.pyd,sha256=or20D9hM0yMaNhJ-_FgfKNHN35QqFde6iZHgXxD3OM8,563712
spacy/tokens/doc.cpp,sha256=315BXhZxiv8eJiS8gcqKC6tbLRwo1HugIYaEcAxpIxk,3215859
spacy/tokens/doc.pxd,sha256=2qdjQOnRzoQDm4pAyxCaFFO1qw2J5Rm5_7lGy-Htv_4,1737
spacy/tokens/doc.pyi,sha256=3PwyZwxOimUhPL5ISnPlHWahWGnlSbPFBLNepWqqgxk,6110
spacy/tokens/doc.pyx,sha256=efiq2L4WluSTpBFu2PD0h5WBNu5sGnnKeH10lyPd1BU,85004
spacy/tokens/graph.cp311-win_amd64.pyd,sha256=EZNp4eLkTI-rdNyeeNFCHb5kKHfN-VGiSBRzvOmUmMI,194048
spacy/tokens/graph.cpp,sha256=mSlSqZRcZsAh7XxDIVCb7OCXGiLK93vVGWwPhGA_2-E,1112371
spacy/tokens/graph.pxd,sha256=VoQpzw39YNHeyGVRHYTK27g_2AtfSelLpOreT9G4oXw,308
spacy/tokens/graph.pyx,sha256=hsJo1XwlxGPHRNGnKqV7dOSXzZxPudaJ9lejrNbl2-U,23820
spacy/tokens/morphanalysis.cp311-win_amd64.pyd,sha256=-sV6Q0Tq43Rm0y8hLYO-n4ji0YFxRNI4BhL8EOGfr-A,59392
spacy/tokens/morphanalysis.cpp,sha256=c8zeZUgrUKQjL8D_cB8IyctvWSFG_YB_3G4el9TecdI,451993
spacy/tokens/morphanalysis.pxd,sha256=loPkGywg777sBNjIdFAVFk597qTdYb-PMooQ8obddto,218
spacy/tokens/morphanalysis.pyi,sha256=WUXfYFBafbphA8JL6v5Q0NdbM8nozcKkspUcTcUpRuc,880
spacy/tokens/morphanalysis.pyx,sha256=8DeH1hslqSY7jyRoFxHKLqjiPqWZOYF3KlSzbyJLZhU,3083
spacy/tokens/span.cp311-win_amd64.pyd,sha256=GZPUR_bbVARaZyYP27-y9a9T3BxcIZmP9XEGOgkTRhw,267776
spacy/tokens/span.cpp,sha256=ZnXmBK6mVqKrm33Te-nx_OlTpPBYAA3Bd_opR1f5zTc,1768333
spacy/tokens/span.pxd,sha256=dZccc4vUbysQCGBnA9IyeL6bpQF8du0y7j5iH7BBzbI,546
spacy/tokens/span.pyi,sha256=R3G5SZnl4Ak4_dga15-OZXWLj8XFoZRB7d2K6XzkOck,3867
spacy/tokens/span.pyx,sha256=jpx_OWObVTruicso9e1nbXvavTBA-7QcIJcuV_W2rWY,33813
spacy/tokens/span_group.cp311-win_amd64.pyd,sha256=GwB6eyYUPvFay1qZS3ICg6l4fD9cZNo19pm6NyAyZnQ,177152
spacy/tokens/span_group.cpp,sha256=lpL2RgvH8yUCRQDTNBikesbA_nISyExoiNZQs_IOio0,1242895
spacy/tokens/span_group.pxd,sha256=ywE8bb1JyV3mTjyY4anm1TvJ_cDRZ3XtINI53bUfvD8,254
spacy/tokens/span_group.pyi,sha256=jwMpZRO8i6A064dHfd6q-KNtbWFnnKnFi71eDueP9y4,863
spacy/tokens/span_group.pyx,sha256=8jp5aPxai_FsOee34-0gQMKPHrP6BueLz2wvQ5VMiZQ,11318
spacy/tokens/token.cp311-win_amd64.pyd,sha256=bvfesApgIDS2njh2gmJSmYm2-cL9cQ9h3jULiEXcc-E,225792
spacy/tokens/token.cpp,sha256=DJh6hgSvKBC27VNI8_Y-dQgM2TVnmLjRlN_ZdMLfa0M,1687890
spacy/tokens/token.pxd,sha256=oAchJwvMpHbPweYOE9xhtRpCaCO2xR8-r_es8NkTqA8,3526
spacy/tokens/token.pyi,sha256=vGuC01oHcpM4QBSSdk6lhIkpsW8BGlP7CAzchROe1fU,5606
spacy/tokens/token.pyx,sha256=EWIJuQOrRkmXmkcvJEeZEfNUL7ktJJD0WNKlsXJBgzE,34286
spacy/tokens/underscore.py,sha256=GkGJttWtNz1M7yodaEJhYUpEKFjpKVv0xKGtthFXgSw,5727
spacy/training/__init__.pxd,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/training/__init__.py,sha256=267HS5U8TQooqF4XhEgGUmGdnAuAgwcQtmoR44xSmI0,843
spacy/training/__pycache__/__init__.cpython-311.pyc,,
spacy/training/__pycache__/alignment.cpython-311.pyc,,
spacy/training/__pycache__/augment.cpython-311.pyc,,
spacy/training/__pycache__/batchers.cpython-311.pyc,,
spacy/training/__pycache__/callbacks.cpython-311.pyc,,
spacy/training/__pycache__/corpus.cpython-311.pyc,,
spacy/training/__pycache__/initialize.cpython-311.pyc,,
spacy/training/__pycache__/iob_utils.cpython-311.pyc,,
spacy/training/__pycache__/loggers.cpython-311.pyc,,
spacy/training/__pycache__/loop.cpython-311.pyc,,
spacy/training/__pycache__/pretrain.cpython-311.pyc,,
spacy/training/align.cp311-win_amd64.pyd,sha256=KBpGw-R5tnmVhaklLqbwWrVWorcf_US2ufWH_zyU_cM,58368
spacy/training/align.cpp,sha256=o3QW8GYFHyUKDzx9RDlEt0ybP8b0vWyd8LKdLb8yXrY,319045
spacy/training/align.pyx,sha256=qNeNb3vYHenQ3tsany4IK5xSWzXovqzIzgksYrqsa_4,3266
spacy/training/alignment.py,sha256=Tg0lHppzGB62AyputCVkowA64N2jfz9tfIFshP_b7ho,636
spacy/training/alignment_array.cp311-win_amd64.pyd,sha256=SUuGvelBIPigo7YwYyagvzxw89O3Ypmo1iZJXzANVN4,47104
spacy/training/alignment_array.cpp,sha256=18V7NRBsaurj92YCqnVSjV9xQiCuEpcl_rgQZuH_OIo,353987
spacy/training/alignment_array.pxd,sha256=WmPEViuYOXFrFKuuwegmD7YKfjnQh5-OrEeUJXEyHak,177
spacy/training/alignment_array.pyx,sha256=y51ms7r_Qxk1OC8-yNllgDJTyQTJLjq0LhEIGPQghoQ,2202
spacy/training/augment.py,sha256=aCYrwfBmvkpB8oh_d1jgBeZueh1LN4SxYMYvCqgFOrc,13628
spacy/training/batchers.py,sha256=E7D6YuDkIna3BPPdKtvZjnAYPvzs1rd5nW7wxqvWe9k,9342
spacy/training/callbacks.py,sha256=pwYTJ599yjpE_XKeMqNLcmj2IechyQE4N_VZAbY79ig,1301
spacy/training/converters/__init__.py,sha256=BGLhezviI-SZyaHn34BRP0mH0xtB4IqVhSSdosPAr-I,228
spacy/training/converters/__pycache__/__init__.cpython-311.pyc,,
spacy/training/converters/__pycache__/conll_ner_to_docs.cpython-311.pyc,,
spacy/training/converters/__pycache__/conllu_to_docs.cpython-311.pyc,,
spacy/training/converters/__pycache__/iob_to_docs.cpython-311.pyc,,
spacy/training/converters/__pycache__/json_to_docs.cpython-311.pyc,,
spacy/training/converters/conll_ner_to_docs.py,sha256=-CH-GY7xTBcP9iKjCN3Uu6X2NjilYfOMl0asi0xcrS8,6348
spacy/training/converters/conllu_to_docs.py,sha256=ygO_VI1Voc_WtP7Qlaw2jVkmoKtEHcY11s10RXnOLEA,10580
spacy/training/converters/iob_to_docs.py,sha256=XARbfdWSX2J4_LSk8ofT2vmhSUlI3jysrK-zYL6EfUA,2418
spacy/training/converters/json_to_docs.py,sha256=p0swwrGt9bMiJsBuTXzyLCJ2q6pjebhSOHGyoi2KKHA,903
spacy/training/corpus.py,sha256=D5T_Bfw1_Ueppz5WlpW1-L2OYAiKgL4k8ICbJnXdDFY,12321
spacy/training/example.cp311-win_amd64.pyd,sha256=NTLgKar9In9dhYD_QNuGkh7hO0AsxkSRNcIDMZ6NzAU,309760
spacy/training/example.cpp,sha256=1hLjbSbZh4sRtsHM5I1Uc4yxZRNFsJN1CASXnDJOJvw,1974715
spacy/training/example.pxd,sha256=WhsUubN7fPQvwx_grhdkLAVkaQySfkc_oAU6RbQqEO4,339
spacy/training/example.pyx,sha256=FUlkkqpvF5zREZubSWcPQXqtbcLgMcJLerO2ORv1txI,25457
spacy/training/gold_io.cp311-win_amd64.pyd,sha256=-h9utlfdmLFAtAqt9Dy1_7VOufZ-oeOmNq7OSiFZrbk,96768
spacy/training/gold_io.cpp,sha256=wgcjzSPZPsNxWAc9_JfcwZPUyTQES_LID2VhsL_sXXg,515484
spacy/training/gold_io.pyx,sha256=lU9OZTeHgPPxw9q_PmRzerTHr6MI81Lgw5lv1z71S-U,8235
spacy/training/initialize.py,sha256=58LvsuydmyqJaldm9TCx9sRKVPInsmWb2gpLSEN0VW8,14187
spacy/training/iob_utils.py,sha256=vgWSvzPtACVP5W1cEHxSbP0FZ1rXA649aPWGzjoAjCo,9315
spacy/training/loggers.py,sha256=poO0Y02tRZMOC_rkhkUXvErk07Cq_BoDouyytwmxqXo,8017
spacy/training/loop.py,sha256=ZOnBCNXuKH0wVRlftgvSRPvtRCfffL3c24A_Zsf6cYw,15376
spacy/training/pretrain.py,sha256=SPmtLdIxT3-LmfIq_JfSvpXs-TLSSTguzmrXZI_C8-E,9956
spacy/ty.py,sha256=TJycLKrFn0xIAC7PSuh6Wo7AuvEJh9pFPoze64lb0-k,1354
spacy/typedefs.pxd,sha256=C8BcxWbWqeCanDaMNOdVP5BOhdc4ojUOXpqLQ4o4CDQ,294
spacy/typedefs.pyx,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/util.py,sha256=cs9hyjJj06tgPMtHu2Nt_rI7MRrlsEbg1oVtiZi1UiI,69107
spacy/vectors.cp311-win_amd64.pyd,sha256=crA8UbbbsROh2C3RncZ0v3IQVc0ckqEVOaPz0W8ryys,237568
spacy/vectors.cpp,sha256=D8Nl2uT2LlBrflDTbwcIHZuZJ1V58nqncUu9XFFZ_qk,1202771
spacy/vectors.pyx,sha256=xD6NbDpIVsk7KLk0m-PLlQvDXq47whg-IaJekV3pq_0,26636
spacy/vocab.cp311-win_amd64.pyd,sha256=R9dnDFtMgpe6yRZ4TqoGsO82OjgZfoRv9X23BLD4PWk,288768
spacy/vocab.cpp,sha256=eY2095Z7vc_sHaM1Jkq9Xo21BKfeBnmjsL8LWGqSOiA,1758196
spacy/vocab.pxd,sha256=ZaPUZiIGJEB1DZqj2IF-mGF7wsaaPR4_2mlTuaLGEuo,1421
spacy/vocab.pyi,sha256=h6o_5aDXWpXYQOx0sI7l8qq7YNGUrvujw2LXdZ11YRo,2831
spacy/vocab.pyx,sha256=_6x2qAZBmyfrlSvtfYr_JAXf92USO-C5sQO-YC8w1UI,23952
