"""Helpers for Python and platform compatibility."""
import sys
from thinc.util import copy_array

try:
    import cPickle as pickle
except ImportError:
    import pickle  # type: ignore[no-redef]

try:
    import copy_reg
except ImportError:
    import copyreg as copy_reg  # type: ignore[no-redef]

try:
    from cupy.cuda.stream import Stream as CudaStream
except ImportError:
    CudaStream = None

try:
    import cupy
except ImportError:
    cupy = None

if sys.version_info[:2] >= (3, 8):  # Python 3.8+
    from typing import Literal, Protocol, runtime_checkable
else:
    from typing_extensions import Literal, Protocol, runtime_checkable  # noqa: F401

# Important note: The importlib_metadata "backport" includes functionality
# that's not part of the built-in importlib.metadata. We should treat this
# import like the built-in and only use what's available there.
try:  # Python 3.8+
    import importlib.metadata as importlib_metadata
except ImportError:
    from catalogue import _importlib_metadata as importlib_metadata  # type: ignore[no-redef]    # noqa: F401

from thinc.api import Optimizer  # noqa: F401

pickle = pickle
copy_reg = copy_reg
CudaStream = CudaStream
cupy = cupy
copy_array = copy_array

is_windows = sys.platform.startswith("win")
is_linux = sys.platform.startswith("linux")
is_osx = sys.platform == "darwin"
