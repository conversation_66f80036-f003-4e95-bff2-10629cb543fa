import pandas as pd

try:
    # Read the Excel file
    df = pd.read_excel('Masterbrands.xlsx')
    
    print('=== EXCEL FILE ANALYSIS ===')
    print(f'Shape: {df.shape}')
    print(f'Columns: {list(df.columns)}')
    print()
    
    print('=== COLUMN DETAILS ===')
    for col in df.columns:
        print(f'{col}:')
        print(f'  - Data type: {df[col].dtype}')
        print(f'  - Non-null count: {df[col].count()}/{len(df)}')
        if df[col].dtype == 'object':  # String columns
            sample_values = df[col].dropna().head(3).tolist()
            print(f'  - Sample values: {sample_values}')
        print()
    
    print('=== FIRST 3 ROWS ===')
    print(df.head(3).to_string())
    
except Exception as e:
    print(f'Error reading Excel file: {e}')
