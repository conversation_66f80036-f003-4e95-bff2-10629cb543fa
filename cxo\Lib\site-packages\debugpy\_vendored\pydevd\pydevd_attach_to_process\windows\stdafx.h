/* ****************************************************************************
 *
 * Copyright (c) Microsoft Corporation.
 *
 * This source code is subject to terms and conditions of the Apache License, Version 2.0. A
 * copy of the license can be found in the License.html file at the root of this distribution. If
 * you cannot locate the Apache License, Version 2.0, please send an email to
 * <EMAIL>. By using this source code in any fashion, you are agreeing to be bound
 * by the terms of the Apache License, Version 2.0.
 *
 * You must not remove this notice, or any other, from this software.
 *
 * ***************************************************************************/

// stdafx.h : include file for standard system include files,
// or project specific include files that are used frequently, but
// are changed infrequently
//

#pragma once

#include "targetver.h"

#include <cstdint>
#include <fstream>
#include <string>
#include <unordered_set>
#include <unordered_map>

#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <psapi.h>
#include <strsafe.h>
#include <tlhelp32.h>
#include <winsock.h>
#include <winternl.h>
